"""
Progress Tracking Integration Example
====================================

This example demonstrates how to integrate comprehensive progress tracking
into your application's long-running operations.
"""

import asyncio
import flet as ft
from services.simple_progress_service import SimpleProgressService
from services.financial_service import FinancialModelService
from services.ml_prediction_service import MLPredictionService
from services.export_service import ExportService
from models.project_assumptions import EnhancedProjectAssumptions
from models.client_profile import ClientProfile


class ProgressTrackingExample:
    """Example application demonstrating progress tracking."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.progress_service = SimpleProgressService(page)
        self.financial_service = FinancialModelService()
        self.ml_service = MLPredictionService()
        self.export_service = ExportService()
        
        # Setup UI
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the example UI."""
        self.page.title = "Progress Tracking Example"
        self.page.scroll = ft.ScrollMode.AUTO
        
        # Operation buttons
        self.financial_btn = ft.ElevatedButton(
            "Run Financial Analysis",
            on_click=self.run_financial_analysis,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.BLUE
            )
        )
        
        self.ml_training_btn = ft.ElevatedButton(
            "Train ML Models",
            on_click=self.train_ml_models,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.GREEN
            )
        )
        
        self.monte_carlo_btn = ft.ElevatedButton(
            "Run Monte Carlo Simulation",
            on_click=self.run_monte_carlo,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.ORANGE
            )
        )
        
        self.export_btn = ft.ElevatedButton(
            "Export Reports",
            on_click=self.export_reports,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.PURPLE
            )
        )
        
        # Status display
        self.status_text = ft.Text(
            "Ready to run operations...",
            size=16,
            color=ft.Colors.GREEN
        )
        
        # Layout
        self.page.add(
            ft.Container(
                content=ft.Column([
                    ft.Text("Progress Tracking Example", size=24, weight=ft.FontWeight.BOLD),
                    ft.Container(height=20),
                    
                    # Operation buttons
                    ft.Row([
                        self.financial_btn,
                        self.ml_training_btn,
                        self.monte_carlo_btn,
                        self.export_btn
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    
                    ft.Container(height=20),
                    
                    # Status
                    self.status_text
                    
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=20
            )
        )
    
    
    async def run_financial_analysis(self, e):
        """Run financial analysis with progress tracking."""
        try:
            # Start operation
            await self.progress_service.start_operation("Financial Analysis")
            
            # Create dummy assumptions
            assumptions = self.create_dummy_assumptions()
            
            # Simulate progress updates
            await self.progress_service.update_progress(10, "Initializing financial model...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(30, "Processing project assumptions...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(60, "Calculating cash flows...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(80, "Computing financial metrics...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(95, "Finalizing results...")
            await asyncio.sleep(0.5)
            
            # Complete operation
            await self.progress_service.complete_operation("Financial analysis completed successfully!")
            
            self.status_text.value = "Financial analysis completed successfully!"
            self.page.update()  # Use synchronous update

        except Exception as ex:
            await self.progress_service.complete_operation(f"Financial analysis failed: {str(ex)}")
            self.status_text.value = f"Financial analysis failed: {str(ex)}"
            self.page.update()  # Use synchronous update
    
    async def train_ml_models(self, e):
        """Train ML models with progress tracking."""
        try:
            # Start operation
            await self.progress_service.start_operation("ML Model Training")
            
            # Simulate progress updates
            await self.progress_service.update_progress(5, "Loading training data...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(20, "Preprocessing features...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(40, "Training regression model...")
            await asyncio.sleep(1.0)
            
            await self.progress_service.update_progress(65, "Training classification model...")
            await asyncio.sleep(1.0)
            
            await self.progress_service.update_progress(85, "Validating models...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(95, "Saving trained models...")
            await asyncio.sleep(0.5)
            
            # Complete operation
            await self.progress_service.complete_operation("ML model training completed successfully!")
            
            self.status_text.value = "ML model training completed successfully!"
            self.page.update()  # Use synchronous update

        except Exception as ex:
            await self.progress_service.complete_operation(f"ML training failed: {str(ex)}")
            self.status_text.value = f"ML training failed: {str(ex)}"
            self.page.update()  # Use synchronous update
    
    async def run_monte_carlo(self, e):
        """Run Monte Carlo simulation with progress tracking."""
        try:
            # Start operation
            await self.progress_service.start_operation("Monte Carlo Simulation")
            
            # Create dummy assumptions
            assumptions = self.create_dummy_assumptions()
            
            # Simulate progress updates for 1000 simulations
            await self.progress_service.update_progress(5, "Initializing simulation parameters...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(15, "Setting up random variables...")
            await asyncio.sleep(0.5)
            
            # Simulate running simulations
            for i in range(10):
                progress = 15 + (i + 1) * 7  # Progress from 15% to 85%
                simulations_done = (i + 1) * 100
                await self.progress_service.update_progress(
                    progress, 
                    f"Running simulations... ({simulations_done}/1000 completed)"
                )
                await asyncio.sleep(0.3)
            
            await self.progress_service.update_progress(90, "Analyzing results...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(95, "Generating statistics...")
            await asyncio.sleep(0.5)
            
            # Complete operation
            await self.progress_service.complete_operation("Monte Carlo simulation completed successfully!")
            
            self.status_text.value = "Monte Carlo simulation completed successfully!"
            self.page.update()  # Use synchronous update

        except Exception as ex:
            await self.progress_service.complete_operation(f"Monte Carlo simulation failed: {str(ex)}")
            self.status_text.value = f"Monte Carlo simulation failed: {str(ex)}"
            self.page.update()  # Use synchronous update
    
    async def export_reports(self, e):
        """Export reports with progress tracking."""
        try:
            # Start operation
            await self.progress_service.start_operation("Export Reports")
            
            # Create dummy data
            client_profile = ClientProfile(
                company_name="Example Company",
                contact_person="John Doe",
                email="<EMAIL>"
            )
            
            assumptions = self.create_dummy_assumptions()
            
            # Simulate financial results
            financial_results = {
                'kpis': {
                    'IRR_equity': 0.15,
                    'IRR_project': 0.12,
                    'NPV_equity': 1500000,
                    'LCOE_eur_kwh': 0.045
                },
                'cashflow': {}
            }
            
            # Simulate progress updates
            await self.progress_service.update_progress(10, "Preparing export data...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(25, "Creating Excel workbook...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(40, "Writing financial data...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(60, "Formatting worksheets...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(80, "Adding charts and graphs...")
            await asyncio.sleep(0.5)
            
            await self.progress_service.update_progress(95, "Saving file...")
            await asyncio.sleep(0.5)
            
            # Complete operation
            await self.progress_service.complete_operation("Reports exported successfully!")
            
            self.status_text.value = "Reports exported successfully!"
            self.page.update()  # Use synchronous update

        except Exception as ex:
            await self.progress_service.complete_operation(f"Export operation failed: {str(ex)}")
            self.status_text.value = f"Export operation failed: {str(ex)}"
            self.page.update()  # Use synchronous update
    
    def create_dummy_assumptions(self) -> EnhancedProjectAssumptions:
        """Create dummy project assumptions for testing."""
        return EnhancedProjectAssumptions(
            capacity_mw=10.0,
            production_mwh_year1=18000,
            project_life_years=25,
            capex_meur=8.5,
            opex_keuros_year1=180,
            ppa_price_eur_kwh=0.045,
            debt_ratio=0.75,
            interest_rate=0.06,
            discount_rate=0.08,
            tax_rate=0.15,
            degradation_rate=0.005,
            land_lease_eur_mw_year=1500,
            location_name="Morocco - Ouarzazate"
        )


async def main(page: ft.Page):
    """Main application entry point."""
    # Create example app
    app = ProgressTrackingExample(page)
    
    # Keep the app running
    await asyncio.sleep(0.1)  # Allow UI to initialize


if __name__ == "__main__":
    ft.app(target=main, view=ft.AppView.WEB_BROWSER)
