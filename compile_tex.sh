#!/bin/bash

# LaTeX Compiler Script with Non-Stop Mode and Custom Output
# Usage: ./compile_tex.sh [OPTIONS] [compiler]
# Options:
#   -o, --output NAME    Custom output PDF name (without .pdf extension)
#   -h, --help          Show help message
# compiler: pdflatex (default) or xelatex

# Default settings
COMPILER="pdflatex"
CUSTOM_OUTPUT=""
SHOW_GUI_LOGS="false"

# Function to show help
show_help() {
    echo "LaTeX Compiler Script"
    echo "Usage: $0 [OPTIONS] [compiler]"
    echo ""
    echo "Options:"
    echo "  -o, --output NAME    Custom output PDF name (without .pdf extension)"
    echo "  -g, --gui-logs      Show compilation logs in GUI window"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "Compilers:"
    echo "  pdflatex            Default LaTeX compiler"
    echo "  xelatex             Extended LaTeX compiler with Unicode support"
    echo ""
    echo "Examples:"
    echo "  $0                           # Compile with pdflatex (default)"
    echo "  $0 -g                        # Compile with GUI log viewer"
    echo "  $0 xelatex                   # Compile with xelatex"
    echo "  $0 -o mydoc                  # Compile with custom output name 'mydoc.pdf'"
    echo "  $0 -g -o mydoc xelatex       # GUI logs + custom output + xelatex"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -o|--output)
            CUSTOM_OUTPUT="$2"
            shift 2
            ;;
        -g|--gui-logs)
            SHOW_GUI_LOGS="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        pdflatex|xelatex)
            COMPILER="$1"
            shift
            ;;
        *)
            echo "Error: Unknown option '$1'"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "Using compiler: $COMPILER"
if [ -n "$CUSTOM_OUTPUT" ]; then
    echo "Custom output name: $CUSTOM_OUTPUT.pdf"
fi
echo "Non-stop mode: enabled"
if [ "$SHOW_GUI_LOGS" = "true" ]; then
    echo "GUI log viewer: enabled"
fi
echo "==========================================="

# Check if the compiler is available
if ! command -v $COMPILER &> /dev/null; then
    echo "Error: $COMPILER is not installed or not in PATH"
    echo "Please install LaTeX with the required compiler"
    exit 1
fi

# Check if zenity is available for GUI logs
if [ "$SHOW_GUI_LOGS" = "true" ] && ! command -v zenity &> /dev/null; then
    echo "Warning: zenity not found. GUI logs disabled, falling back to terminal."
    SHOW_GUI_LOGS="false"
fi

# Count .tex files
tex_count=0
for file in *.tex; do
    if [ -f "$file" ]; then
        ((tex_count++))
    fi
done

if [ $tex_count -eq 0 ]; then
    echo "No .tex files found in current directory"
    exit 0
fi

echo "Found $tex_count .tex file(s) to compile"
echo "==========================================="

# Function to show logs in GUI
show_gui_logs() {
    local log_file="$1"
    local title="$2"
    
    if [ -f "$log_file" ]; then
        zenity --text-info \
               --title="$title" \
               --width=800 \
               --height=600 \
               --filename="$log_file" \
               --checkbox="Keep log file" 2>/dev/null
        
        # If user didn't check "keep log file", remove it
        if [ $? -ne 0 ]; then
            rm -f "$log_file" 2>/dev/null
        fi
    fi
}

# Compile all .tex files
for file in *.tex; do
    if [ -f "$file" ]; then
        basename=$(basename "$file" .tex)
        
        if [ -n "$CUSTOM_OUTPUT" ]; then
            # Use custom output name
            output_name="$CUSTOM_OUTPUT"
        else
            # Use original filename
            output_name="$basename"
        fi
        
        echo "Compiling: $file → $output_name.pdf"
        
        # Run compiler with non-stop mode and custom output
        if $COMPILER -interaction=nonstopmode -jobname="$output_name" "$file"; then
            echo "✓ Successfully compiled: $file → $output_name.pdf"
            
            # Show success logs in GUI if enabled
            if [ "$SHOW_GUI_LOGS" = "true" ] && [ -f "$output_name.log" ]; then
                show_gui_logs "$output_name.log" "✓ Success: $file → $output_name.pdf"
            fi
            
            # Clean up auxiliary files (but keep log if GUI viewer was shown)
            if [ "$SHOW_GUI_LOGS" != "true" ]; then
                rm -f "$output_name.aux" "$output_name.log" "$output_name.out" "$output_name.toc" "$output_name.fls" "$output_name.fdb_latexmk" 2>/dev/null
            else
                rm -f "$output_name.aux" "$output_name.out" "$output_name.toc" "$output_name.fls" "$output_name.fdb_latexmk" 2>/dev/null
            fi
        else
            echo "✗ Error compiling: $file"
            echo "  Check $output_name.log for details"
            
            # Show error logs in GUI if enabled
            if [ "$SHOW_GUI_LOGS" = "true" ] && [ -f "$output_name.log" ]; then
                show_gui_logs "$output_name.log" "✗ Error: $file compilation failed"
            fi
        fi
        echo "---"
    fi
done

echo "==========================================="
echo "Compilation finished using $COMPILER"
if [ "$SHOW_GUI_LOGS" != "true" ]; then
    echo "All auxiliary files cleaned up"
else
    echo "Log files preserved for GUI viewing"
fi
