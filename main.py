"""
Hiel RnE Modeler v3.0 Application
=================================

Main entry point for the Hiel RnE financial modeling application.

Author: Abdelhalim Serhani
Company: Agevolami SRL
Website: www.agevolami.it & www.agevolami.ma
Tagline: Your way to explore crossborder opportunities and grow big
"""

import flet as ft
import logging
import logging.handlers
import sys
import os
import json
import uuid
import platform
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.app_controller import AppController
from config.app_config import AppConfig
from config.ui_config import UIConfig
from services.error_handler import setup_global_error_handling, global_error_handler

# Try to import logging utilities (will be available once utils/logging_utils.py is created)
try:
    from utils.logging_utils import (
        CorrelationIDGenerator,
        LoggerFactory,
        StructuredLogFormatter,
        CorrelationIDManager
    )
    LOGGING_UTILS_AVAILABLE = True
except ImportError:
    # Fallback implementations for when utils/logging_utils.py doesn't exist yet
    LOGGING_UTILS_AVAILABLE = False

    class CorrelationIDGenerator:
        """Fallback correlation ID generator."""
        @staticmethod
        def generate():
            return str(uuid.uuid4())

    class StructuredLogFormatter(logging.Formatter):
        """Fallback JSON formatter."""
        def __init__(self, include_correlation_id=True, json_format=False):
            self.include_correlation_id = include_correlation_id
            self.json_format = json_format
            if json_format:
                super().__init__()
            else:
                fmt = '%(asctime)s - %(name)s - %(levelname)s'
                if include_correlation_id:
                    fmt += ' - [%(correlation_id)s]'
                fmt += ' - %(message)s'
                super().__init__(fmt)

        def format(self, record):
            if self.include_correlation_id:
                record.correlation_id = getattr(record, 'correlation_id', 'N/A')

            if self.json_format:
                log_data = {
                    'timestamp': self.formatTime(record, self.datefmt),
                    'level': record.levelname,
                    'logger': record.name,
                    'message': record.getMessage(),
                    'module': record.module,
                    'function': record.funcName,
                    'line': record.lineno
                }

                # Add correlation ID if present
                if hasattr(record, 'correlation_id'):
                    log_data['correlation_id'] = record.correlation_id

                # Add extra fields
                if hasattr(record, 'extra_data'):
                    log_data.update(record.extra_data)

                return json.dumps(log_data, default=str)
            else:
                return super().format(record)

    class CorrelationIDFilter(logging.Filter):
        """Fallback correlation ID filter."""
        def __init__(self):
            super().__init__()
            self.correlation_id = None

        def set_correlation_id(self, correlation_id):
            self.correlation_id = correlation_id

        def filter(self, record):
            if self.correlation_id:
                record.correlation_id = self.correlation_id
            return True

    class CorrelationIDManager:
        """Fallback correlation ID manager."""
        _correlation_id = None

        @classmethod
        def set_correlation_id(cls, correlation_id):
            cls._correlation_id = correlation_id

        @classmethod
        def get_correlation_id(cls):
            return cls._correlation_id

        @classmethod
        def clear_correlation_id(cls):
            cls._correlation_id = None

    class LoggerFactory:
        """Fallback logger factory."""
        @staticmethod
        def get_logger(name, include_correlation_id=True, json_format=False):
            return logging.LoggerAdapter(logging.getLogger(name), {'correlation_id': 'N/A'})


def setup_logging():
    """Setup application logging with correlation ID support and structured logging."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Get log level from environment variable, default to INFO
    log_level_str = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_level = getattr(logging, log_level_str, logging.INFO)
    
    # Check if JSON formatting is requested
    use_json_format = os.getenv('LOG_FORMAT', 'standard').lower() == 'json'

    # Create formatters
    if use_json_format:
        formatter = StructuredLogFormatter(include_correlation_id=True, json_format=True)
        console_formatter = StructuredLogFormatter(include_correlation_id=True, json_format=True)
    else:
        # Enhanced standard format with correlation ID support
        formatter = StructuredLogFormatter(include_correlation_id=True, json_format=False)
        console_formatter = StructuredLogFormatter(include_correlation_id=True, json_format=False)
    
    # Setup file handler with rotation
    max_bytes = int(os.getenv('LOG_MAX_BYTES', '10485760'))  # 10MB default
    backup_count = int(os.getenv('LOG_BACKUP_COUNT', '5'))

    file_handler = logging.handlers.RotatingFileHandler(
        log_dir / "app.log",
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)

    # Setup console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(console_formatter)

    # Setup error file handler for errors and above
    error_handler = logging.handlers.RotatingFileHandler(
        log_dir / "error.log",
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)

    # Create correlation ID filter only for fallback mode
    if not LOGGING_UTILS_AVAILABLE:
        correlation_filter = CorrelationIDFilter()
        file_handler.addFilter(correlation_filter)
        console_handler.addFilter(correlation_filter)
        error_handler.addFilter(correlation_filter)
        # Store correlation filter globally for access by other modules
        global _correlation_filter
        _correlation_filter = correlation_filter
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Add handlers
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(error_handler)
    
    # Log setup completion
    logger = logging.getLogger(__name__)
    logger.info("Logging system initialized")
    logger.info(f"Log level: {log_level_str}")
    logger.info(f"Log format: {'JSON' if use_json_format else 'Standard'}")
    logger.info(f"Log directory: {log_dir.absolute()}")
    logger.info(f"Logging utilities available: {LOGGING_UTILS_AVAILABLE}")


def set_correlation_id(correlation_id):
    """Set correlation ID for current logging context."""
    if LOGGING_UTILS_AVAILABLE:
        CorrelationIDManager.set_correlation_id(correlation_id)
    else:
        global _correlation_filter
        if '_correlation_filter' in globals():
            _correlation_filter.set_correlation_id(correlation_id)


def get_system_info():
    """Get system information for startup logging."""
    return {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'architecture': platform.architecture()[0],
        'processor': platform.processor(),
        'hostname': platform.node(),
        'user': os.getenv('USER', os.getenv('USERNAME', 'unknown'))
    }


def log_startup_info():
    """Log comprehensive startup information."""
    logger = logging.getLogger(__name__)
    startup_correlation_id = CorrelationIDGenerator.generate()
    set_correlation_id(startup_correlation_id)
    
    # Log application startup
    logger.info("=" * 80)
    logger.info("Hiel RnE Modeler v3.0 - 2025 Edition - STARTUP")
    logger.info("=" * 80)
    logger.info("Author: Abdelhalim Serhani")
    logger.info("Company: Agevolami SRL")
    logger.info("Website: www.agevolami.it & www.agevolami.ma")
    logger.info("Tagline: Your way to explore crossborder opportunities and grow big")
    logger.info("Enhanced with comprehensive error handling and DCF modeling")
    
    # Log system information
    system_info = get_system_info()
    logger.info("System Information:")
    for key, value in system_info.items():
        logger.info(f"  {key}: {value}")
    
    # Log Piano Mattei/SIMEST unification status
    logger.info("Feature Status:")
    logger.info("  Piano Mattei/SIMEST Grant Unification: ACTIVE")
    logger.info("  Enhanced Analysis Pipeline: ENABLED")
    logger.info("  Correlation ID Tracking: ENABLED")
    logger.info("  Structured Logging: ENABLED")
    
    # Log configuration
    logger.info("Configuration:")
    logger.info(f"  Log Level: {os.getenv('LOG_LEVEL', 'INFO')}")
    logger.info(f"  Log Format: {os.getenv('LOG_FORMAT', 'standard')}")
    logger.info(f"  Max Log Size: {os.getenv('LOG_MAX_BYTES', '10485760')} bytes")
    logger.info(f"  Log Backup Count: {os.getenv('LOG_BACKUP_COUNT', '5')}")
    
    logger.info("=" * 80)
    
    return startup_correlation_id


def create_security_screen(page: ft.Page, on_authenticated: callable):
    """Create modern security/authentication screen with 2025 design trends."""

    # Animation state
    login_in_progress = ft.Ref[bool]()
    login_in_progress.current = False

    def on_password_submit(e):
        password = password_field.value.strip()

        if not password:
            show_error("Please enter a password")
            return

        # Set loading state with modern animation
        set_loading_state(True)

        # Updated password for 2025
        if password == "agevolami2025":
            # Success animation
            success_animation()
            # Delay for animation then authenticate
            page.run_task(delayed_authenticate)
        else:
            # Error animation
            error_animation()
            set_loading_state(False)

    async def delayed_authenticate():
        """Delayed authentication for smooth animation."""
        import asyncio
        await asyncio.sleep(0.8)  # Allow success animation to complete
        on_authenticated()

    def on_password_change(e):
        error_text.visible = False
        error_text.update()

    def show_error(message: str):
        """Show error with modern styling."""
        error_text.value = message
        error_text.visible = True
        error_text.color = ft.Colors.RED_400
        error_text.update()

    def set_loading_state(loading: bool):
        """Set loading state with visual feedback."""
        login_in_progress.current = loading
        login_button.disabled = loading
        if loading:
            login_button.text = "Authenticating..."
            login_button.icon = ft.Icons.HOURGLASS_EMPTY
            progress_ring.visible = True
        else:
            login_button.text = "Access Application"
            login_button.icon = ft.Icons.LOGIN
            progress_ring.visible = False
        login_button.update()
        progress_ring.update()

    def success_animation():
        """Success animation with green checkmark."""
        password_field.bgcolor = ft.Colors.GREEN_50
        password_field.border_color = ft.Colors.GREEN_400
        password_field.update()

        success_icon.visible = True
        success_icon.update()

    def error_animation():
        """Error animation with red highlight."""
        password_field.bgcolor = ft.Colors.RED_50
        password_field.border_color = ft.Colors.RED_400
        password_field.update()

        # Reset after animation
        page.run_task(reset_field_styling)

    async def reset_field_styling():
        """Reset field styling after error."""
        import asyncio
        await asyncio.sleep(1)
        password_field.bgcolor = ft.Colors.WHITE
        password_field.border_color = ft.Colors.GREY_400
        password_field.update()

    # Modern password field with enhanced styling
    password_field = ft.TextField(
        label="Enter Access Password",
        password=True,
        autofocus=True,
        on_submit=on_password_submit,
        on_change=on_password_change,
        width=350,
        height=60,
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border_color=ft.Colors.GREY_400,
        focused_border_color=ft.Colors.BLUE_600,
        cursor_color=ft.Colors.BLUE_600,
        text_style=ft.TextStyle(size=16),
        label_style=ft.TextStyle(color=ft.Colors.GREY_600)
    )

    # Modern error text
    error_text = ft.Text(
        "Invalid password. Please try again.",
        color=ft.Colors.RED_400,
        visible=False,
        size=14,
        weight=ft.FontWeight.W_500
    )

    # Success icon
    success_icon = ft.Icon(
        ft.Icons.CHECK_CIRCLE,
        color=ft.Colors.GREEN_400,
        size=24,
        visible=False
    )

    # Progress ring
    progress_ring = ft.ProgressRing(
        width=20,
        height=20,
        stroke_width=2,
        visible=False
    )

    # Modern login button with gradient effect
    login_button = ft.ElevatedButton(
        "Access Application",
        icon=ft.Icons.LOGIN,
        on_click=on_password_submit,
        width=350,
        height=56,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.W_600),
            shape=ft.RoundedRectangleBorder(radius=12),
            elevation=3,
            shadow_color=ft.Colors.BLUE_200,
        )
    )
    
    # LEFT SIDE: Professional Company Branding & Information
    left_side_content = ft.Container(
        content=ft.Column([
            # Company Logo/Header
            ft.Container(
                content=ft.Column([
                    ft.Text("🚀 Hiel RnE Modeler",
                           size=36, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.WHITE),
                    ft.Text("v3.0 • 2025 Edition",
                           size=18, color=ft.Colors.BLUE_100,
                           weight=ft.FontWeight.W_500),
                ], spacing=8),
                margin=ft.margin.only(bottom=40)
            ),

            # Value Proposition
            ft.Container(
                content=ft.Column([
                    ft.Text("Advanced Renewable Energy",
                           size=24, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.WHITE),
                    ft.Text("Financial Analysis Platform",
                           size=24, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.WHITE),
                ], spacing=5),
                margin=ft.margin.only(bottom=30)
            ),

            # Key Features
            ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.ANALYTICS, color=ft.Colors.BLUE_200, size=24),
                    ft.Text("Comprehensive Financial Modeling",
                           size=16, color=ft.Colors.BLUE_100)
                ], spacing=15),
                ft.Row([
                    ft.Icon(ft.Icons.LOCATION_ON, color=ft.Colors.BLUE_200, size=24),
                    ft.Text("Multi-Location Comparison",
                           size=16, color=ft.Colors.BLUE_100)
                ], spacing=15),
                ft.Row([
                    ft.Icon(ft.Icons.TRENDING_UP, color=ft.Colors.BLUE_200, size=24),
                    ft.Text("Monte Carlo Risk Analysis",
                           size=16, color=ft.Colors.BLUE_100)
                ], spacing=15),
                ft.Row([
                    ft.Icon(ft.Icons.ASSESSMENT, color=ft.Colors.BLUE_200, size=24),
                    ft.Text("Professional Reports & Charts",
                           size=16, color=ft.Colors.BLUE_100)
                ], spacing=15),
            ], spacing=20),

            # Spacer for pushing company info to bottom
            ft.Container(expand=True),

            # Company Info at bottom
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.ACCOUNT_CIRCLE, color=ft.Colors.BLUE_200, size=20),
                        ft.Text("Abdelhalim Serhani", size=16, color=ft.Colors.BLUE_100, weight=ft.FontWeight.BOLD)
                    ], spacing=10, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Text("Business Consultant and Analyst", size=14, color=ft.Colors.BLUE_200),
                    ft.Container(height=10),
                    ft.Row([
                        ft.Icon(ft.Icons.BUSINESS_CENTER, color=ft.Colors.BLUE_200, size=20),
                        ft.Text("Agevolami SRL", size=16, color=ft.Colors.BLUE_100, weight=ft.FontWeight.BOLD)
                    ], spacing=10, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Text("Your gateway to cross-border opportunities",
                           size=14, color=ft.Colors.BLUE_200, italic=True),
                    ft.Container(height=10),
                    ft.Row([
                        ft.Text("www.agevolami.it", size=12, color=ft.Colors.BLUE_200),
                        ft.Text("•", size=12, color=ft.Colors.BLUE_200),
                        ft.Text("www.agevolami.ma", size=12, color=ft.Colors.BLUE_200),
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER)
                ], spacing=5, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=20,
                bgcolor=ft.Colors.BLUE_800,
                border_radius=12,
                opacity=0.9
            )
        ], spacing=0),
        padding=50,
        expand=True,
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_700, ft.Colors.BLUE_900]
        )
    )

    # RIGHT SIDE: Clean, Focused Login Form
    right_side_content = ft.Container(
        content=ft.Column([
            # Spacer for vertical centering
            ft.Container(expand=True),

            # Login Card
            ft.Container(
                content=ft.Column([
                    # Security Icon
                    ft.Container(
                        content=ft.Icon(ft.Icons.SHIELD, size=48, color=ft.Colors.BLUE_600),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=30,
                        padding=15,
                        margin=ft.margin.only(bottom=25)
                    ),

                    # Title
                    ft.Text("Secure Access Portal",
                           size=28, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.GREY_800,
                           text_align=ft.TextAlign.CENTER),

                    # Subtitle
                    ft.Text("Enter your credentials to access the platform",
                           size=16, color=ft.Colors.GREY_600,
                           text_align=ft.TextAlign.CENTER,
                           weight=ft.FontWeight.W_400),

                    ft.Container(height=35),

                    # Password Field with Success Icon
                    ft.Stack([
                        password_field,
                        ft.Container(
                            content=success_icon,
                            right=15,
                            top=18
                        )
                    ]),

                    # Error Text
                    ft.Container(
                        content=error_text,
                        height=25,
                        alignment=ft.alignment.center_left
                    ),

                    # Login Button with Progress
                    ft.Stack([
                        login_button,
                        ft.Container(
                            content=progress_ring,
                            right=20,
                            top=18
                        )
                    ]),

                    # Security Note
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.SECURITY, color=ft.Colors.GREEN_600, size=16),
                            ft.Text("Secure SSL Encryption", size=12, color=ft.Colors.GREEN_600)
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=8),
                        margin=ft.margin.only(top=25)
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=40,
                bgcolor=ft.Colors.WHITE,
                border_radius=20,
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=30,
                    color=ft.Colors.GREY_300,
                    offset=ft.Offset(0, 10)
                ),
                width=450
            ),

            # Spacer for vertical centering
            ft.Container(expand=True),
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=50,
        expand=True,
        bgcolor=ft.Colors.GREY_50
    )

    # MAIN LAYOUT: Professional Left-Right Split Design
    professional_layout = ft.ResponsiveRow([
        ft.Container(
            content=left_side_content,
            col={"sm": 12, "md": 6, "lg": 6, "xl": 6}
        ),
        ft.Container(
            content=right_side_content,
            col={"sm": 12, "md": 6, "lg": 6, "xl": 6}
        )
    ], expand=True)

    page.add(professional_layout)


def main(page: ft.Page):
    """Main application function."""
    
    # Load configuration
    app_config = AppConfig()
    ui_config = UIConfig()
    
    # Configure page
    page.title = f"{app_config.app_name} - {app_config.company_name}"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1400
    page.window_height = 900
    page.window_min_width = 1200
    page.window_min_height = 800
    page.padding = 0

    # Set window icon
    try:
        # Try .ico first (preferred for Windows), then .png as fallback
        if os.path.exists("assets/logo.ico"):
            page.window_icon = "assets/logo.ico"
        elif os.path.exists("assets/Hiel RnE Logo.png"):
            page.window_icon = "assets/Hiel RnE Logo.png"
    except Exception as e:
        logging.warning(f"Could not set window icon: {e}")
    
    def start_main_application():
        """Start the main application after authentication."""
        try:
            page.clean()

            # Initialize and start the main application controller
            app_controller = AppController(page)

            logging.info("Hiel RnE Modeler v3.0 Application started successfully")
        except Exception as e:
            global_error_handler.handle_error(e,
                context={'function': 'start_main_application'},
                show_user_message=True,
                page=page)
            logging.error(f"Failed to start main application: {str(e)}")
    
    # Show security screen first
    create_security_screen(page, start_main_application)


if __name__ == "__main__":
    # Setup logging and error handling
    setup_logging()
    setup_global_error_handling()

    # Log comprehensive startup information with correlation ID
    startup_correlation_id = log_startup_info()

    try:
        # Start the Flet application
        logger = logging.getLogger(__name__)
        logger.info(f"Starting Flet application with correlation ID: {startup_correlation_id}")
        
        ft.app(
            target=main,
            name="Hiel RnE Modeler v3.0",
            assets_dir="assets"
        )
    except Exception as e:
        logger = logging.getLogger(__name__)
        global_error_handler.handle_error(e,
            context={
                'function': 'main_startup',
                'correlation_id': startup_correlation_id
            },
            show_user_message=False)
        logger.critical(f"Critical failure starting application: {str(e)}")
        sys.exit(1)
