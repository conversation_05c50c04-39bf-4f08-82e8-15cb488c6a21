"""
Modern Progress Dialog Component for Flet 1.0

This component provides a clean, async-first progress dialog that uses proper
Flet 1.0 patterns for real-time UI updates without threading complexity.
"""

import flet as ft
import asyncio


class ModernProgressDialog:
    """
    A modern progress dialog component that uses Flet 1.0 async patterns
    for real-time progress updates.
    """
    
    def __init__(self, page: ft.Page):
        """
        Initialize the progress dialog component.
        
        Args:
            page: The Flet page instance
        """
        self.page = page
        self.dialog = None
        self.progress_bar = None
        self.progress_text = None
        self.status_text = None
        
    def create_dialog(self, title: str) -> ft.AlertDialog:
        """
        Create the dialog UI with progress bar, percentage text, status text,
        and proper container layout.
        
        Args:
            title: The title to display in the dialog
            
        Returns:
            The configured AlertDialog instance
        """
        # Create progress bar with specified dimensions
        self.progress_bar = ft.ProgressBar(
            width=400,
            height=8,
            value=0,
            color=ft.Colors.BLUE,
            bgcolor=ft.Colors.GREY_300
        )
        
        # Create percentage text display
        self.progress_text = ft.Text(
            "0%",
            size=16,
            weight=ft.FontWeight.BOLD,
            text_align=ft.TextAlign.CENTER
        )
        
        # Create status message text
        self.status_text = ft.Text(
            "Initializing...",
            size=14,
            text_align=ft.TextAlign.CENTER,
            color=ft.Colors.GREY_700
        )
        
        # Create the dialog content container
        content = ft.Container(
            content=ft.Column(
                controls=[
                    self.progress_bar,
                    ft.Container(height=10),  # Spacer
                    self.progress_text,
                    ft.Container(height=5),   # Spacer
                    self.status_text
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=0
            ),
            padding=ft.padding.all(20),
            width=450
        )
        
        # Create the dialog
        self.dialog = ft.AlertDialog(
            title=ft.Text(
                title,
                size=18,
                weight=ft.FontWeight.BOLD,
                text_align=ft.TextAlign.CENTER
            ),
            content=content,
            modal=True,
            actions=[]  # No action buttons for progress dialog
        )
        
        return self.dialog
    
    async def show(self, title: str):
        """
        Create and display the progress dialog using proper Flet 1.0 patterns.
        
        Args:
            title: The title to display in the dialog
        """
        # Create the dialog
        self.create_dialog(title)
        
        # Show the dialog using Flet 1.0 patterns
        self.page.dialog = self.dialog
        self.dialog.open = True

        # Update the page to show the dialog
        self.page.update()  # Use synchronous update
    
    async def update_progress(self, progress: float, message: str = ""):
        """
        Update progress bar value, percentage text, and status message
        using proper async patterns for real-time UI updates.
        
        Args:
            progress: Progress value (will be clamped to 0-100)
            message: Status message to display
        """
        if not self.dialog or not self.dialog.open:
            return
        
        # Clamp progress to 0-100 range
        progress = max(0, min(100, progress))
        
        # Update progress bar value (0.0 to 1.0)
        self.progress_bar.value = progress / 100.0
        
        # Update percentage text
        self.progress_text.value = f"{int(progress)}%"
        
        # Update status message if provided
        if message:
            self.status_text.value = message
        
        # Use proper Flet update patterns for real-time UI updates
        self.page.update()  # Update the entire page to refresh all controls
    
    async def hide(self):
        """
        Close the dialog with proper cleanup.
        """
        if self.dialog:
            self.dialog.open = False
            self.page.update()  # Use synchronous update

            # Clean up references
            self.dialog = None
            self.progress_bar = None
            self.progress_text = None
            self.status_text = None