"""
Contextual Help System
======================

Comprehensive help system with financial glossary and guided tutorials.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass

from components.ui.modern_theme_system import get_theme, ComponentSize
from components.ui.modern_components import ModernButton, ButtonVariant

class HelpType(Enum):
    """Types of contextual help."""
    TOOLTIP = "tooltip"
    DEFINITION = "definition"
    TUTORIAL = "tutorial"
    FAQ = "faq"
    VIDEO = "video"

@dataclass
class HelpItem:
    """Configuration for help items."""
    id: str
    title: str
    content: str
    help_type: HelpType
    category: str
    tags: List[str]
    difficulty: str  # "beginner", "intermediate", "advanced"
    related_items: List[str]

class FinancialGlossary:
    """Financial terms glossary with contextual definitions."""
    
    def __init__(self):
        self.terms = self._load_financial_terms()
    
    def _load_financial_terms(self) -> Dict[str, HelpItem]:
        """Load financial terms and definitions."""
        return {
            'irr': HelpItem(
                id='irr',
                title='Internal Rate of Return (IRR)',
                content="""
**Internal Rate of Return (IRR)** is a financial metric used to estimate the profitability of potential investments.

**Definition**: The discount rate that makes the net present value (NPV) of all cash flows equal to zero.

**What it means**:
- Higher IRR = More attractive investment
- Compare IRR to your required rate of return
- Project IRR should exceed cost of capital

**For Solar Projects**:
- **Good**: IRR > 12% (above market rates)
- **Excellent**: IRR > 15% (strong returns)
- **Caution**: IRR < 8% (below market expectations)

**Example**: If your solar project has an IRR of 14%, it means the project generates a 14% annual return on investment over its lifetime.

**Related**: NPV, Discount Rate, Cost of Capital
                """,
                help_type=HelpType.DEFINITION,
                category='Financial Metrics',
                tags=['irr', 'profitability', 'returns', 'dcf'],
                difficulty='intermediate',
                related_items=['npv', 'discount_rate', 'payback_period']
            ),
            
            'npv': HelpItem(
                id='npv',
                title='Net Present Value (NPV)',
                content="""
**Net Present Value (NPV)** represents the total value created by an investment in today's money.

**Definition**: The difference between the present value of cash inflows and outflows over time.

**What it means**:
- Positive NPV = Value-creating investment
- Higher NPV = More value created
- Accounts for time value of money

**Calculation**: NPV = Σ [Cash Flow / (1 + discount rate)^year] - Initial Investment

**For Solar Projects**:
- **Positive NPV**: Project creates value
- **Higher NPV**: More financial benefit
- **Per MW**: Compare projects of different sizes

**Example**: An NPV of €2M means the project creates €2 million in value above the required return rate.

**Related**: IRR, Present Value, Discount Rate
                """,
                help_type=HelpType.DEFINITION,
                category='Financial Metrics',
                tags=['npv', 'valuation', 'present value', 'dcf'],
                difficulty='intermediate',
                related_items=['irr', 'discount_rate', 'terminal_value']
            ),
            
            'lcoe': HelpItem(
                id='lcoe',
                title='Levelized Cost of Energy (LCOE)',
                content="""
**Levelized Cost of Energy (LCOE)** is the average cost per unit of energy produced over the project lifetime.

**Definition**: Total lifetime costs divided by total lifetime energy production, adjusted for time value of money.

**What it means**:
- Lower LCOE = More competitive energy
- Compare to market electricity prices
- Includes all costs (CAPEX, OPEX, financing)

**Calculation**: LCOE = (CAPEX + PV of OPEX) / PV of Energy Production

**For Solar Projects (2025)**:
- **Excellent**: < €0.040/kWh (highly competitive)
- **Good**: €0.040-0.050/kWh (competitive)
- **Caution**: > €0.060/kWh (above market)

**Example**: LCOE of €0.045/kWh means each kWh costs €0.045 to produce over the project lifetime.

**Related**: CAPEX, OPEX, Capacity Factor
                """,
                help_type=HelpType.DEFINITION,
                category='Technical Metrics',
                tags=['lcoe', 'cost', 'energy', 'competitiveness'],
                difficulty='beginner',
                related_items=['capex', 'opex', 'capacity_factor']
            ),
            
            'dscr': HelpItem(
                id='dscr',
                title='Debt Service Coverage Ratio (DSCR)',
                content="""
**Debt Service Coverage Ratio (DSCR)** measures the project's ability to pay its debt obligations.

**Definition**: Cash available for debt service divided by required debt payments (principal + interest).

**What it means**:
- DSCR > 1.0 = Can cover debt payments
- Higher DSCR = Better debt coverage
- Key metric for lenders

**Calculation**: DSCR = (EBITDA - Taxes - CAPEX) / (Principal + Interest)

**Lender Requirements**:
- **Minimum**: DSCR > 1.20 (barely acceptable)
- **Comfortable**: DSCR > 1.35 (preferred)
- **Strong**: DSCR > 1.50 (excellent coverage)

**Example**: DSCR of 1.40 means the project generates 40% more cash than needed for debt payments.

**Related**: EBITDA, Debt Financing, Interest Coverage
                """,
                help_type=HelpType.DEFINITION,
                category='Financial Metrics',
                tags=['dscr', 'debt', 'coverage', 'lending'],
                difficulty='intermediate',
                related_items=['ebitda', 'debt_ratio', 'interest_rate']
            ),
            
            'capex': HelpItem(
                id='capex',
                title='Capital Expenditures (CAPEX)',
                content="""
**Capital Expenditures (CAPEX)** are the upfront investments required to build the solar project.

**Components for Solar Projects**:
- **Solar panels** (40-50% of CAPEX)
- **Inverters** (8-12% of CAPEX)
- **Mounting systems** (8-10% of CAPEX)
- **Electrical systems** (10-15% of CAPEX)
- **Construction & installation** (15-20% of CAPEX)
- **Development costs** (3-5% of CAPEX)

**2025 Benchmarks**:
- **Utility Solar**: €600-800/kW installed
- **Commercial Solar**: €800-1,200/kW installed
- **Includes**: Equipment, installation, grid connection

**Impact on Project**:
- Lower CAPEX = Higher returns
- Quality equipment = Lower maintenance
- Local content = Potential grants

**Related**: OPEX, Total Project Cost, Equipment Selection
                """,
                help_type=HelpType.DEFINITION,
                category='Technical Metrics',
                tags=['capex', 'investment', 'cost', 'equipment'],
                difficulty='beginner',
                related_items=['opex', 'equipment_cost', 'installation']
            ),
            
            'capacity_factor': HelpItem(
                id='capacity_factor',
                title='Capacity Factor',
                content="""
**Capacity Factor** measures how much energy a solar project produces compared to its theoretical maximum.

**Definition**: Actual energy output / (Capacity × 8,760 hours) × 100%

**What affects Capacity Factor**:
- **Solar irradiation** (most important)
- **Technology type** (tracking vs fixed)
- **Weather patterns** 
- **System efficiency**
- **Maintenance quality**

**Typical Ranges**:
- **Fixed systems**: 15-25%
- **Single-axis tracking**: 20-30%
- **Dual-axis tracking**: 25-35%

**By Location** (approximate):
- **Northern Europe**: 12-18%
- **Southern Europe**: 18-25%
- **North Africa**: 22-30%
- **Middle East**: 25-35%

**Example**: 25% capacity factor means the system produces 25% of its theoretical maximum energy over a year.

**Related**: Solar Irradiation, Technology Selection, Performance Ratio
                """,
                help_type=HelpType.DEFINITION,
                category='Technical Metrics',
                tags=['capacity_factor', 'performance', 'solar', 'irradiation'],
                difficulty='beginner',
                related_items=['solar_irradiation', 'tracking_systems', 'degradation']
            ),
            
            'payback_period': HelpItem(
                id='payback_period',
                title='Payback Period',
                content="""
**Payback Period** is the time required to recover the initial investment through project cash flows.

**Types**:
- **Simple Payback**: Initial investment / Average annual cash flow
- **Discounted Payback**: Accounts for time value of money (more accurate)

**What it means**:
- Shorter payback = Faster cost recovery
- Compare to project lifetime
- Risk indicator (shorter = less risk)

**For Solar Projects**:
- **Excellent**: < 6 years
- **Good**: 6-8 years
- **Acceptable**: 8-12 years
- **Caution**: > 12 years (for 25-year projects)

**Factors Affecting Payback**:
- Initial CAPEX costs
- Energy prices/PPA rates
- Operating costs
- Financing structure
- Tax incentives/grants

**Example**: 7-year payback means the project recovers its initial cost in 7 years, with 18 years of profit remaining.

**Related**: IRR, NPV, Cash Flow Analysis
                """,
                help_type=HelpType.DEFINITION,
                category='Financial Metrics',
                tags=['payback', 'recovery', 'investment', 'time'],
                difficulty='beginner',
                related_items=['irr', 'npv', 'cash_flow']
            ),
            
            'ppa': HelpItem(
                id='ppa',
                title='Power Purchase Agreement (PPA)',
                content="""
**Power Purchase Agreement (PPA)** is a long-term contract to sell electricity at a fixed price.

**Key Terms**:
- **Price**: Fixed rate (€/kWh) or escalating
- **Duration**: Typically 15-25 years
- **Volume**: Annual energy commitments
- **Off-taker**: Utility, corporate, or government buyer

**Types**:
- **Fixed Price**: Same rate throughout contract
- **Escalating**: Annual price increases (1-3%)
- **Merchant**: No long-term contract (market prices)

**2025 PPA Prices** (indicative):
- **Europe**: €0.035-0.055/kWh
- **MENA**: €0.025-0.040/kWh
- **Corporate PPAs**: €0.040-0.065/kWh

**Benefits**:
- Revenue certainty for 15-25 years
- Easier project financing
- Reduced merchant risk
- Predictable cash flows

**Related**: Revenue Model, Off-taker Risk, Market Prices
                """,
                help_type=HelpType.DEFINITION,
                category='Commercial Terms',
                tags=['ppa', 'contract', 'revenue', 'pricing'],
                difficulty='intermediate',
                related_items=['revenue_model', 'offtaker_risk', 'merchant_risk']
            ),
            
            'ebitda': HelpItem(
                id='ebitda',
                title='EBITDA',
                content="""
**EBITDA** (Earnings Before Interest, Taxes, Depreciation, and Amortization) measures operational profitability.

**Definition**: Revenue - Operating Expenses (excluding interest, taxes, depreciation, amortization)

**Calculation for Solar Projects**:
EBITDA = Energy Revenue - O&M Costs - Insurance - Land Lease - Management Fees

**What it shows**:
- Operating profitability before financing costs
- Cash generation capability
- Debt service coverage potential
- Operating efficiency

**For Solar Projects**:
- **Strong EBITDA margin**: > 85%
- **Good EBITDA margin**: 80-85%
- **Concerning**: < 80%

**Uses**:
- DSCR calculations
- Valuation multiples
- Operating performance tracking
- Lender assessments

**Example**: €900k EBITDA from €1M revenue = 90% EBITDA margin (excellent operational efficiency).

**Related**: DSCR, Operating Expenses, Cash Flow
                """,
                help_type=HelpType.DEFINITION,
                category='Financial Metrics',
                tags=['ebitda', 'profitability', 'operations', 'cash'],
                difficulty='intermediate',
                related_items=['dscr', 'opex', 'cash_flow']
            )
        }

class ContextualHelpSystem:
    """Comprehensive contextual help system."""
    
    def __init__(self, on_action: Optional[Callable[[str, Dict[str, Any]], None]] = None):
        self.theme = get_theme()
        self.on_action = on_action
        self.glossary = FinancialGlossary()
        
        # Help state
        self.current_term = None
        self.search_query = ""
        self.selected_category = "All"
        self.help_history = []
        
        # Tutorial state
        self.active_tutorial = None
        self.tutorial_step = 0
        
        # User preferences
        self.preferences = {
            'show_tooltips': True,
            'auto_suggest': True,
            'difficulty_level': 'intermediate',
            'preferred_language': 'en'
        }
    
    def create_help_tooltip(self, term: str, context: Optional[str] = None) -> ft.Control:
        """Create contextual help tooltip for financial terms."""
        if term.lower() not in self.glossary.terms:
            return ft.Container()
        
        help_item = self.glossary.terms[term.lower()]
        
        # Extract first paragraph for tooltip
        content_lines = help_item.content.strip().split('\n')
        first_paragraph = ""
        for line in content_lines:
            if line.strip() and not line.startswith('**'):
                first_paragraph = line.strip()
                break
        
        tooltip_content = first_paragraph[:200] + "..." if len(first_paragraph) > 200 else first_paragraph
        
        return ft.Tooltip(
            message=f"{help_item.title}\n\n{tooltip_content}\n\nClick for detailed explanation",
            content=ft.Container(
                content=ft.Row([
                    ft.Text(term, weight=ft.FontWeight.W_500),
                    ft.Icon(ft.Icons.HELP_OUTLINE, size=14,
                           color=self.theme.get_semantic_color('info'))
                ], spacing=4),
                padding=ft.padding.symmetric(horizontal=4, vertical=2),
                bgcolor=self.theme.get_semantic_color('info', '50'),
                border_radius=4,
                border=ft.border.all(1, self.theme.get_semantic_color('info', '300')),
                on_click=lambda e: self._show_term_details(term.lower()),
                ink=True
            ),
            wait_duration=500,
            show_duration=5000
        )
    
    def create_help_panel(self) -> ft.Control:
        """Create comprehensive help panel."""
        return ft.Container(
            content=ft.Column([
                self._create_help_header(),
                ft.Container(height=16),
                self._create_search_and_filters(),
                ft.Container(height=16),
                self._create_help_content()
            ]),
            padding=ft.padding.all(20),
            bgcolor=self.theme.get_background_colors()['surface'],
            border_radius=12,
            shadow=self.theme.tokens.shadows['lg'],
            width=800,
            height=600
        )
    
    def _create_help_header(self) -> ft.Control:
        """Create help panel header."""
        return ft.Row([
            ft.Column([
                ft.Text(
                    "💡 Financial Analysis Help",
                    size=20,
                    weight=ft.FontWeight.W_700,
                    color=self.theme.get_text_colors()['primary']
                ),
                ft.Text(
                    "Comprehensive glossary and guidance for solar project analysis",
                    size=14,
                    color=self.theme.get_text_colors()['secondary']
                )
            ], expand=True),
            ft.Row([
                ft.IconButton(
                    icon=ft.Icons.SCHOOL,
                    tooltip="Start Tutorial",
                    on_click=self._start_tutorial,
                    icon_color=self.theme.get_semantic_color('primary')
                ),
                ft.IconButton(
                    icon=ft.Icons.SETTINGS,
                    tooltip="Help Settings",
                    on_click=self._show_help_settings,
                    icon_color=self.theme.get_semantic_color('neutral', '600')
                )
            ])
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
    
    def _create_search_and_filters(self) -> ft.Control:
        """Create search and filter controls."""
        categories = ["All", "Financial Metrics", "Technical Metrics", "Commercial Terms", "Risk Analysis"]
        
        category_chips = []
        for category in categories:
            is_selected = category == self.selected_category
            
            chip = ft.Container(
                content=ft.Text(
                    category,
                    size=12,
                    color=ft.Colors.WHITE if is_selected else self.theme.get_text_colors()['secondary']
                ),
                padding=ft.padding.symmetric(horizontal=12, vertical=6),
                bgcolor=self.theme.get_semantic_color('primary') if is_selected else self.theme.get_semantic_color('neutral', '100'),
                border_radius=16,
                on_click=lambda e, cat=category: self._select_category(cat),
                ink=True
            )
            category_chips.append(chip)
        
        return ft.Column([
            ft.TextField(
                hint_text="Search financial terms, metrics, or concepts...",
                prefix_icon=ft.Icons.SEARCH,
                border_radius=8,
                on_change=self._handle_search,
                value=self.search_query
            ),
            ft.Container(height=8),
            ft.Row([
                ft.Text("Categories:", size=12, weight=ft.FontWeight.W_500),
                ft.Row(category_chips, spacing=8, scroll=ft.ScrollMode.AUTO)
            ], spacing=12)
        ])
    
    def _create_help_content(self) -> ft.Control:
        """Create main help content area."""
        if self.current_term:
            return self._create_term_detail_view()
        else:
            return self._create_term_list_view()
    
    def _create_term_list_view(self) -> ft.Control:
        """Create list view of financial terms."""
        filtered_terms = self._filter_terms()
        
        if not filtered_terms:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.SEARCH_OFF, size=48, color=self.theme.get_semantic_color('neutral', '300')),
                    ft.Text("No terms found matching your search",
                           color=self.theme.get_text_colors()['secondary'])
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                alignment=ft.alignment.center,
                expand=True
            )
        
        term_cards = []
        for term_id in filtered_terms:
            term = self.glossary.terms[term_id]
            term_cards.append(self._create_term_card(term))
        
        return ft.Column([
            ft.Text(f"Found {len(filtered_terms)} terms", 
                   size=12, color=self.theme.get_text_colors()['tertiary']),
            ft.Container(height=8),
            ft.Column(term_cards, spacing=8, scroll=ft.ScrollMode.AUTO, expand=True)
        ], expand=True)
    
    def _create_term_card(self, term: HelpItem) -> ft.Control:
        """Create card for financial term."""
        # Difficulty color coding
        difficulty_colors = {
            'beginner': 'success',
            'intermediate': 'warning', 
            'advanced': 'error'
        }
        difficulty_color = difficulty_colors.get(term.difficulty, 'neutral')
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Column([
                        ft.Text(term.title, size=16, weight=ft.FontWeight.W_600),
                        ft.Text(term.category, size=12, color=self.theme.get_text_colors()['tertiary'])
                    ], expand=True),
                    ft.Container(
                        content=ft.Text(term.difficulty.title(), size=10, color=ft.Colors.WHITE),
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        bgcolor=self.theme.get_semantic_color(difficulty_color),
                        border_radius=12
                    )
                ]),
                ft.Text(
                    self._extract_summary(term.content),
                    size=12,
                    color=self.theme.get_text_colors()['secondary'],
                    max_lines=2
                ),
                ft.Row([
                    ft.Text(f"🏷️ {', '.join(term.tags[:3])}", size=10, 
                           color=self.theme.get_text_colors()['tertiary']),
                    ft.TextButton(
                        "View Details",
                        style=ft.ButtonStyle(color=self.theme.get_semantic_color('primary')),
                        on_click=lambda e, tid=term.id: self._show_term_details(tid)
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
            ]),
            padding=ft.padding.all(16),
            bgcolor=self.theme.get_background_colors()['secondary'],
            border_radius=8,
            border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
            ink=True,
            on_click=lambda e, tid=term.id: self._show_term_details(tid)
        )
    
    def _create_term_detail_view(self) -> ft.Control:
        """Create detailed view for a specific term."""
        if not self.current_term or self.current_term not in self.glossary.terms:
            return ft.Text("Term not found")
        
        term = self.glossary.terms[self.current_term]
        
        # Related terms
        related_items = []
        for related_id in term.related_items:
            if related_id in self.glossary.terms:
                related_term = self.glossary.terms[related_id]
                related_items.append(
                    ft.TextButton(
                        related_term.title,
                        style=ft.ButtonStyle(color=self.theme.get_semantic_color('primary')),
                        on_click=lambda e, rid=related_id: self._show_term_details(rid)
                    )
                )
        
        return ft.Column([
            # Header with back button
            ft.Row([
                ft.IconButton(
                    icon=ft.Icons.ARROW_BACK,
                    on_click=self._go_back,
                    tooltip="Back to list"
                ),
                ft.Column([
                    ft.Text(term.title, size=18, weight=ft.FontWeight.W_700),
                    ft.Text(f"{term.category} • {term.difficulty.title()}", 
                           size=12, color=self.theme.get_text_colors()['tertiary'])
                ], expand=True)
            ]),
            
            ft.Container(height=16),
            
            # Content
            ft.Container(
                content=ft.Markdown(
                    term.content,
                    selectable_text=True,
                    extension_set=ft.MarkdownExtensionSet.GITHUB_WEB,
                    on_tap_link=self._handle_link_tap
                ),
                padding=ft.padding.all(16),
                bgcolor=self.theme.get_background_colors()['secondary'],
                border_radius=8,
                expand=True
            ),
            
            ft.Container(height=16),
            
            # Related terms
            ft.Column([
                ft.Text("Related Terms:", size=14, weight=ft.FontWeight.W_600),
                ft.Row(related_items, spacing=8, wrap=True)
            ]) if related_items else ft.Container(),
            
            # Action buttons
            ft.Row([
                ModernButton(
                    "📋 Copy Definition",
                    variant=ButtonVariant.SECONDARY,
                    size=ComponentSize.SM,
                    on_click=lambda e: self._copy_definition(term)
                ).build(),
                ModernButton(
                    "🎥 Watch Tutorial",
                    variant=ButtonVariant.PRIMARY,
                    size=ComponentSize.SM,
                    on_click=lambda e: self._watch_tutorial(term.id)
                ).build()
            ], spacing=8)
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _filter_terms(self) -> List[str]:
        """Filter terms based on search and category."""
        filtered = []
        
        for term_id, term in self.glossary.terms.items():
            # Category filter
            if self.selected_category != "All" and term.category != self.selected_category:
                continue
            
            # Search filter
            if self.search_query:
                query = self.search_query.lower()
                if (query not in term.title.lower() and 
                    query not in term.content.lower() and
                    not any(query in tag for tag in term.tags)):
                    continue
            
            filtered.append(term_id)
        
        return filtered
    
    def _extract_summary(self, content: str) -> str:
        """Extract summary from help content."""
        lines = content.strip().split('\n')
        for line in lines:
            if line.strip() and not line.startswith('**') and len(line.strip()) > 50:
                return line.strip()[:150] + "..." if len(line.strip()) > 150 else line.strip()
        return "Click for detailed explanation"
    
    # Event handlers
    def _handle_search(self, e):
        """Handle search input."""
        self.search_query = e.control.value
    
    def _select_category(self, category: str):
        """Select category filter."""
        self.selected_category = category
    
    def _show_term_details(self, term_id: str):
        """Show detailed view for term."""
        self.current_term = term_id
        self.help_history.append(term_id)
    
    def _go_back(self, e):
        """Go back to term list."""
        self.current_term = None
    
    def _copy_definition(self, term: HelpItem):
        """Copy term definition to clipboard."""
        # Would implement clipboard copy
        pass
    
    def _watch_tutorial(self, term_id: str):
        """Watch tutorial for term."""
        if self.on_action:
            self.on_action("watch_tutorial", {"term_id": term_id})
    
    def _start_tutorial(self, e):
        """Start interactive tutorial."""
        if self.on_action:
            self.on_action("start_tutorial", {})
    
    def _show_help_settings(self, e):
        """Show help system settings."""
        if self.on_action:
            self.on_action("show_help_settings", {})
    
    def _handle_link_tap(self, e):
        """Handle markdown link taps."""
        # Would handle internal links to other terms
        pass
    
    def create_inline_help_button(self, term: str, compact: bool = False) -> ft.Control:
        """Create inline help button for terms."""
        if term.lower() not in self.glossary.terms:
            return ft.Container()
        
        return ft.IconButton(
            icon=ft.Icons.HELP_OUTLINE,
            icon_size=16 if compact else 18,
            tooltip=f"Learn about {term}",
            on_click=lambda e: self._show_term_details(term.lower()),
            icon_color=self.theme.get_semantic_color('info'),
            style=ft.ButtonStyle(
                padding=ft.padding.all(4)
            )
        )
    
    def create_smart_text_with_help(self, text: str, **text_kwargs) -> ft.Control:
        """Create text with automatic help links for financial terms."""
        # Simple implementation - would be enhanced with NLP
        words = text.split()
        components = []
        
        for word in words:
            clean_word = word.lower().strip('.,!?:;')
            if clean_word in self.glossary.terms:
                # Create clickable term
                components.append(
                    ft.GestureDetector(
                        content=ft.Text(
                            word,
                            color=self.theme.get_semantic_color('primary'),
                            style=ft.TextStyle(decoration=ft.TextDecoration.UNDERLINE),
                            **text_kwargs
                        ),
                        on_tap=lambda e, term=clean_word: self._show_term_details(term)
                    )
                )
            else:
                components.append(ft.Text(word + " ", **text_kwargs))
        
        return ft.Row(components, spacing=0, wrap=True)
    
    def get_contextual_suggestions(self, context: str) -> List[str]:
        """Get contextual help suggestions based on current context."""
        suggestions = []
        
        context_mapping = {
            'setup': ['capex', 'capacity_factor', 'ppa'],
            'analysis': ['irr', 'npv', 'lcoe', 'dscr'],
            'results': ['payback_period', 'ebitda', 'terminal_value'],
            'risk': ['dscr', 'sensitivity', 'monte_carlo'],
            'export': ['financial_summary', 'investment_case']
        }
        
        return context_mapping.get(context.lower(), [])
    
    def create_contextual_help_sidebar(self, context: str) -> ft.Control:
        """Create contextual help sidebar for current view."""
        suggestions = self.get_contextual_suggestions(context)
        
        if not suggestions:
            return ft.Container()
        
        suggestion_items = []
        for term_id in suggestions:
            if term_id in self.glossary.terms:
                term = self.glossary.terms[term_id]
                suggestion_items.append(
                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LIGHTBULB, size=16,
                                      color=self.theme.get_semantic_color('warning')),
                        title=ft.Text(term.title, size=12),
                        subtitle=ft.Text(self._extract_summary(term.content)[:50] + "...", 
                                       size=10),
                        on_click=lambda e, tid=term_id: self._show_term_details(tid),
                        dense=True
                    )
                )
        
        return ft.Container(
            content=ft.Column([
                ft.Text("💡 Helpful Terms", size=14, weight=ft.FontWeight.W_600,
                       color=self.theme.get_text_colors()['primary']),
                ft.Container(height=8),
                ft.Column(suggestion_items, spacing=4)
            ]),
            padding=ft.padding.all(16),
            bgcolor=self.theme.get_semantic_color('warning', '50'),
            border_radius=8,
            border=ft.border.all(1, self.theme.get_semantic_color('warning', '200')),
            width=300
        )
