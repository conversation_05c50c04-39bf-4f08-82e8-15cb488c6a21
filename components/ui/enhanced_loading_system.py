"""
Enhanced Loading System
======================

Advanced loading states, skeleton screens, and feedback components for better UX.
"""

import flet as ft
from typing import Optional, List, Dict, Any, Callable
from enum import Enum
import asyncio

from components.ui.modern_theme_system import get_theme, ComponentSize

class LoadingVariant(Enum):
    """Loading animation variants."""
    SPINNER = "spinner"
    DOTS = "dots"
    PULSE = "pulse"
    SKELETON = "skeleton"
    PROGRESS = "progress"

class SkeletonType(Enum):
    """Skeleton loading types."""
    TEXT = "text"
    AVATAR = "avatar"
    CARD = "card"
    CHART = "chart"
    TABLE = "table"

class EnhancedLoadingSpinner:
    """Modern loading spinner with customizable animations."""
    
    def __init__(self, 
                 variant: LoadingVariant = LoadingVariant.SPINNER,
                 size: ComponentSize = ComponentSize.MD,
                 color: Optional[str] = None,
                 message: Optional[str] = None,
                 show_percentage: bool = False):
        self.variant = variant
        self.size = size
        self.color = color
        self.message = message
        self.show_percentage = show_percentage
        self.theme = get_theme()
        self._percentage = 0
    
    def build(self) -> ft.Control:
        """Build the loading spinner."""
        size_map = {
            ComponentSize.XS: 16,
            ComponentSize.SM: 20,
            ComponentSize.MD: 24,
            ComponentSize.LG: 32,
            ComponentSize.XL: 40
        }
        
        spinner_size = size_map[self.size]
        spinner_color = self.color or self.theme.get_semantic_color('primary')
        
        if self.variant == LoadingVariant.SPINNER:
            spinner = ft.ProgressRing(
                width=spinner_size,
                height=spinner_size,
                stroke_width=3,
                color=spinner_color
            )
        elif self.variant == LoadingVariant.DOTS:
            spinner = self._create_dots_animation(spinner_size, spinner_color)
        elif self.variant == LoadingVariant.PULSE:
            spinner = self._create_pulse_animation(spinner_size, spinner_color)
        else:
            spinner = ft.ProgressRing(width=spinner_size, height=spinner_size, color=spinner_color)
        
        # Wrap with message if provided
        if self.message:
            content = ft.Column([
                spinner,
                ft.Text(
                    self.message,
                    size=self.theme.tokens.component_sizes[self.size]['font_size'],
                    color=self.theme.get_text_colors()['secondary'],
                    text_align=ft.TextAlign.CENTER
                )
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8)
            
            if self.show_percentage:
                content.controls.append(
                    ft.Text(
                        f"{self._percentage}%",
                        size=self.theme.tokens.component_sizes[self.size]['font_size'] - 1,
                        color=self.theme.get_text_colors()['tertiary'],
                        text_align=ft.TextAlign.CENTER
                    )
                )
            
            return content
        
        return spinner
    
    def _create_dots_animation(self, size: int, color: str) -> ft.Control:
        """Create animated dots loading indicator."""
        dot_size = size // 4
        return ft.Row([
            ft.Container(
                width=dot_size,
                height=dot_size,
                bgcolor=color,
                border_radius=dot_size // 2,
                animate_opacity=ft.Animation(600, ft.AnimationCurve.EASE_IN_OUT)
            ) for _ in range(3)
        ], spacing=4, alignment=ft.MainAxisAlignment.CENTER)
    
    def _create_pulse_animation(self, size: int, color: str) -> ft.Control:
        """Create pulsing animation."""
        return ft.Container(
            width=size,
            height=size,
            bgcolor=color,
            border_radius=size // 2,
            animate_scale=ft.Animation(800, ft.AnimationCurve.EASE_IN_OUT),
            animate_opacity=ft.Animation(800, ft.AnimationCurve.EASE_IN_OUT)
        )
    
    def update_percentage(self, percentage: float):
        """Update loading percentage."""
        self._percentage = min(100, max(0, percentage))

class SkeletonLoader:
    """Skeleton loading screens for different content types."""
    
    def __init__(self, 
                 skeleton_type: SkeletonType,
                 count: int = 1,
                 animated: bool = True,
                 custom_dimensions: Optional[Dict[str, Any]] = None):
        self.skeleton_type = skeleton_type
        self.count = count
        self.animated = animated
        self.custom_dimensions = custom_dimensions or {}
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build skeleton loading screen."""
        skeletons = []
        
        for _ in range(self.count):
            if self.skeleton_type == SkeletonType.TEXT:
                skeletons.append(self._create_text_skeleton())
            elif self.skeleton_type == SkeletonType.AVATAR:
                skeletons.append(self._create_avatar_skeleton())
            elif self.skeleton_type == SkeletonType.CARD:
                skeletons.append(self._create_card_skeleton())
            elif self.skeleton_type == SkeletonType.CHART:
                skeletons.append(self._create_chart_skeleton())
            elif self.skeleton_type == SkeletonType.TABLE:
                skeletons.append(self._create_table_skeleton())
        
        return ft.Column(skeletons, spacing=self.theme.tokens.spacing['md'])
    
    def _create_skeleton_element(self, width: int, height: int) -> ft.Container:
        """Create base skeleton element with shimmer effect."""
        skeleton_style = self.theme.create_skeleton_style()
        
        element = ft.Container(
            width=width,
            height=height,
            bgcolor=skeleton_style['bgcolor'],
            border_radius=skeleton_style['border_radius']
        )
        
        if self.animated:
            element.animate_opacity = ft.Animation(
                skeleton_style['animation_duration'],
                ft.AnimationCurve.EASE_IN_OUT
            )
        
        return element
    
    def _create_text_skeleton(self) -> ft.Control:
        """Create text line skeleton."""
        lines = []
        base_width = self.custom_dimensions.get('width', 200)
        
        # Create multiple lines with varying widths
        for i in range(3):
            width = base_width - (i * 20)  # Each line slightly shorter
            height = self.custom_dimensions.get('height', 16)
            lines.append(self._create_skeleton_element(width, height))
        
        return ft.Column(lines, spacing=8)
    
    def _create_avatar_skeleton(self) -> ft.Control:
        """Create avatar skeleton."""
        size = self.custom_dimensions.get('size', 48)
        return ft.Container(
            width=size,
            height=size,
            bgcolor=self.theme.get_semantic_color('neutral', '200'),
            border_radius=size // 2
        )
    
    def _create_card_skeleton(self) -> ft.Control:
        """Create card skeleton."""
        return ft.Container(
            width=self.custom_dimensions.get('width', 300),
            height=self.custom_dimensions.get('height', 200),
            padding=self.theme.tokens.spacing['lg'],
            bgcolor=self.theme.get_background_colors()['surface'],
            border_radius=self.theme.tokens.radius['lg'],
            border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
            content=ft.Column([
                # Header
                ft.Row([
                    self._create_avatar_skeleton(),
                    ft.Column([
                        self._create_skeleton_element(120, 16),
                        self._create_skeleton_element(80, 12)
                    ], spacing=4)
                ], spacing=12),
                # Content
                ft.Column([
                    self._create_skeleton_element(250, 12),
                    self._create_skeleton_element(200, 12),
                    self._create_skeleton_element(180, 12)
                ], spacing=8),
                # Footer
                ft.Row([
                    self._create_skeleton_element(60, 24),
                    self._create_skeleton_element(60, 24)
                ], spacing=8)
            ], spacing=16)
        )
    
    def _create_chart_skeleton(self) -> ft.Control:
        """Create chart skeleton."""
        return ft.Container(
            width=self.custom_dimensions.get('width', 400),
            height=self.custom_dimensions.get('height', 250),
            padding=self.theme.tokens.spacing['lg'],
            bgcolor=self.theme.get_background_colors()['surface'],
            border_radius=self.theme.tokens.radius['lg'],
            border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
            content=ft.Column([
                # Chart title
                self._create_skeleton_element(150, 20),
                # Chart area
                ft.Container(
                    width=350,
                    height=180,
                    bgcolor=self.theme.get_semantic_color('neutral', '100'),
                    border_radius=self.theme.tokens.radius['md']
                )
            ], spacing=16)
        )
    
    def _create_table_skeleton(self) -> ft.Control:
        """Create table skeleton."""
        rows = []
        
        # Header row
        header_cells = [self._create_skeleton_element(80, 20) for _ in range(4)]
        rows.append(ft.Row(header_cells, spacing=16))
        
        # Data rows
        for _ in range(5):
            data_cells = [self._create_skeleton_element(60 + (i * 10), 16) for i in range(4)]
            rows.append(ft.Row(data_cells, spacing=16))
        
        return ft.Container(
            padding=self.theme.tokens.spacing['lg'],
            bgcolor=self.theme.get_background_colors()['surface'],
            border_radius=self.theme.tokens.radius['lg'],
            border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
            content=ft.Column(rows, spacing=12)
        )

class ProgressIndicator:
    """Enhanced progress indicator with multiple variants."""
    
    def __init__(self,
                 value: float = 0,
                 max_value: float = 100,
                 variant: str = "linear",
                 size: ComponentSize = ComponentSize.MD,
                 color: Optional[str] = None,
                 show_label: bool = True,
                 animated: bool = True):
        self.value = value
        self.max_value = max_value
        self.variant = variant
        self.size = size
        self.color = color
        self.show_label = show_label
        self.animated = animated
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build progress indicator."""
        if self.variant == "linear":
            return self._create_linear_progress()
        elif self.variant == "circular":
            return self._create_circular_progress()
        elif self.variant == "stepped":
            return self._create_stepped_progress()
        else:
            return self._create_linear_progress()
    
    def _create_linear_progress(self) -> ft.Control:
        """Create linear progress bar."""
        progress_style = self.theme.create_progress_style()
        percentage = (self.value / self.max_value) * 100
        
        progress_bar = ft.Container(
            width=200,
            height=progress_style['height'],
            bgcolor=progress_style['track_color'],
            border_radius=progress_style['border_radius'],
            content=ft.Container(
                width=200 * (self.value / self.max_value),
                height=progress_style['height'],
                bgcolor=self.color or progress_style['fill_color'],
                border_radius=progress_style['border_radius']
            )
        )
        
        if self.animated:
            progress_bar.content.animate = ft.Animation(300, ft.AnimationCurve.EASE_OUT)
        
        if self.show_label:
            return ft.Column([
                ft.Row([
                    ft.Text("Progress", size=12, color=self.theme.get_text_colors()['secondary']),
                    ft.Text(f"{percentage:.1f}%", size=12, color=self.theme.get_text_colors()['primary'])
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                progress_bar
            ], spacing=4)
        
        return progress_bar
    
    def _create_circular_progress(self) -> ft.Control:
        """Create circular progress indicator."""
        size_map = {
            ComponentSize.XS: 32,
            ComponentSize.SM: 40,
            ComponentSize.MD: 48,
            ComponentSize.LG: 56,
            ComponentSize.XL: 64
        }
        
        progress_size = size_map[self.size]
        percentage = (self.value / self.max_value) * 100
        
        return ft.Stack([
            ft.Container(
                width=progress_size,
                height=progress_size,
                border_radius=progress_size // 2,
                bgcolor=self.theme.get_semantic_color('neutral', '200')
            ),
            ft.ProgressRing(
                width=progress_size,
                height=progress_size,
                value=self.value / self.max_value,
                color=self.color or self.theme.get_semantic_color('primary'),
                stroke_width=4
            ),
            ft.Container(
                width=progress_size,
                height=progress_size,
                content=ft.Text(
                    f"{percentage:.0f}%",
                    size=10,
                    weight=ft.FontWeight.BOLD,
                    color=self.theme.get_text_colors()['primary'],
                    text_align=ft.TextAlign.CENTER
                ),
                alignment=ft.alignment.center
            )
        ])
    
    def _create_stepped_progress(self) -> ft.Control:
        """Create stepped progress indicator."""
        steps = 5  # Default number of steps
        current_step = int((self.value / self.max_value) * steps)
        
        step_elements = []
        for i in range(steps):
            is_completed = i < current_step
            is_current = i == current_step
            
            step_color = self.theme.get_semantic_color('primary') if is_completed else self.theme.get_semantic_color('neutral', '300')
            
            step_element = ft.Container(
                width=40,
                height=40,
                bgcolor=step_color,
                border_radius=20,
                content=ft.Text(
                    str(i + 1),
                    color=ft.Colors.WHITE if is_completed else self.theme.get_text_colors()['secondary'],
                    size=14,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER
                ),
                alignment=ft.alignment.center,
                border=ft.border.all(2, self.theme.get_semantic_color('primary')) if is_current else None
            )
            
            step_elements.append(step_element)
            
            # Add connector line between steps
            if i < steps - 1:
                step_elements.append(
                    ft.Container(
                        width=50,
                        height=2,
                        bgcolor=step_color if is_completed else self.theme.get_semantic_color('neutral', '300')
                    )
                )
        
        return ft.Row(step_elements, alignment=ft.MainAxisAlignment.CENTER)
    
    def update_value(self, new_value: float):
        """Update progress value."""
        self.value = min(self.max_value, max(0, new_value))

class FeedbackToast:
    """Enhanced toast notifications with different types."""
    
    def __init__(self,
                 message: str,
                 type: str = "info",
                 duration: int = 4000,
                 action_label: Optional[str] = None,
                 action_callback: Optional[Callable] = None,
                 dismissible: bool = True):
        self.message = message
        self.type = type
        self.duration = duration
        self.action_label = action_label
        self.action_callback = action_callback
        self.dismissible = dismissible
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build toast notification."""
        notification_style = self.theme.create_notification_style(self.type)
        
        # Icon mapping
        icon_map = {
            'success': ft.Icons.CHECK_CIRCLE,
            'error': ft.Icons.ERROR,
            'warning': ft.Icons.WARNING,
            'info': ft.Icons.INFO
        }
        
        icon = ft.Icon(
            icon_map.get(self.type, ft.Icons.INFO),
            color=notification_style['icon_color'],
            size=20
        )
        
        content_elements = [
            icon,
            ft.Text(
                self.message,
                color=notification_style['text_color'],
                size=14,
                expand=True
            )
        ]
        
        # Add action button if provided
        if self.action_label and self.action_callback:
            content_elements.append(
                ft.TextButton(
                    self.action_label,
                    on_click=self.action_callback,
                    style=ft.ButtonStyle(
                        color=notification_style['icon_color']
                    )
                )
            )
        
        # Add dismiss button if dismissible
        if self.dismissible:
            content_elements.append(
                ft.IconButton(
                    ft.Icons.CLOSE,
                    icon_size=16,
                    icon_color=notification_style['text_color'],
                    tooltip="Dismiss"
                )
            )
        
        return ft.Container(
            content=ft.Row(content_elements, spacing=12),
            bgcolor=notification_style['bgcolor'],
            border=ft.border.all(1, notification_style['border_color']),
            border_radius=notification_style['border_radius'],
            padding=notification_style['padding'],
            shadow=notification_style['shadow'],
            width=350
        )

class LoadingOverlay:
    """Full-screen loading overlay with enhanced UX."""
    
    def __init__(self,
                 message: str = "Loading...",
                 variant: LoadingVariant = LoadingVariant.SPINNER,
                 blocking: bool = True,
                 show_backdrop: bool = True):
        self.message = message
        self.variant = variant
        self.blocking = blocking
        self.show_backdrop = show_backdrop
        self.theme = get_theme()
        self._is_visible = False
    
    def build(self) -> ft.Control:
        """Build loading overlay."""
        spinner = EnhancedLoadingSpinner(
            variant=self.variant,
            size=ComponentSize.LG,
            message=self.message
        ).build()
        
        overlay_content = ft.Container(
            content=ft.Column([
                spinner
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            alignment=ft.alignment.center,
            expand=True
        )
        
        if self.show_backdrop:
            overlay_content.bgcolor = ft.Colors.with_opacity(0.8, ft.Colors.BLACK)
        
        return overlay_content
    
    def show(self):
        """Show loading overlay."""
        self._is_visible = True
    
    def hide(self):
        """Hide loading overlay."""
        self._is_visible = False
    
    @property
    def is_visible(self) -> bool:
        """Check if overlay is visible."""
        return self._is_visible

# Utility functions for easy component creation
def create_loading_spinner(variant: LoadingVariant = LoadingVariant.SPINNER,
                          size: ComponentSize = ComponentSize.MD,
                          message: str = "Loading...") -> ft.Control:
    """Quick function to create a loading spinner."""
    return EnhancedLoadingSpinner(variant, size, message=message).build()

def create_skeleton(skeleton_type: SkeletonType,
                   count: int = 1,
                   **kwargs) -> ft.Control:
    """Quick function to create skeleton loading."""
    return SkeletonLoader(skeleton_type, count, **kwargs).build()

def create_progress_bar(value: float,
                       max_value: float = 100,
                       variant: str = "linear",
                       **kwargs) -> ft.Control:
    """Quick function to create progress bar."""
    return ProgressIndicator(value, max_value, variant, **kwargs).build()

def create_toast(message: str,
                type: str = "info",
                duration: int = 4000,
                **kwargs) -> ft.Control:
    """Quick function to create toast notification."""
    return FeedbackToast(message, type, duration, **kwargs).build()
