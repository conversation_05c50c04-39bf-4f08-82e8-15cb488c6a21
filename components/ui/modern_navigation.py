"""
Modern Navigation System
=======================

Enhanced navigation components with modern UX patterns and improved usability.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass

from components.ui.modern_theme_system import get_theme


class NavigationState(Enum):
    """Navigation sidebar states."""
    COLLAPSED = "collapsed"
    EXPANDED = "expanded"
    MINI = "mini"


@dataclass
class NavigationItem:
    """Navigation item configuration."""
    id: str
    label: str
    icon: str
    route: Optional[str] = None
    badge: Optional[str] = None
    badge_variant: str = "default"
    children: Optional[List['NavigationItem']] = None
    disabled: bool = False
    tooltip: Optional[str] = None


class ModernSidebar:
    """Modern collapsible sidebar navigation."""
    
    def __init__(self,
                 navigation_items: List[NavigationItem],
                 on_navigate: Optional[Callable[[str], None]] = None,
                 on_state_change: Optional[Callable[[NavigationState], None]] = None,
                 initial_state: NavigationState = NavigationState.EXPANDED,
                 show_user_profile: bool = True,
                 user_name: Optional[str] = None,
                 user_avatar: Optional[str] = None):
        
        self.navigation_items = navigation_items
        self.on_navigate = on_navigate
        self.on_state_change = on_state_change
        self.current_state = initial_state
        self.show_user_profile = show_user_profile
        self.user_name = user_name or "User"
        self.user_avatar = user_avatar
        self.selected_item_id: Optional[str] = None
        self.theme = get_theme()
        
        # State-dependent configurations
        self.state_configs = {
            NavigationState.EXPANDED: {
                'width': 220,  # Reduced from 280 for better space efficiency
                'show_labels': True,
                'show_user_details': True,
                'icon_size': 20,  # Slightly smaller for compact design
                'padding': 16   # Reduced padding for efficiency
            },
            NavigationState.COLLAPSED: {
                'width': 70,   # Slightly reduced for consistency
                'show_labels': False,
                'show_user_details': False,
                'icon_size': 20,
                'padding': 12
            },
            NavigationState.MINI: {
                'width': 50,   # More compact mini state
                'show_labels': False,
                'show_user_details': False,
                'icon_size': 18,
                'padding': 8
            }
        }
    
    def build(self) -> ft.Control:
        """Build the modern sidebar with proper scrolling and layout."""
        config = self.state_configs[self.current_state]

        # Create header section (fixed at top)
        header_section = ft.Container(
            content=ft.Column([
                self._create_header(config),
                ft.Divider(height=1, color=self.theme.get_semantic_color('neutral', '200'))
            ], spacing=8),
            padding=ft.padding.symmetric(vertical=config['padding'])
        )

        # Create scrollable navigation section
        navigation_section = ft.Container(
            content=self._create_navigation_items(config),
            expand=True,  # Takes remaining space
            padding=ft.padding.only(bottom=8)
        )

        # Create footer section (fixed at bottom)
        footer_section = ft.Container(
            content=self._create_footer(config) if self.show_user_profile else ft.Container(),
            padding=ft.padding.symmetric(vertical=config['padding'])
        )

        # Main sidebar layout with proper height management
        content = ft.Column([
            header_section,
            navigation_section,
            footer_section
        ], spacing=0, expand=True)

        # Create sidebar container
        sidebar = ft.Container(
            content=content,
            width=config['width'],
            bgcolor=self.theme.get_background_colors()['surface'],
            border=ft.border.only(right=ft.BorderSide(1, self.theme.get_semantic_color('neutral', '200'))),
            animate_size=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
        )

        return sidebar
    
    def _create_header(self, config: Dict[str, Any]) -> ft.Control:
        """Create modern sidebar header with enhanced branding and toggle."""
        header_content = []

        if config['show_labels']:
            # Enhanced full header with modern branding
            brand_container = ft.Container(
                content=ft.Row([
                    # Modern logo with gradient background
                    ft.Container(
                        content=ft.Icon(
                            ft.Icons.ANALYTICS_ROUNDED,
                            size=36,
                            color=ft.Colors.WHITE
                        ),
                        width=48,
                        height=48,
                        bgcolor=self.theme.get_semantic_color('primary'),
                        border_radius=12,
                        alignment=ft.alignment.center,
                        shadow=ft.BoxShadow(
                            spread_radius=0,
                            blur_radius=8,
                            offset=ft.Offset(0, 2),
                            color=self.theme.get_semantic_color('primary', '200')
                        )
                    ),
                    # Enhanced title section
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "Hiel RnE",
                                size=20,
                                weight=ft.FontWeight.W_800,
                                color=self.theme.get_text_colors()['primary']
                            ),
                            ft.Text(
                                "Modeler v4.1",
                                size=13,
                                weight=ft.FontWeight.W_500,
                                color=self.theme.get_semantic_color('primary')
                            )
                        ], spacing=2),
                        margin=ft.margin.only(left=12),
                        expand=True
                    )
                ], spacing=0),
                padding=ft.padding.symmetric(vertical=8)
            )
            header_content.append(brand_container)

            # Separator line
            header_content.append(
                ft.Container(
                    height=1,
                    bgcolor=self.theme.get_semantic_color('neutral', '200'),
                    margin=ft.margin.symmetric(vertical=16)
                )
            )
        else:
            # Compact header with enhanced icon
            header_content = [
                ft.Container(
                    content=ft.Container(
                        content=ft.Icon(
                            ft.Icons.ANALYTICS_ROUNDED,
                            size=config['icon_size'],
                            color=ft.Colors.WHITE
                        ),
                        width=40,
                        height=40,
                        bgcolor=self.theme.get_semantic_color('primary'),
                        border_radius=10,
                        alignment=ft.alignment.center
                    ),
                    alignment=ft.alignment.center,
                    padding=ft.padding.symmetric(vertical=12)
                )
            ]

        # Enhanced toggle button
        toggle_button = ft.Container(
            content=ft.IconButton(
                icon=ft.Icons.MENU_OPEN_ROUNDED if self.current_state == NavigationState.EXPANDED else ft.Icons.MENU_ROUNDED,
                icon_size=22,
                on_click=self._toggle_sidebar,
                tooltip="Toggle Sidebar",
                style=ft.ButtonStyle(
                    color=self.theme.get_text_colors()['secondary'],
                    bgcolor=ft.Colors.TRANSPARENT,
                    overlay_color=self.theme.get_semantic_color('neutral', '100')
                )
            ),
            border_radius=8,
            bgcolor=self.theme.get_semantic_color('neutral', '50'),
            border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200'))
        )

        if config['show_labels']:
            header_content.append(
                ft.Row([
                    ft.Container(expand=True),
                    toggle_button
                ], alignment=ft.MainAxisAlignment.END)
            )
        else:
            header_content.append(
                ft.Container(
                    content=toggle_button,
                    alignment=ft.alignment.center,
                    margin=ft.margin.only(top=8)
                )
            )

        return ft.Container(
            content=ft.Column(header_content, spacing=0),
            padding=ft.padding.symmetric(horizontal=config['padding'])
        )
    
    def _create_navigation_items(self, config: Dict[str, Any]) -> ft.Control:
        """Create scrollable navigation items list."""
        nav_items = []

        for item in self.navigation_items:
            nav_item = self._create_navigation_item(item, config)
            nav_items.append(nav_item)

            # Add children if expanded and has children
            if (item.children and config['show_labels'] and
                self.selected_item_id and self.selected_item_id.startswith(item.id)):
                for child in item.children:
                    child_item = self._create_navigation_item(child, config, is_child=True)
                    nav_items.append(child_item)

        # Create scrollable column for navigation items
        navigation_column = ft.Column(
            controls=nav_items,
            spacing=4,
            scroll=ft.ScrollMode.AUTO,  # Enable scrolling
            expand=True
        )

        return ft.Container(
            content=navigation_column,
            padding=ft.padding.symmetric(horizontal=config['padding']),
            expand=True
        )
    
    def _create_navigation_item(self, item: NavigationItem, config: Dict[str, Any],
                               is_child: bool = False) -> ft.Control:
        """Create individual navigation item with modern styling."""
        is_selected = self.selected_item_id == item.id

        # Enhanced color scheme for selected/unselected states
        if is_selected:
            bg_color = self.theme.get_semantic_color('primary', '50')
            border_color = self.theme.get_semantic_color('primary')
            icon_color = self.theme.get_semantic_color('primary')
            text_color = self.theme.get_semantic_color('primary')
        else:
            bg_color = ft.Colors.TRANSPARENT
            border_color = ft.Colors.TRANSPARENT
            icon_color = self.theme.get_text_colors()['secondary']
            text_color = self.theme.get_text_colors()['primary']

        # Create item content
        content = []

        # Enhanced icon with container
        icon_container = ft.Container(
            content=ft.Icon(item.icon, size=config['icon_size'], color=icon_color),
            width=config['icon_size'] + 8,
            height=config['icon_size'] + 8,
            alignment=ft.alignment.center
        )
        content.append(icon_container)
        
        # Enhanced label and badge (if expanded)
        if config['show_labels']:
            label_content = [
                ft.Text(
                    item.label,
                    size=14,
                    weight=ft.FontWeight.W_600 if is_selected else ft.FontWeight.W_500,
                    color=text_color,
                    expand=True
                )
            ]

            # Enhanced badge with modern styling
            if item.badge:
                badge = ft.Container(
                    content=ft.Text(
                        item.badge,
                        size=10,
                        weight=ft.FontWeight.W_600,
                        color=ft.Colors.WHITE
                    ),
                    bgcolor=self.theme.get_semantic_color('success') if item.badge == "New" else self.theme.get_semantic_color('primary'),
                    border_radius=12,
                    padding=ft.padding.symmetric(horizontal=8, vertical=3),
                    shadow=ft.BoxShadow(
                        spread_radius=0,
                        blur_radius=4,
                        offset=ft.Offset(0, 1),
                        color=self.theme.get_semantic_color('neutral', '200')
                    )
                )
                label_content.append(badge)

            content.append(
                ft.Row(label_content, spacing=8, expand=True)
            )

        # Create click handler with proper closure
        def create_click_handler(nav_item):
            return lambda _: self._handle_item_click(nav_item)

        # Enhanced clickable container with modern styling
        item_container = ft.Container(
            content=ft.Row(content, spacing=12, alignment=ft.MainAxisAlignment.START),
            padding=ft.padding.symmetric(
                horizontal=16 if config['show_labels'] else 12,
                vertical=14
            ),
            margin=ft.margin.only(
                left=20 if is_child else 0,
                bottom=2
            ),
            bgcolor=bg_color,
            border=ft.border.all(1, border_color) if is_selected else None,
            border_radius=self.theme.tokens.radius['lg'],
            ink=True,
            on_click=create_click_handler(item),
            animate=ft.Animation(200, ft.AnimationCurve.EASE_OUT),
            tooltip=item.tooltip if not config['show_labels'] else None,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                offset=ft.Offset(0, 1),
                color=self.theme.get_semantic_color('neutral', '100')
            ) if is_selected else None,
            # Ensure the container is properly sized for clicking
            height=50 if config['show_labels'] else 44
        )
        
        # Add hover effect
        if not is_selected:
            item_container.animate_opacity = ft.Animation(200, ft.AnimationCurve.EASE_OUT)
        
        return item_container
    
    def _create_footer(self, config: Dict[str, Any]) -> ft.Control:
        """Create compact modern sidebar footer with enhanced user profile."""
        if not self.show_user_profile:
            return ft.Container()

        if config['show_labels']:
            # Compact user profile with modern styling
            avatar = ft.Container(
                content=ft.CircleAvatar(
                    content=ft.Text(
                        self.user_name[0].upper(),
                        color=ft.Colors.WHITE,
                        size=14,
                        weight=ft.FontWeight.W_700
                    ),
                    bgcolor=self.theme.get_semantic_color('primary'),
                    radius=18
                ) if not self.user_avatar else ft.CircleAvatar(
                    foreground_image_src=self.user_avatar,
                    radius=18
                ),
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=4,
                    offset=ft.Offset(0, 1),
                    color=self.theme.get_semantic_color('primary', '200')
                )
            )

            # Compact status indicator
            status_indicator = ft.Container(
                width=6,
                height=6,
                bgcolor=self.theme.get_semantic_color('success'),
                border_radius=3,
                border=ft.border.all(1, ft.Colors.WHITE)
            )

            return ft.Container(
                content=ft.Column([
                    # Thin separator line
                    ft.Container(
                        height=1,
                        bgcolor=self.theme.get_semantic_color('neutral', '200'),
                        margin=ft.margin.only(bottom=8)
                    ),
                    # Compact user profile section
                    ft.Row([
                        ft.Container(
                            content=ft.Stack([
                                avatar,
                                ft.Container(
                                    content=status_indicator,
                                    alignment=ft.alignment.bottom_right,
                                    margin=ft.margin.only(right=2, bottom=2)
                                )
                            ]),
                            width=40,
                            height=40
                        ),
                        ft.Column([
                            ft.Text(
                                self.user_name,
                                size=13,
                                weight=ft.FontWeight.W_600,
                                color=self.theme.get_text_colors()['primary']
                            ),
                            ft.Text(
                                "Financial Analyst",
                                size=11,
                                color=self.theme.get_text_colors()['secondary']
                            )
                        ], spacing=0, expand=True),
                        ft.IconButton(
                            icon=ft.Icons.SETTINGS_ROUNDED,
                            icon_size=16,
                            tooltip="User Settings",
                            style=ft.ButtonStyle(
                                color=self.theme.get_text_colors()['secondary'],
                                bgcolor=ft.Colors.TRANSPARENT,
                                overlay_color=self.theme.get_semantic_color('neutral', '100')
                            )
                        )
                    ], spacing=12)
                ], spacing=0),
                padding=ft.padding.symmetric(horizontal=config['padding'], vertical=12),
                bgcolor=self.theme.get_semantic_color('neutral', '25'),
                border_radius=self.theme.tokens.radius['lg'],
                margin=ft.margin.symmetric(horizontal=config['padding']),
                border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
                height=60  # Fixed compact height
            )
        else:
            # Enhanced compact user avatar
            avatar = ft.Container(
                content=ft.CircleAvatar(
                    content=ft.Text(
                        self.user_name[0].upper(),
                        color=ft.Colors.WHITE,
                        size=14,
                        weight=ft.FontWeight.W_700
                    ),
                    bgcolor=self.theme.get_semantic_color('primary'),
                    radius=18
                ),
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=6,
                    offset=ft.Offset(0, 2),
                    color=self.theme.get_semantic_color('primary', '200')
                )
            )

            return ft.Container(
                content=ft.Container(
                    content=ft.Stack([
                        avatar,
                        ft.Container(
                            content=ft.Container(
                                width=6,
                                height=6,
                                bgcolor=self.theme.get_semantic_color('success'),
                                border_radius=3,
                                border=ft.border.all(1, ft.Colors.WHITE)
                            ),
                            alignment=ft.alignment.bottom_right,
                            margin=ft.margin.only(right=2, bottom=2)
                        )
                    ]),
                    width=40,
                    height=40
                ),
                alignment=ft.alignment.center,
                padding=ft.padding.symmetric(vertical=12)
            )
    
    def _toggle_sidebar(self, _):
        """Toggle sidebar state."""
        if self.current_state == NavigationState.EXPANDED:
            self.current_state = NavigationState.COLLAPSED
        else:
            self.current_state = NavigationState.EXPANDED
        
        if self.on_state_change:
            self.on_state_change(self.current_state)
    
    def _handle_item_click(self, item: NavigationItem):
        """Handle navigation item click."""
        if item.disabled:
            return
        
        self.selected_item_id = item.id
        
        if self.on_navigate and item.route:
            self.on_navigate(item.route)
    
    def select_item(self, item_id: str):
        """Programmatically select a navigation item."""
        self.selected_item_id = item_id
    
    def set_state(self, state: NavigationState):
        """Set sidebar state."""
        self.current_state = state
        if self.on_state_change:
            self.on_state_change(state)


class ModernBreadcrumb:
    """Modern breadcrumb navigation component."""
    
    def __init__(self,
                 items: List[Dict[str, str]],
                 on_navigate: Optional[Callable[[str], None]] = None,
                 separator: str = "/",
                 max_items: int = 4):
        
        self.items = items  # [{"label": "Home", "route": "/home"}, ...]
        self.on_navigate = on_navigate
        self.separator = separator
        self.max_items = max_items
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build the breadcrumb navigation."""
        if not self.items:
            return ft.Container()
        
        # Truncate items if too many
        display_items = self.items
        if len(self.items) > self.max_items:
            display_items = [
                self.items[0],
                {"label": "...", "route": None},
                *self.items[-(self.max_items-2):]
            ]
        
        breadcrumb_items = []
        
        for i, item in enumerate(display_items):
            is_last = i == len(display_items) - 1
            is_ellipsis = item["label"] == "..."
            
            if is_ellipsis:
                breadcrumb_items.append(
                    ft.Text("...", size=14, color=self.theme.get_text_colors()['secondary'])
                )
            elif is_last:
                # Current page (not clickable)
                breadcrumb_items.append(
                    ft.Text(item["label"], size=14, weight=ft.FontWeight.W_500,
                           color=self.theme.get_text_colors()['primary'])
                )
            else:
                # Clickable breadcrumb item
                breadcrumb_items.append(
                    ft.TextButton(
                        text=item["label"],
                        style=ft.ButtonStyle(
                            color=self.theme.get_semantic_color('primary'),
                            padding=ft.padding.symmetric(horizontal=4, vertical=2)
                        ),
                        on_click=lambda _, route=item["route"]: self._handle_click(route)
                    )
                )
            
            # Add separator (except for last item)
            if not is_last:
                breadcrumb_items.append(
                    ft.Text(self.separator, size=14, 
                           color=self.theme.get_text_colors()['tertiary'])
                )
        
        return ft.Container(
            content=ft.Row(breadcrumb_items, spacing=8),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            bgcolor=self.theme.get_background_colors()['secondary'],
            border=ft.border.only(bottom=ft.BorderSide(1, self.theme.get_semantic_color('neutral', '200')))
        )
    
    def _handle_click(self, route: Optional[str]):
        """Handle breadcrumb item click."""
        if route and self.on_navigate:
            self.on_navigate(route)


class ModernTopBar:
    """Modern top navigation bar with enhanced design and functionality."""

    def __init__(self,
                 title: str,
                 actions: Optional[List[ft.Control]] = None,
                 show_search: bool = True,
                 on_search: Optional[Callable[[str], None]] = None,
                 user_menu: Optional[List[Dict[str, Any]]] = None,
                 show_breadcrumb: bool = False,
                 breadcrumb_items: Optional[List[Dict[str, str]]] = None):

        self.title = title
        self.actions = actions or []
        self.show_search = show_search
        self.on_search = on_search
        self.user_menu = user_menu or []
        self.show_breadcrumb = show_breadcrumb
        self.breadcrumb_items = breadcrumb_items or []
        self.theme = get_theme()

    def build(self) -> ft.Control:
        """Build the modern top navigation bar with enhanced styling."""
        # Left section with title and breadcrumb
        left_content = []

        # App logo/icon
        left_content.append(
            ft.Container(
                content=ft.Icon(
                    ft.Icons.ANALYTICS_OUTLINED,
                    size=28,
                    color=self.theme.get_semantic_color('primary')
                ),
                padding=ft.padding.only(right=12)
            )
        )

        # Title with enhanced typography
        title_text = ft.Text(
            self.title,
            size=22,
            weight=ft.FontWeight.W_700,
            color=self.theme.get_text_colors()['primary']
        )
        left_content.append(title_text)

        # Breadcrumb if enabled
        if self.show_breadcrumb and self.breadcrumb_items:
            breadcrumb = self._create_breadcrumb()
            left_content.append(breadcrumb)

        # Right section with search, actions, and user menu
        right_content = []

        # Enhanced search bar with modern styling
        if self.show_search:
            search_field = ft.Container(
                content=ft.TextField(
                    hint_text="Search projects, analyses...",
                    hint_style=ft.TextStyle(
                        color=self.theme.get_text_colors()['secondary'],
                        size=14
                    ),
                    prefix_icon=ft.Icons.SEARCH_ROUNDED,
                    border=ft.InputBorder.OUTLINE,
                    border_radius=self.theme.tokens.radius['lg'],
                    border_color=self.theme.get_semantic_color('neutral', '300'),
                    focused_border_color=self.theme.get_semantic_color('primary'),
                    height=44,
                    width=320,
                    content_padding=ft.padding.symmetric(horizontal=16, vertical=12),
                    on_submit=lambda e: self.on_search(e.control.value) if self.on_search else None,
                    text_style=ft.TextStyle(size=14)
                ),
                margin=ft.margin.only(right=16)
            )
            right_content.append(search_field)

        # Action buttons with enhanced styling
        if self.actions:
            actions_row = ft.Row(
                controls=self.actions,
                spacing=8,
                alignment=ft.MainAxisAlignment.END
            )
            right_content.append(actions_row)

        # Enhanced user menu
        if self.user_menu:
            user_avatar = ft.Container(
                content=ft.Row([
                    ft.Icon(
                        ft.Icons.ACCOUNT_CIRCLE_ROUNDED,
                        size=32,
                        color=self.theme.get_semantic_color('primary')
                    ),
                    ft.Column([
                        ft.Text(
                            "Financial Analyst",
                            size=12,
                            weight=ft.FontWeight.W_600,
                            color=self.theme.get_text_colors()['primary']
                        ),
                        ft.Text(
                            "Hiel RnE Modeler",
                            size=10,
                            color=self.theme.get_text_colors()['secondary']
                        )
                    ], spacing=0)
                ], spacing=8),
                padding=ft.padding.symmetric(horizontal=12, vertical=6),
                border_radius=self.theme.tokens.radius['lg'],
                bgcolor=self.theme.get_semantic_color('neutral', '50'),
                border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
                on_click=self._show_user_menu,
                ink=True
            )
            right_content.append(user_avatar)

        # Main header container with gradient background
        header_content = ft.Row([
            ft.Row(left_content, spacing=0, expand=True),
            ft.Row(right_content, spacing=12)
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)

        return ft.Container(
            content=header_content,
            padding=ft.padding.symmetric(horizontal=24, vertical=16),
            bgcolor=self.theme.get_background_colors()['surface'],
            border=ft.border.only(
                bottom=ft.BorderSide(
                    1,
                    self.theme.get_semantic_color('neutral', '200')
                )
            ),
            height=80,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                offset=ft.Offset(0, 2),
                color=self.theme.get_semantic_color('neutral', '100')
            )
        )

    def _create_breadcrumb(self) -> ft.Control:
        """Create breadcrumb navigation."""
        breadcrumb_items = []

        for i, item in enumerate(self.breadcrumb_items):
            if i > 0:
                breadcrumb_items.append(
                    ft.Icon(
                        ft.Icons.CHEVRON_RIGHT,
                        size=16,
                        color=self.theme.get_text_colors()['secondary']
                    )
                )

            is_last = i == len(self.breadcrumb_items) - 1
            breadcrumb_items.append(
                ft.Text(
                    item.get('label', ''),
                    size=14,
                    weight=ft.FontWeight.W_500 if is_last else ft.FontWeight.W_400,
                    color=self.theme.get_text_colors()['primary'] if is_last
                          else self.theme.get_text_colors()['secondary']
                )
            )

        return ft.Container(
            content=ft.Row(breadcrumb_items, spacing=4),
            margin=ft.margin.only(left=16, top=4)
        )

    def _show_user_menu(self, e):
        """Show user menu dropdown."""
        # Placeholder for user menu functionality
        _ = e  # Suppress unused parameter warning
        pass


# Utility functions
def create_navigation_items() -> List[NavigationItem]:
    """Create modern navigation items for the financial application with enhanced icons."""
    return [
        NavigationItem(
            id="dashboard",
            label="Dashboard",
            icon=ft.Icons.DASHBOARD_ROUNDED,
            route="/dashboard",
            tooltip="Main dashboard overview"
        ),
        NavigationItem(
            id="setup",
            label="Project Setup",
            icon=ft.Icons.SETTINGS_ROUNDED,
            route="/setup",
            tooltip="Configure project parameters"
        ),
        NavigationItem(
            id="financial",
            label="Financial Model",
            icon=ft.Icons.ACCOUNT_BALANCE_ROUNDED,
            route="/analysis/financial",
            tooltip="DCF financial modeling and analysis"
        ),
        NavigationItem(
            id="monte_carlo",
            label="Monte Carlo",
            icon=ft.Icons.SCATTER_PLOT_ROUNDED,
            route="/analysis/monte_carlo",
            badge="Risk",
            badge_variant="primary",
            tooltip="Risk analysis through probabilistic modeling"
        ),
        NavigationItem(
            id="analysis",
            label="More Analysis",
            icon=ft.Icons.ANALYTICS_ROUNDED,
            route="/analysis",
            children=[
                NavigationItem(id="sensitivity", label="Sensitivity Analysis",
                             icon=ft.Icons.TUNE_ROUNDED, route="/analysis/sensitivity"),
                NavigationItem(id="scenarios", label="Scenarios",
                             icon=ft.Icons.COMPARE_ROUNDED, route="/analysis/scenarios")
            ],
            tooltip="Additional analysis tools"
        ),
        NavigationItem(
            id="locations",
            label="Locations",
            icon=ft.Icons.LOCATION_ON_ROUNDED,
            route="/locations",
            tooltip="Location comparison analysis"
        ),
        NavigationItem(
            id="validation",
            label="Validation",
            icon=ft.Icons.VERIFIED_ROUNDED,
            route="/validation",
            badge="New",
            badge_variant="success",
            tooltip="Model validation and benchmarks"
        ),
        NavigationItem(
            id="export",
            label="Export & Reports",
            icon=ft.Icons.DOWNLOAD_ROUNDED,
            route="/export",
            tooltip="Generate and export reports"
        )
    ]
