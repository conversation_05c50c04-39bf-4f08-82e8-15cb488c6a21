"""
Theme Manager
=============

Advanced theme management for the application.
"""

import flet as ft
from typing import Dict, Any, Optional
from enum import Enum


class ThemeMode(Enum):
    """Theme mode enumeration."""
    LIGHT = "light"
    DARK = "dark"
    SYSTEM = "system"


class ColorScheme:
    """Color scheme definitions."""
    
    LIGHT_SCHEME = {
        'primary': ft.Colors.BLUE_600,
        'primary_variant': ft.Colors.BLUE_800,
        'secondary': ft.Colors.ORANGE_600,
        'secondary_variant': ft.Colors.ORANGE_800,
        'background': ft.Colors.WHITE,
        'surface': ft.Colors.GREY_50,
        'error': ft.Colors.RED_600,
        'success': ft.Colors.GREEN_600,
        'warning': ft.Colors.ORANGE_600,
        'info': ft.Colors.BLUE_600,
        'on_primary': ft.Colors.WHITE,
        'on_secondary': ft.Colors.WHITE,
        'on_background': ft.Colors.BLACK,
        'on_surface': ft.Colors.BLACK87,
        'on_error': ft.Colors.WHITE,
        'text_primary': ft.Colors.BLACK87,
        'text_secondary': ft.Colors.BLACK54,
        'divider': ft.Colors.GREY_300,
        'border': ft.Colors.GREY_400
    }
    
    DARK_SCHEME = {
        'primary': ft.Colors.BLUE_400,
        'primary_variant': ft.Colors.BLUE_600,
        'secondary': ft.Colors.ORANGE_400,
        'secondary_variant': ft.Colors.ORANGE_600,
        'background': ft.Colors.GREY_900,
        'surface': ft.Colors.GREY_800,
        'error': ft.Colors.RED_400,
        'success': ft.Colors.GREEN_400,
        'warning': ft.Colors.ORANGE_400,
        'info': ft.Colors.BLUE_400,
        'on_primary': ft.Colors.BLACK,
        'on_secondary': ft.Colors.BLACK,
        'on_background': ft.Colors.WHITE,
        'on_surface': ft.Colors.WHITE,
        'on_error': ft.Colors.BLACK,
        'text_primary': ft.Colors.WHITE,
        'text_secondary': ft.Colors.WHITE70,
        'divider': ft.Colors.GREY_600,
        'border': ft.Colors.GREY_500
    }


class ThemeManager:
    """Advanced theme management system."""
    
    def __init__(self):
        self.current_mode = ThemeMode.LIGHT
        self.custom_colors: Dict[str, str] = {}
        self.callbacks: list = []
    
    def set_theme_mode(self, mode: ThemeMode):
        """Set the current theme mode."""
        self.current_mode = mode
        self._notify_callbacks()
    
    def get_current_colors(self) -> Dict[str, str]:
        """Get current color scheme based on theme mode."""
        if self.current_mode == ThemeMode.DARK:
            colors = ColorScheme.DARK_SCHEME.copy()
        else:
            colors = ColorScheme.LIGHT_SCHEME.copy()
        
        # Apply custom colors
        colors.update(self.custom_colors)
        return colors
    
    def set_custom_color(self, color_name: str, color_value: str):
        """Set a custom color override."""
        self.custom_colors[color_name] = color_value
        self._notify_callbacks()
    
    def get_flet_theme(self) -> ft.Theme:
        """Get Flet theme configuration."""
        colors = self.get_current_colors()
        
        return ft.Theme(
            color_scheme_seed=colors['primary'],
            use_material3=True
        )
    
    def get_page_theme_mode(self) -> ft.ThemeMode:
        """Get Flet page theme mode."""
        if self.current_mode == ThemeMode.DARK:
            return ft.ThemeMode.DARK
        elif self.current_mode == ThemeMode.LIGHT:
            return ft.ThemeMode.LIGHT
        else:
            return ft.ThemeMode.SYSTEM
    
    def register_callback(self, callback):
        """Register a callback for theme changes."""
        self.callbacks.append(callback)
    
    def unregister_callback(self, callback):
        """Unregister a theme change callback."""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def _notify_callbacks(self):
        """Notify all registered callbacks of theme changes."""
        for callback in self.callbacks:
            try:
                callback(self.current_mode, self.get_current_colors())
            except Exception:
                pass  # Ignore callback errors
    
    def get_component_style(self, component_type: str) -> Dict[str, Any]:
        """Get styling for specific component types."""
        colors = self.get_current_colors()
        
        styles = {
            'card': {
                'bgcolor': colors['surface'],
                'border': ft.border.all(1, colors['border']),
                'border_radius': 8,
                'elevation': 2
            },
            'button_primary': {
                'bgcolor': colors['primary'],
                'color': colors['on_primary'],
                'style': ft.ButtonStyle(
                    shape=ft.RoundedRectangleBorder(radius=8)
                )
            },
            'button_secondary': {
                'bgcolor': colors['secondary'],
                'color': colors['on_secondary'],
                'style': ft.ButtonStyle(
                    shape=ft.RoundedRectangleBorder(radius=8)
                )
            },
            'text_field': {
                'border_color': colors['border'],
                'focused_border_color': colors['primary'],
                'bgcolor': colors['surface']
            },
            'container': {
                'bgcolor': colors['background'],
                'border': ft.border.all(1, colors['border'])
            },
            'divider': {
                'color': colors['divider']
            }
        }
        
        return styles.get(component_type, {})
    
    def create_themed_card(self, content: ft.Control, title: str = "") -> ft.Card:
        """Create a themed card component."""
        colors = self.get_current_colors()
        style = self.get_component_style('card')
        
        card_content = content
        if title:
            card_content = ft.Column([
                ft.Text(title, size=16, weight=ft.FontWeight.BOLD, 
                       color=colors['text_primary']),
                ft.Divider(color=colors['divider']),
                content
            ])
        
        return ft.Card(
            content=ft.Container(
                content=card_content,
                padding=15,
                **style
            )
        )
    
    def create_themed_button(self, text: str, on_click, 
                           button_type: str = "primary", 
                           icon: str = None) -> ft.ElevatedButton:
        """Create a themed button."""
        style = self.get_component_style(f'button_{button_type}')
        
        return ft.ElevatedButton(
            text=text,
            icon=icon,
            on_click=on_click,
            **style
        )
    
    def apply_theme_to_page(self, page: ft.Page):
        """Apply current theme to a Flet page."""
        page.theme = self.get_flet_theme()
        page.theme_mode = self.get_page_theme_mode()
        
        colors = self.get_current_colors()
        page.bgcolor = colors['background']
