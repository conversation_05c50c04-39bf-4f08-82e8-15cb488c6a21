"""
Modern UI Components
===================

Enhanced UI components using the modern theme system with 2025 design standards.
"""

import flet as ft
from typing import Optional, Callable, Dict, Any, List, Union
from enum import Enum

from components.ui.modern_theme_system import get_theme, ComponentSize, ModernThemeSystem


class ButtonVariant(Enum):
    """Button style variants."""
    PRIMARY = "primary"
    SECONDARY = "secondary" 
    GHOST = "ghost"
    DESTRUCTIVE = "destructive"
    SUCCESS = "success"
    WARNING = "warning"


class ModernButton:
    """Enhanced button component with modern styling."""
    
    def __init__(self,
                 text: str,
                 variant: ButtonVariant = ButtonVariant.PRIMARY,
                 size: ComponentSize = ComponentSize.MD,
                 icon: Optional[str] = None,
                 icon_position: str = "left",
                 loading: bool = False,
                 disabled: bool = False,
                 full_width: bool = False,
                 on_click: Optional[Callable] = None,
                 tooltip: Optional[str] = None):
        
        self.text = text
        self.variant = variant
        self.size = size
        self.icon = icon
        self.icon_position = icon_position
        self.loading = loading
        self.disabled = disabled
        self.full_width = full_width
        self.on_click = on_click
        self.tooltip = tooltip
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build the modern button."""
        # Get size configuration
        size_config = self.theme.tokens.component_sizes[self.size]
        
        # Create button style based on variant
        button_style = self._get_button_style()
        
        # Create button content
        content = self._create_button_content()
        
        # Handle loading state
        if self.loading:
            content = ft.Row([
                ft.ProgressRing(width=16, height=16, stroke_width=2, 
                               color=button_style.get('text_color', ft.Colors.WHITE)),
                ft.Text("Loading...", size=size_config['font_size'], 
                       color=button_style.get('text_color', ft.Colors.WHITE))
            ], alignment=ft.MainAxisAlignment.CENTER, spacing=8)
        
        # Create the button
        button = ft.ElevatedButton(
            content=content,
            style=ft.ButtonStyle(
                bgcolor=button_style.get('bgcolor'),
                color=button_style.get('text_color'),
                overlay_color=button_style.get('overlay_color'),
                elevation=button_style.get('elevation'),
                shape=ft.RoundedRectangleBorder(radius=self.theme.tokens.radius['md']),
                side=button_style.get('border'),
                padding=ft.padding.symmetric(
                    horizontal=size_config['padding_x'],
                    vertical=size_config['padding_y']
                )
            ),
            height=size_config['height'],
            width=None if not self.full_width else float('inf'),
            disabled=self.disabled or self.loading,
            on_click=self.on_click,
            tooltip=self.tooltip
        )
        
        return button
    
    def _get_button_style(self) -> Dict[str, Any]:
        """Get button style based on variant."""
        if self.variant == ButtonVariant.PRIMARY:
            return {
                'bgcolor': {
                    ft.ControlState.DEFAULT: self.theme.get_semantic_color('primary'),
                    ft.ControlState.HOVERED: self.theme.get_semantic_color('primary', '600'),
                    ft.ControlState.PRESSED: self.theme.get_semantic_color('primary', '700'),
                    ft.ControlState.DISABLED: self.theme.get_semantic_color('neutral', '300')
                },
                'text_color': self.theme.get_text_colors()['inverse'],
                'elevation': {ft.ControlState.DEFAULT: 2, ft.ControlState.HOVERED: 4},
                'overlay_color': {ft.ControlState.PRESSED: ft.Colors.with_opacity(0.1, ft.Colors.WHITE)}
            }
        
        elif self.variant == ButtonVariant.SECONDARY:
            return {
                'bgcolor': {
                    ft.ControlState.DEFAULT: 'transparent',
                    ft.ControlState.HOVERED: self.theme.get_semantic_color('primary', '50'),
                    ft.ControlState.PRESSED: self.theme.get_semantic_color('primary', '100')
                },
                'text_color': self.theme.get_semantic_color('primary'),
                'border': ft.BorderSide(1, self.theme.get_semantic_color('primary')),
                'elevation': {ft.ControlState.DEFAULT: 0}
            }
        
        elif self.variant == ButtonVariant.GHOST:
            return {
                'bgcolor': {
                    ft.ControlState.DEFAULT: 'transparent',
                    ft.ControlState.HOVERED: self.theme.get_semantic_color('neutral', '100'),
                    ft.ControlState.PRESSED: self.theme.get_semantic_color('neutral', '200')
                },
                'text_color': self.theme.get_text_colors()['primary'],
                'elevation': {ft.ControlState.DEFAULT: 0}
            }
        
        elif self.variant == ButtonVariant.DESTRUCTIVE:
            return {
                'bgcolor': {
                    ft.ControlState.DEFAULT: self.theme.get_semantic_color('error'),
                    ft.ControlState.HOVERED: self.theme.get_semantic_color('error', '600'),
                    ft.ControlState.PRESSED: self.theme.get_semantic_color('error', '700')
                },
                'text_color': self.theme.get_text_colors()['inverse'],
                'elevation': {ft.ControlState.DEFAULT: 2, ft.ControlState.HOVERED: 4}
            }
        
        elif self.variant == ButtonVariant.SUCCESS:
            return {
                'bgcolor': {
                    ft.ControlState.DEFAULT: self.theme.get_semantic_color('success'),
                    ft.ControlState.HOVERED: self.theme.get_semantic_color('success', '600'),
                    ft.ControlState.PRESSED: self.theme.get_semantic_color('success', '700')
                },
                'text_color': self.theme.get_text_colors()['inverse'],
                'elevation': {ft.ControlState.DEFAULT: 2, ft.ControlState.HOVERED: 4}
            }
        
        return {}
    
    def _create_button_content(self) -> ft.Control:
        """Create button content with icon and text."""
        size_config = self.theme.tokens.component_sizes[self.size]
        icon_size = size_config['font_size'] + 2
        
        if not self.icon:
            return ft.Text(self.text, size=size_config['font_size'], weight=ft.FontWeight.W_500)
        
        icon_widget = ft.Icon(self.icon, size=icon_size)
        text_widget = ft.Text(self.text, size=size_config['font_size'], weight=ft.FontWeight.W_500)
        
        if self.icon_position == "left":
            return ft.Row([icon_widget, text_widget], spacing=8, 
                         alignment=ft.MainAxisAlignment.CENTER)
        else:
            return ft.Row([text_widget, icon_widget], spacing=8, 
                         alignment=ft.MainAxisAlignment.CENTER)


class ModernCard:
    """Enhanced card component with modern styling."""
    
    def __init__(self,
                 content: ft.Control,
                 title: Optional[str] = None,
                 subtitle: Optional[str] = None,
                 header_actions: Optional[List[ft.Control]] = None,
                 variant: str = "default",
                 padding: Optional[str] = None,
                 elevated: bool = True,
                 hoverable: bool = False,
                 clickable: bool = False,
                 on_click: Optional[Callable] = None,
                 full_width: bool = True):
        
        self.content = content
        self.title = title
        self.subtitle = subtitle
        self.header_actions = header_actions or []
        self.variant = variant
        self.padding = padding or 'lg'
        self.elevated = elevated
        self.hoverable = hoverable
        self.clickable = clickable
        self.on_click = on_click
        self.full_width = full_width
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build the modern card."""
        # Create card style
        card_style = self._get_card_style()
        
        # Create header if title is provided
        header = self._create_header() if self.title else None
        
        # Create card content
        card_content = []
        if header:
            card_content.extend([header, ft.Divider(height=1, color=self.theme.get_semantic_color('neutral', '200'))])
        
        card_content.append(self.content)
        
        # Create container
        container = ft.Container(
            content=ft.Column(card_content, spacing=self.theme.tokens.spacing[self.padding] // 2),
            padding=self.theme.tokens.spacing[self.padding],
            **card_style
        )
        
        # Add click handler if needed
        if self.clickable and self.on_click:
            container.on_click = self.on_click
            container.ink = True
        
        # Add hover effect if needed
        if self.hoverable:
            container.animate_scale = ft.Animation(200, ft.AnimationCurve.EASE_OUT)
        
        if self.elevated:
            return ft.Card(
                content=container,
                elevation=4 if self.elevated else 0,
                margin=0
            )
        else:
            return container
    
    def _get_card_style(self) -> Dict[str, Any]:
        """Get card styling based on variant."""
        backgrounds = self.theme.get_background_colors()
        
        style = {
            'bgcolor': backgrounds['surface'],
            'border_radius': self.theme.tokens.radius['lg'],
            'width': None if not self.full_width else float('inf')
        }
        
        if self.variant == "outlined":
            style.update({
                'border': ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
                'bgcolor': backgrounds['primary']
            })
        elif self.variant == "filled":
            style.update({
                'bgcolor': backgrounds['secondary']
            })
        elif self.variant == "primary":
            style.update({
                'bgcolor': self.theme.get_semantic_color('primary', '50'),
                'border': ft.border.all(1, self.theme.get_semantic_color('primary', '200'))
            })
        
        return style
    
    def _create_header(self) -> ft.Control:
        """Create card header with title, subtitle, and actions."""
        header_content = []
        
        # Title and subtitle column
        title_column = []
        if self.title:
            title_style = self.theme.get_typography_style('title_md')
            title_column.append(
                ft.Text(self.title, size=title_style['size'], 
                       weight=title_style['weight'], color=title_style['color'])
            )
        
        if self.subtitle:
            subtitle_style = self.theme.get_typography_style('body_sm')
            title_column.append(
                ft.Text(self.subtitle, size=subtitle_style['size'],
                       color=self.theme.get_text_colors()['secondary'])
            )
        
        header_content.append(
            ft.Column(title_column, spacing=4, expand=True)
        )
        
        # Header actions
        if self.header_actions:
            header_content.append(
                ft.Row(self.header_actions, spacing=8)
            )
        
        return ft.Row(header_content, alignment=ft.MainAxisAlignment.SPACE_BETWEEN)


class ModernInput:
    """Enhanced input field component."""
    
    def __init__(self,
                 label: str,
                 value: str = "",
                 placeholder: Optional[str] = None,
                 helper_text: Optional[str] = None,
                 error_text: Optional[str] = None,
                 prefix_icon: Optional[str] = None,
                 suffix_icon: Optional[str] = None,
                 input_type: str = "text",
                 size: ComponentSize = ComponentSize.MD,
                 disabled: bool = False,
                 required: bool = False,
                 multiline: bool = False,
                 max_lines: int = 1,
                 on_change: Optional[Callable] = None,
                 on_submit: Optional[Callable] = None):
        
        self.label = label
        self.value = value
        self.placeholder = placeholder
        self.helper_text = helper_text
        self.error_text = error_text
        self.prefix_icon = prefix_icon
        self.suffix_icon = suffix_icon
        self.input_type = input_type
        self.size = size
        self.disabled = disabled
        self.required = required
        self.multiline = multiline
        self.max_lines = max_lines
        self.on_change = on_change
        self.on_submit = on_submit
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build the modern input field."""
        # Determine input state
        state = "error" if self.error_text else "default"
        input_style = self.theme.create_input_style(self.size, state)
        
        # Create input field
        if self.input_type == "password":
            field = ft.TextField(
                label=self._create_label(),
                value=self.value,
                hint_text=self.placeholder,
                password=True,
                can_reveal_password=True,
                disabled=self.disabled,
                multiline=self.multiline,
                max_lines=self.max_lines,
                on_change=self.on_change,
                on_submit=self.on_submit,
                **self._get_field_style(input_style)
            )
        elif self.input_type == "email":
            field = ft.TextField(
                label=self._create_label(),
                value=self.value,
                hint_text=self.placeholder,
                keyboard_type=ft.KeyboardType.EMAIL,
                disabled=self.disabled,
                on_change=self.on_change,
                on_submit=self.on_submit,
                **self._get_field_style(input_style)
            )
        elif self.input_type == "number":
            field = ft.TextField(
                label=self._create_label(),
                value=self.value,
                hint_text=self.placeholder,
                keyboard_type=ft.KeyboardType.NUMBER,
                disabled=self.disabled,
                on_change=self.on_change,
                on_submit=self.on_submit,
                **self._get_field_style(input_style)
            )
        else:
            field = ft.TextField(
                label=self._create_label(),
                value=self.value,
                hint_text=self.placeholder,
                disabled=self.disabled,
                multiline=self.multiline,
                max_lines=self.max_lines,
                on_change=self.on_change,
                on_submit=self.on_submit,
                **self._get_field_style(input_style)
            )
        
        # Add icons if provided
        if self.prefix_icon:
            field.prefix_icon = self.prefix_icon
        if self.suffix_icon:
            field.suffix_icon = self.suffix_icon
        
        # Create container with helper/error text
        content = [field]
        
        if self.error_text:
            content.append(
                ft.Text(self.error_text, size=12, 
                       color=self.theme.get_semantic_color('error'),
                       style=ft.TextStyle(italic=True))
            )
        elif self.helper_text:
            content.append(
                ft.Text(self.helper_text, size=12,
                       color=self.theme.get_text_colors()['secondary'])
            )
        
        return ft.Column(content, spacing=4)
    
    def _create_label(self) -> str:
        """Create field label with required indicator."""
        if self.required:
            return f"{self.label} *"
        return self.label
    
    def _get_field_style(self, input_style: Dict[str, Any]) -> Dict[str, Any]:
        """Get field styling."""
        return {
            'height': input_style.get('height'),
            'bgcolor': input_style.get('bgcolor'),
            'border_color': input_style.get('border_color'),
            'focused_border_color': input_style.get('focused_border_color'),
            'color': input_style.get('color'),
            'content_padding': input_style.get('content_padding'),
            'border_radius': input_style.get('border_radius', self.theme.tokens.radius['md'])
        }


class ModernBadge:
    """Modern badge/chip component."""
    
    def __init__(self,
                 text: str,
                 variant: str = "default",
                 size: ComponentSize = ComponentSize.SM,
                 icon: Optional[str] = None,
                 removable: bool = False,
                 on_remove: Optional[Callable] = None):
        
        self.text = text
        self.variant = variant
        self.size = size
        self.icon = icon
        self.removable = removable
        self.on_remove = on_remove
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build the badge."""
        # Get styling
        badge_style = self._get_badge_style()
        size_config = self.theme.tokens.component_sizes[self.size]
        
        # Create content
        content = []
        
        if self.icon:
            content.append(ft.Icon(self.icon, size=size_config['font_size'], 
                                 color=badge_style['text_color']))
        
        content.append(ft.Text(self.text, size=size_config['font_size'],
                              color=badge_style['text_color'], weight=ft.FontWeight.W_500))
        
        if self.removable and self.on_remove:
            content.append(
                ft.IconButton(
                    icon=ft.Icons.CLOSE,
                    icon_size=size_config['font_size'],
                    icon_color=badge_style['text_color'],
                    on_click=self.on_remove,
                    style=ft.ButtonStyle(padding=0)
                )
            )
        
        return ft.Container(
            content=ft.Row(content, spacing=4, alignment=ft.MainAxisAlignment.CENTER),
            padding=ft.padding.symmetric(
                horizontal=size_config['padding_x'],
                vertical=size_config['padding_y'] // 2
            ),
            bgcolor=badge_style['bg_color'],
            border=badge_style.get('border'),
            border_radius=self.theme.tokens.radius['full']
        )
    
    def _get_badge_style(self) -> Dict[str, Any]:
        """Get badge styling based on variant."""
        if self.variant == "primary":
            return {
                'bg_color': self.theme.get_semantic_color('primary'),
                'text_color': self.theme.get_text_colors()['inverse']
            }
        elif self.variant == "secondary":
            return {
                'bg_color': self.theme.get_semantic_color('neutral', '100'),
                'text_color': self.theme.get_text_colors()['primary']
            }
        elif self.variant == "success":
            return {
                'bg_color': self.theme.get_semantic_color('success'),
                'text_color': self.theme.get_text_colors()['inverse']
            }
        elif self.variant == "error":
            return {
                'bg_color': self.theme.get_semantic_color('error'),
                'text_color': self.theme.get_text_colors()['inverse']
            }
        elif self.variant == "warning":
            return {
                'bg_color': self.theme.get_semantic_color('warning'),
                'text_color': self.theme.get_text_colors()['inverse']
            }
        elif self.variant == "outlined":
            return {
                'bg_color': 'transparent',
                'text_color': self.theme.get_text_colors()['primary'],
                'border': ft.border.all(1, self.theme.get_semantic_color('neutral', '300'))
            }
        else:  # default
            return {
                'bg_color': self.theme.get_semantic_color('neutral', '100'),
                'text_color': self.theme.get_text_colors()['secondary']
            }


# Utility functions for quick component creation
def create_modern_button(text: str, 
                        variant: ButtonVariant = ButtonVariant.PRIMARY,
                        **kwargs) -> ft.Control:
    """Quick function to create a modern button."""
    return ModernButton(text, variant, **kwargs).build()


def create_modern_card(content: ft.Control, 
                      title: Optional[str] = None,
                      **kwargs) -> ft.Control:
    """Quick function to create a modern card."""
    return ModernCard(content, title, **kwargs).build()


def create_modern_input(label: str, 
                       **kwargs) -> ft.Control:
    """Quick function to create a modern input."""
    return ModernInput(label, **kwargs).build()


def create_modern_badge(text: str, 
                       variant: str = "default",
                       **kwargs) -> ft.Control:
    """Quick function to create a modern badge."""
    return ModernBadge(text, variant, **kwargs).build() 