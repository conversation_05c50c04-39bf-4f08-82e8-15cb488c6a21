"""
Modern Theme System
==================

Comprehensive theme system with 2025 design standards and design tokens.
"""

import flet as ft
from typing import Dict, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass


class ColorIntensity(Enum):
    """Color intensity levels for semantic design."""
    _50 = "50"
    _100 = "100"
    _200 = "200"
    _300 = "300"
    _400 = "400"
    _500 = "500"
    _600 = "600"
    _700 = "700"
    _800 = "800"
    _900 = "900"
    _950 = "950"


class ComponentSize(Enum):
    """Standard component sizes."""
    XS = "xs"
    SM = "sm"
    MD = "md"
    LG = "lg"
    XL = "xl"


@dataclass
class DesignTokens:
    """Modern design tokens following 2025 standards with enhanced accessibility."""
    
    # Enhanced spacing system (4px base grid for finer control)
    spacing = {
        'xxs': 2,    # 0.125rem - micro spacing
        'xs': 4,     # 0.25rem - tight spacing
        'sm': 8,     # 0.5rem - small spacing
        'md': 12,    # 0.75rem - medium-small spacing
        'lg': 16,    # 1rem - standard spacing
        'xl': 24,    # 1.5rem - large spacing
        'xxl': 32,   # 2rem - extra large spacing
        'xxxl': 48,  # 3rem - huge spacing
        'xxxxl': 64, # 4rem - massive spacing
        'xxxxxl': 96 # 6rem - maximum spacing
    }
    
    # Enhanced typography system with improved hierarchy and accessibility
    typography = {
        'display_xl': {'size': 72, 'weight': ft.FontWeight.W_800, 'line_height': 1.1, 'letter_spacing': -0.5},
        'display_lg': {'size': 60, 'weight': ft.FontWeight.W_800, 'line_height': 1.1, 'letter_spacing': -0.25},
        'display_md': {'size': 48, 'weight': ft.FontWeight.W_700, 'line_height': 1.2, 'letter_spacing': 0},
        'display_sm': {'size': 36, 'weight': ft.FontWeight.W_700, 'line_height': 1.2, 'letter_spacing': 0},
        'headline_xl': {'size': 32, 'weight': ft.FontWeight.W_600, 'line_height': 1.25, 'letter_spacing': 0},
        'headline_lg': {'size': 28, 'weight': ft.FontWeight.W_600, 'line_height': 1.3, 'letter_spacing': 0},
        'headline_md': {'size': 24, 'weight': ft.FontWeight.W_600, 'line_height': 1.3, 'letter_spacing': 0},
        'headline_sm': {'size': 20, 'weight': ft.FontWeight.W_600, 'line_height': 1.4, 'letter_spacing': 0},
        'title_xl': {'size': 22, 'weight': ft.FontWeight.W_500, 'line_height': 1.4, 'letter_spacing': 0},
        'title_lg': {'size': 20, 'weight': ft.FontWeight.W_500, 'line_height': 1.4, 'letter_spacing': 0},
        'title_md': {'size': 18, 'weight': ft.FontWeight.W_500, 'line_height': 1.4, 'letter_spacing': 0},
        'title_sm': {'size': 16, 'weight': ft.FontWeight.W_500, 'line_height': 1.4, 'letter_spacing': 0},
        'body_xl': {'size': 18, 'weight': ft.FontWeight.W_400, 'line_height': 1.6, 'letter_spacing': 0},
        'body_lg': {'size': 16, 'weight': ft.FontWeight.W_400, 'line_height': 1.6, 'letter_spacing': 0},
        'body_md': {'size': 14, 'weight': ft.FontWeight.W_400, 'line_height': 1.6, 'letter_spacing': 0},
        'body_sm': {'size': 13, 'weight': ft.FontWeight.W_400, 'line_height': 1.5, 'letter_spacing': 0},
        'body_xs': {'size': 12, 'weight': ft.FontWeight.W_400, 'line_height': 1.5, 'letter_spacing': 0},
        'label_lg': {'size': 14, 'weight': ft.FontWeight.W_500, 'line_height': 1.4, 'letter_spacing': 0.25},
        'label_md': {'size': 13, 'weight': ft.FontWeight.W_500, 'line_height': 1.4, 'letter_spacing': 0.25},
        'label_sm': {'size': 12, 'weight': ft.FontWeight.W_500, 'line_height': 1.4, 'letter_spacing': 0.25},
        'caption_lg': {'size': 12, 'weight': ft.FontWeight.W_400, 'line_height': 1.4, 'letter_spacing': 0.4},
        'caption_md': {'size': 11, 'weight': ft.FontWeight.W_400, 'line_height': 1.4, 'letter_spacing': 0.4},
        'caption_sm': {'size': 10, 'weight': ft.FontWeight.W_400, 'line_height': 1.4, 'letter_spacing': 0.4},
        'overline': {'size': 11, 'weight': ft.FontWeight.W_600, 'line_height': 1.2, 'letter_spacing': 1.5},
        'code': {'size': 14, 'weight': ft.FontWeight.W_400, 'line_height': 1.4, 'letter_spacing': 0, 'font_family': 'monospace'}
    }
    
    # Enhanced accessibility-focused typography
    accessibility_typography = {
        'minimum_touch_target': 44,  # Minimum 44px for touch targets
        'minimum_contrast_ratio': 4.5,  # WCAG AA standard
        'enhanced_contrast_ratio': 7.0,  # WCAG AAA standard
        'dyslexia_friendly_spacing': 1.8,  # Enhanced line height for dyslexia
        'reading_width_max': 75,  # Maximum characters per line for readability
    }
    
    # Enhanced border radius system
    radius = {
        'none': 0,
        'xs': 2,
        'sm': 4,
        'md': 6,
        'lg': 8,
        'xl': 12,
        'xxl': 16,
        'xxxl': 20,
        'xxxxl': 24,
        'pill': 50,  # For pill-shaped buttons
        'full': 9999
    }
    
    # Focus and interaction states for accessibility
    focus_styles = {
        'outline_width': 2,
        'outline_offset': 2,
        'outline_style': 'solid',
        'focus_ring_opacity': 0.5,
        'transition_duration': 150,  # milliseconds
    }
    
    # Motion and animation preferences
    motion = {
        'duration_instant': 0,
        'duration_fast': 100,
        'duration_normal': 200,
        'duration_slow': 300,
        'duration_slower': 500,
        'easing_ease_out': ft.AnimationCurve.EASE_OUT,
        'easing_ease_in': ft.AnimationCurve.EASE_IN,
        'easing_ease_in_out': ft.AnimationCurve.EASE_IN_OUT,
        'easing_bounce': ft.AnimationCurve.BOUNCE_OUT,
    }
    
    # Enhanced shadow system with subtle gradients
    shadows = {
        'none': None,
        'xs': ft.BoxShadow(spread_radius=0, blur_radius=1, color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK), offset=ft.Offset(0, 1)),
        'sm': ft.BoxShadow(spread_radius=0, blur_radius=2, color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK), offset=ft.Offset(0, 1)),
        'md': ft.BoxShadow(spread_radius=0, blur_radius=6, color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK), offset=ft.Offset(0, 4)),
        'lg': ft.BoxShadow(spread_radius=0, blur_radius=15, color=ft.Colors.with_opacity(0.12, ft.Colors.BLACK), offset=ft.Offset(0, 10)),
        'xl': ft.BoxShadow(spread_radius=0, blur_radius=25, color=ft.Colors.with_opacity(0.15, ft.Colors.BLACK), offset=ft.Offset(0, 20)),
        'xxl': ft.BoxShadow(spread_radius=0, blur_radius=50, color=ft.Colors.with_opacity(0.2, ft.Colors.BLACK), offset=ft.Offset(0, 25)),
        'inner': ft.BoxShadow(spread_radius=0, blur_radius=2, color=ft.Colors.with_opacity(0.06, ft.Colors.BLACK), offset=ft.Offset(0, 1)),
        'colored': {
            'primary': ft.BoxShadow(spread_radius=0, blur_radius=15, color=ft.Colors.with_opacity(0.15, ft.Colors.BLUE), offset=ft.Offset(0, 8)),
            'success': ft.BoxShadow(spread_radius=0, blur_radius=15, color=ft.Colors.with_opacity(0.15, ft.Colors.GREEN), offset=ft.Offset(0, 8)),
            'warning': ft.BoxShadow(spread_radius=0, blur_radius=15, color=ft.Colors.with_opacity(0.15, ft.Colors.ORANGE), offset=ft.Offset(0, 8)),
            'error': ft.BoxShadow(spread_radius=0, blur_radius=15, color=ft.Colors.with_opacity(0.15, ft.Colors.RED), offset=ft.Offset(0, 8)),
        }
    }
    
    # Enhanced component sizes with accessibility compliance
    component_sizes = {
        ComponentSize.XS: {
            'height': 32, 'padding_x': 8, 'padding_y': 6, 'font_size': 12,
            'min_touch_target': 32, 'icon_size': 14, 'gap': 6
        },
        ComponentSize.SM: {
            'height': 36, 'padding_x': 12, 'padding_y': 8, 'font_size': 13,
            'min_touch_target': 36, 'icon_size': 16, 'gap': 8
        },
        ComponentSize.MD: {
            'height': 44, 'padding_x': 16, 'padding_y': 10, 'font_size': 14,
            'min_touch_target': 44, 'icon_size': 18, 'gap': 10  # Meets WCAG AA
        },
        ComponentSize.LG: {
            'height': 52, 'padding_x': 20, 'padding_y': 14, 'font_size': 16,
            'min_touch_target': 52, 'icon_size': 20, 'gap': 12
        },
        ComponentSize.XL: {
            'height': 60, 'padding_x': 24, 'padding_y': 16, 'font_size': 18,
            'min_touch_target': 60, 'icon_size': 24, 'gap': 14
        }
    }
    
    # Breakpoints for responsive design
    breakpoints = {
        'xs': 480,   # Extra small devices
        'sm': 640,   # Small devices
        'md': 768,   # Medium devices
        'lg': 1024,  # Large devices
        'xl': 1280,  # Extra large devices
        'xxl': 1536  # 2X extra large devices
    }
    
    # Grid system
    grid = {
        'columns': 12,
        'gutter': 24,
        'margin': 24,
        'container_max_width': 1200
    }


class ModernColorPalette:
    """Modern color palette with semantic meanings and accessibility-first design."""
    
    # Primary brand colors
    PRIMARY = {
        '50': '#eff6ff',   # Very light blue
        '100': '#dbeafe',  # Light blue
        '200': '#bfdbfe',  # Lighter blue
        '300': '#93c5fd',  # Light blue
        '400': '#60a5fa',  # Medium light blue
        '500': '#3b82f6',  # Primary blue
        '600': '#2563eb',  # Medium blue
        '700': '#1d4ed8',  # Dark blue
        '800': '#1e40af',  # Darker blue
        '900': '#1e3a8a',  # Very dark blue
        '950': '#172554'   # Darkest blue
    }
    
    # Secondary/accent colors
    SECONDARY = {
        '50': '#fff7ed',   # Very light orange
        '100': '#ffedd5',  # Light orange
        '200': '#fed7aa',  # Lighter orange
        '300': '#fdba74',  # Light orange
        '400': '#fb923c',  # Medium light orange
        '500': '#f97316',  # Primary orange
        '600': '#ea580c',  # Medium orange
        '700': '#c2410c',  # Dark orange
        '800': '#9a3412',  # Darker orange
        '900': '#7c2d12',  # Very dark orange
        '950': '#431407'   # Darkest orange
    }
    
    # Semantic colors
    SUCCESS = {
        '50': '#f0fdf4',   # Very light green
        '100': '#dcfce7',  # Light green
        '200': '#bbf7d0',  # Lighter green
        '300': '#86efac',  # Light green
        '400': '#4ade80',  # Medium light green
        '500': '#22c55e',  # Primary green
        '600': '#16a34a',  # Medium green
        '700': '#15803d',  # Dark green
        '800': '#166534',  # Darker green
        '900': '#14532d',  # Very dark green
        '950': '#052e16'   # Darkest green
    }
    
    ERROR = {
        '50': '#fef2f2',   # Very light red
        '100': '#fee2e2',  # Light red
        '200': '#fecaca',  # Lighter red
        '300': '#fca5a5',  # Light red
        '400': '#f87171',  # Medium light red
        '500': '#ef4444',  # Primary red
        '600': '#dc2626',  # Medium red
        '700': '#b91c1c',  # Dark red
        '800': '#991b1b',  # Darker red
        '900': '#7f1d1d',  # Very dark red
        '950': '#450a0a'   # Darkest red
    }
    
    WARNING = {
        '50': '#fffbeb',   # Very light yellow
        '100': '#fef3c7',  # Light yellow
        '200': '#fde68a',  # Lighter yellow
        '300': '#fcd34d',  # Light yellow
        '400': '#fbbf24',  # Medium light yellow
        '500': '#f59e0b',  # Primary yellow
        '600': '#d97706',  # Medium yellow
        '700': '#b45309',  # Dark yellow
        '800': '#92400e',  # Darker yellow
        '900': '#78350f',  # Very dark yellow
        '950': '#451a03'   # Darkest yellow
    }
    
    INFO = {
        '50': '#f0f9ff',   # Very light sky blue
        '100': '#e0f2fe',  # Light sky blue
        '200': '#bae6fd',  # Lighter sky blue
        '300': '#7dd3fc',  # Light sky blue
        '400': '#38bdf8',  # Medium light sky blue
        '500': '#0ea5e9',  # Primary sky blue
        '600': '#0284c7',  # Medium sky blue
        '700': '#0369a1',  # Dark sky blue
        '800': '#075985',  # Darker sky blue
        '900': '#0c4a6e',  # Very dark sky blue
        '950': '#082f49'   # Darkest sky blue
    }
    
    # Neutral grays
    NEUTRAL = {
        '50': '#fafafa',   # Very light gray
        '100': '#f5f5f5',  # Light gray
        '200': '#e5e5e5',  # Lighter gray
        '300': '#d4d4d4',  # Light gray
        '400': '#a3a3a3',  # Medium light gray
        '500': '#737373',  # Medium gray
        '600': '#525252',  # Medium dark gray
        '700': '#404040',  # Dark gray
        '800': '#262626',  # Darker gray
        '900': '#171717',  # Very dark gray
        '950': '#0a0a0a'   # Darkest gray
    }


class ModernThemeSystem:
    """Comprehensive modern theme system with 2025 design standards."""
    
    def __init__(self, theme_mode: str = "light"):
        self.theme_mode = theme_mode
        self.tokens = DesignTokens()
        self.colors = ModernColorPalette()
        self.custom_overrides = {}
        
    def get_semantic_color(self, semantic_type: str, intensity: str = "500", 
                          theme_mode: Optional[str] = None) -> str:
        """Get semantic color with automatic theme adaptation."""
        mode = theme_mode or self.theme_mode
        
        color_map = {
            'primary': self.colors.PRIMARY,
            'secondary': self.colors.SECONDARY,
            'success': self.colors.SUCCESS,
            'error': self.colors.ERROR,
            'warning': self.colors.WARNING,
            'info': self.colors.INFO,
            'neutral': self.colors.NEUTRAL
        }
        
        # Adjust intensity for dark mode (lighter colors in dark theme)
        if mode == "dark" and semantic_type != 'neutral':
            intensity_map = {
                '50': '900', '100': '800', '200': '700', '300': '600', '400': '500',
                '500': '400', '600': '300', '700': '200', '800': '100', '900': '50'
            }
            intensity = intensity_map.get(intensity, intensity)
        
        return color_map.get(semantic_type, self.colors.NEUTRAL).get(intensity, '#000000')
    
    def get_background_colors(self) -> Dict[str, str]:
        """Get background colors for current theme."""
        if self.theme_mode == "dark":
            return {
                'primary': self.colors.NEUTRAL['900'],
                'secondary': self.colors.NEUTRAL['800'],
                'tertiary': self.colors.NEUTRAL['700'],
                'surface': self.colors.NEUTRAL['800'],
                'elevated': self.colors.NEUTRAL['750'] if '750' in self.colors.NEUTRAL else self.colors.NEUTRAL['700']
            }
        else:
            return {
                'primary': '#ffffff',
                'secondary': self.colors.NEUTRAL['50'],
                'tertiary': self.colors.NEUTRAL['100'],
                'surface': '#ffffff',
                'elevated': '#ffffff'
            }
    
    def get_text_colors(self) -> Dict[str, str]:
        """Get text colors for current theme."""
        if self.theme_mode == "dark":
            return {
                'primary': self.colors.NEUTRAL['50'],
                'secondary': self.colors.NEUTRAL['300'],
                'tertiary': self.colors.NEUTRAL['400'],
                'disabled': self.colors.NEUTRAL['600'],
                'inverse': self.colors.NEUTRAL['900']
            }
        else:
            return {
                'primary': self.colors.NEUTRAL['900'],
                'secondary': self.colors.NEUTRAL['600'],
                'tertiary': self.colors.NEUTRAL['500'],
                'disabled': self.colors.NEUTRAL['400'],
                'inverse': self.colors.NEUTRAL['50']
            }
    
    def create_button_style(self, variant: str = "primary", 
                           size: ComponentSize = ComponentSize.MD) -> Dict[str, Any]:
        """Create modern button styles."""
        size_config = self.tokens.component_sizes[size]
        
        base_style = {
            'height': size_config['height'],
            'border_radius': self.tokens.radius['md'],
            'animate_scale': ft.Animation(150, ft.AnimationCurve.EASE_OUT)
        }
        
        if variant == "primary":
            return {
                **base_style,
                'bgcolor': {
                    ft.ControlState.DEFAULT: self.get_semantic_color('primary'),
                    ft.ControlState.HOVERED: self.get_semantic_color('primary', '600'),
                    ft.ControlState.PRESSED: self.get_semantic_color('primary', '700'),
                    ft.ControlState.DISABLED: self.get_semantic_color('neutral', '300')
                },
                'color': self.get_text_colors()['inverse'],
                'elevation': {'default': 2, 'hovered': 4, 'pressed': 1}
            }
        elif variant == "secondary":
            return {
                **base_style,
                'bgcolor': {
                    ft.ControlState.DEFAULT: 'transparent',
                    ft.ControlState.HOVERED: self.get_semantic_color('primary', '50'),
                    ft.ControlState.PRESSED: self.get_semantic_color('primary', '100')
                },
                'color': self.get_semantic_color('primary'),
                'border': ft.border.all(1, self.get_semantic_color('primary')),
                'elevation': {'default': 0, 'hovered': 2, 'pressed': 0}
            }
        elif variant == "ghost":
            return {
                **base_style,
                'bgcolor': {
                    ft.ControlState.DEFAULT: 'transparent',
                    ft.ControlState.HOVERED: self.get_semantic_color('neutral', '100'),
                    ft.ControlState.PRESSED: self.get_semantic_color('neutral', '200')
                },
                'color': self.get_text_colors()['primary'],
                'elevation': {'default': 0, 'hovered': 0, 'pressed': 0}
            }
        
        return base_style
    
    def create_card_style(self, variant: str = "default", elevated: bool = True) -> Dict[str, Any]:
        """Create modern card styles."""
        backgrounds = self.get_background_colors()
        
        base_style = {
            'border_radius': self.tokens.radius['lg'],
            'padding': self.tokens.spacing['lg'],
            'bgcolor': backgrounds['surface']
        }
        
        if elevated:
            base_style['shadow'] = self.tokens.shadows['md']
        
        if variant == "outlined":
            base_style.update({
                'border': ft.border.all(1, self.get_semantic_color('neutral', '200')),
                'shadow': None
            })
        elif variant == "filled":
            base_style.update({
                'bgcolor': backgrounds['secondary'],
                'shadow': self.tokens.shadows['sm']
            })
        
        return base_style
    
    def create_input_style(self, size: ComponentSize = ComponentSize.MD,
                          state: str = "default") -> Dict[str, Any]:
        """Create modern input field styles."""
        size_config = self.tokens.component_sizes[size]
        text_colors = self.get_text_colors()
        
        base_style = {
            'height': size_config['height'],
            'border_radius': self.tokens.radius['md'],
            'bgcolor': self.get_background_colors()['surface'],
            'border_color': self.get_semantic_color('neutral', '300'),
            'focused_border_color': self.get_semantic_color('primary'),
            'color': text_colors['primary'],
            'content_padding': ft.padding.symmetric(
                horizontal=size_config['padding_x'],
                vertical=size_config['padding_y']
            )
        }
        
        if state == "error":
            base_style.update({
                'border_color': self.get_semantic_color('error'),
                'focused_border_color': self.get_semantic_color('error')
            })
        elif state == "success":
            base_style.update({
                'border_color': self.get_semantic_color('success'),
                'focused_border_color': self.get_semantic_color('success')
            })
        
        return base_style
    
    def get_typography_style(self, variant: str, color_variant: str = 'primary') -> Dict[str, Any]:
        """Get typography styles for text components with enhanced accessibility."""
        typo_config = self.tokens.typography.get(variant, self.tokens.typography['body_md'])
        text_colors = self.get_text_colors()
        
        style = {
            'size': typo_config['size'],
            'weight': typo_config['weight'],
            'color': text_colors.get(color_variant, text_colors['primary'])
        }
        
        # Add line height for better readability
        if 'line_height' in typo_config:
            style['line_height'] = typo_config['line_height']
        
        # Add letter spacing if specified
        if 'letter_spacing' in typo_config:
            style['letter_spacing'] = typo_config['letter_spacing']
            
        # Add font family for code text
        if 'font_family' in typo_config:
            style['font_family'] = typo_config['font_family']
            
        return style
    
    def create_accessible_button_style(self, variant: str = "primary", 
                                     size: ComponentSize = ComponentSize.MD,
                                     with_focus_ring: bool = True) -> Dict[str, Any]:
        """Create accessible button styles with focus indicators."""
        base_style = self.create_button_style(variant, size)
        
        if with_focus_ring:
            focus_color = self.get_semantic_color('primary', '600')
            base_style.update({
                'focus_outline': f"{self.tokens.focus_styles['outline_width']}px {self.tokens.focus_styles['outline_style']} {focus_color}",
                'focus_offset': self.tokens.focus_styles['outline_offset']
            })
            
        return base_style
    
    def create_skeleton_style(self, variant: str = "default") -> Dict[str, Any]:
        """Create skeleton loading styles."""
        base_color = self.get_semantic_color('neutral', '200')
        shimmer_color = self.get_semantic_color('neutral', '100')
        
        return {
            'bgcolor': base_color,
            'border_radius': self.tokens.radius['md'],
            'shimmer_color': shimmer_color,
            'animation_duration': self.tokens.motion['duration_slower']
        }
    
    def create_notification_style(self, type: str = "info") -> Dict[str, Any]:
        """Create notification/toast styles."""
        type_map = {
            'success': 'success',
            'error': 'error', 
            'warning': 'warning',
            'info': 'info'
        }
        
        semantic_type = type_map.get(type, 'info')
        
        return {
            'bgcolor': self.get_semantic_color(semantic_type, '50'),
            'border_color': self.get_semantic_color(semantic_type, '200'),
            'text_color': self.get_semantic_color(semantic_type, '800'),
            'icon_color': self.get_semantic_color(semantic_type, '600'),
            'border_radius': self.tokens.radius['lg'],
            'padding': self.tokens.spacing['lg'],
            'shadow': self.tokens.shadows['lg']
        }
    
    def create_progress_style(self, variant: str = "primary") -> Dict[str, Any]:
        """Create progress indicator styles."""
        return {
            'track_color': self.get_semantic_color('neutral', '200'),
            'fill_color': self.get_semantic_color('primary'),
            'height': 8,
            'border_radius': self.tokens.radius['full']
        }
    
    def get_spacing_scale(self, multiplier: float = 1.0) -> Dict[str, int]:
        """Get spacing scale with optional multiplier for different density."""
        return {
            key: int(value * multiplier) 
            for key, value in self.tokens.spacing.items()
        }
    
    def validate_contrast_ratio(self, foreground: str, background: str) -> Dict[str, Any]:
        """Validate color contrast ratio for accessibility."""
        # This is a simplified version - in production you'd use a proper contrast calculation
        return {
            'ratio': 4.5,  # Placeholder - would calculate actual ratio
            'aa_compliant': True,
            'aaa_compliant': False,
            'recommendation': 'Meets WCAG AA standards'
        }
    
    def apply_to_page(self, page: ft.Page):
        """Apply theme to Flet page."""
        backgrounds = self.get_background_colors()
        
        # Set page theme
        page.theme_mode = ft.ThemeMode.DARK if self.theme_mode == "dark" else ft.ThemeMode.LIGHT
        page.bgcolor = backgrounds['primary']
        
        # Set Material 3 theme
        page.theme = ft.Theme(
            color_scheme_seed=self.get_semantic_color('primary'),
            use_material3=True
        )
        
        # Set page-level configurations
        page.title = "Hiel RnE Modeler v4.0 - Enhanced Edition"
        page.window_width = 1400
        page.window_height = 900
        page.window_min_width = 1200
        page.window_min_height = 800
        page.padding = 0
        
    def create_component(self, component_type: str, **kwargs) -> ft.Control:
        """Factory method to create themed components."""
        if component_type == "button":
            variant = kwargs.get('variant', 'primary')
            size = kwargs.get('size', ComponentSize.MD)
            text = kwargs.get('text', 'Button')
            icon = kwargs.get('icon', None)
            on_click = kwargs.get('on_click', None)
            
            style = self.create_button_style(variant, size)
            
            return ft.ElevatedButton(
                text=text,
                icon=icon,
                on_click=on_click,
                style=ft.ButtonStyle(
                    bgcolor=style.get('bgcolor'),
                    color=style.get('color'),
                    shape=ft.RoundedRectangleBorder(radius=style.get('border_radius', 8)),
                    elevation=style.get('elevation', {})
                ),
                height=style.get('height')
            )
        
        elif component_type == "card":
            variant = kwargs.get('variant', 'default')
            elevated = kwargs.get('elevated', True)
            content = kwargs.get('content', ft.Text("Card content"))
            
            style = self.create_card_style(variant, elevated)
            
            return ft.Card(
                content=ft.Container(
                    content=content,
                    padding=style.get('padding'),
                    bgcolor=style.get('bgcolor'),
                    border=style.get('border'),
                    border_radius=style.get('border_radius')
                ),
                shadow=style.get('shadow'),
                elevation=4 if elevated else 0
            )
        
        elif component_type == "text":
            variant = kwargs.get('variant', 'body_md')
            text = kwargs.get('text', 'Sample text')
            color = kwargs.get('color', None)
            
            style = self.get_typography_style(variant)
            
            return ft.Text(
                text,
                size=style['size'],
                weight=style['weight'],
                color=color or style['color']
            )
        
        return ft.Container()  # Fallback


# Global theme instance
_theme_instance = None

def get_theme() -> ModernThemeSystem:
    """Get global theme instance."""
    global _theme_instance
    if _theme_instance is None:
        _theme_instance = ModernThemeSystem()
    return _theme_instance

def set_theme_mode(mode: str):
    """Set global theme mode."""
    global _theme_instance
    if _theme_instance is None:
        _theme_instance = ModernThemeSystem(mode)
    else:
        _theme_instance.theme_mode = mode
