"""
Workflow Wizard Component
========================

Simplified 4-step workflow to reduce complexity and improve user experience.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass

from components.ui.modern_theme_system import get_theme, ComponentSize
from components.ui.modern_components import ModernButton, ModernCard, ButtonVariant

class WorkflowStep(Enum):
    """Workflow steps for simplified user journey."""
    SETUP = "setup"
    ANALYSIS = "analysis" 
    RESULTS = "results"
    EXPORT = "export"

@dataclass
class StepConfig:
    """Configuration for workflow steps."""
    id: WorkflowStep
    title: str
    description: str
    icon: str
    estimated_time: str
    prerequisites: List[str]
    completion_criteria: str

class WorkflowWizard:
    """Simplified workflow wizard for better UX."""
    
    def __init__(self, 
                 on_step_change: Optional[Callable[[WorkflowStep], None]] = None,
                 on_action: Optional[Callable[[str, Dict[str, Any]], None]] = None):
        self.current_step = WorkflowStep.SETUP
        self.on_step_change = on_step_change
        self.on_action = on_action
        self.theme = get_theme()
        
        # Step completion tracking
        self.step_completion = {
            WorkflowStep.SETUP: False,
            WorkflowStep.ANALYSIS: False,
            WorkflowStep.RESULTS: False,
            WorkflowStep.EXPORT: False
        }
        
        # Define workflow steps
        self.steps = {
            WorkflowStep.SETUP: StepConfig(
                id=WorkflowStep.SETUP,
                title="Project Setup",
                description="Configure your solar project parameters and assumptions",
                icon=ft.Icons.SETTINGS,
                estimated_time="5-10 min",
                prerequisites=[],
                completion_criteria="All required fields completed"
            ),
            WorkflowStep.ANALYSIS: StepConfig(
                id=WorkflowStep.ANALYSIS,
                title="Enhanced Analysis",
                description="Run comprehensive financial modeling with ML predictions",
                icon=ft.Icons.ANALYTICS,
                estimated_time="2-5 min",
                prerequisites=["Project parameters configured"],
                completion_criteria="Analysis completed successfully"
            ),
            WorkflowStep.RESULTS: StepConfig(
                id=WorkflowStep.RESULTS,
                title="Interactive Results",
                description="Explore your financial model results and insights",
                icon=ft.Icons.DASHBOARD,
                estimated_time="10-20 min",
                prerequisites=["Analysis completed"],
                completion_criteria="Results reviewed and validated"
            ),
            WorkflowStep.EXPORT: StepConfig(
                id=WorkflowStep.EXPORT,
                title="Export & Reports",
                description="Generate professional reports and presentations",
                icon=ft.Icons.DOWNLOAD,
                estimated_time="2-3 min",
                prerequisites=["Results available"],
                completion_criteria="Reports generated and downloaded"
            )
        }
    
    def build(self) -> ft.Control:
        """Build the workflow wizard interface."""
        return ft.Container(
            content=ft.Column([
                self._create_progress_header(),
                ft.Container(height=20),
                self._create_step_cards(),
                ft.Container(height=20),
                self._create_navigation_controls()
            ]),
            padding=ft.padding.all(20),
            bgcolor=self.theme.get_background_colors()['primary']
        )
    
    def _create_progress_header(self) -> ft.Control:
        """Create progress header with step indicators."""
        steps_list = list(WorkflowStep)
        current_index = steps_list.index(self.current_step)
        
        step_indicators = []
        for i, step in enumerate(steps_list):
            is_current = step == self.current_step
            is_completed = self.step_completion[step]
            is_accessible = i <= current_index or is_completed
            
            # Step circle
            if is_completed:
                icon = ft.Icons.CHECK_CIRCLE
                color = self.theme.get_semantic_color('success')
                bg_color = self.theme.get_semantic_color('success', '50')
            elif is_current:
                icon = ft.Icons.RADIO_BUTTON_CHECKED
                color = self.theme.get_semantic_color('primary')
                bg_color = self.theme.get_semantic_color('primary', '50')
            else:
                icon = ft.Icons.RADIO_BUTTON_UNCHECKED
                color = self.theme.get_semantic_color('neutral', '400')
                bg_color = self.theme.get_semantic_color('neutral', '100')
            
            step_config = self.steps[step]
            
            step_indicator = ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Icon(icon, color=color, size=24),
                        width=50,
                        height=50,
                        bgcolor=bg_color,
                        border_radius=25,
                        alignment=ft.alignment.center
                    ),
                    ft.Text(
                        step_config.title,
                        size=12,
                        weight=ft.FontWeight.W_500 if is_current else ft.FontWeight.W_400,
                        color=color if is_accessible else self.theme.get_text_colors()['disabled'],
                        text_align=ft.TextAlign.CENTER
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                on_click=lambda e, s=step: self._handle_step_click(s) if is_accessible else None,
                ink=is_accessible
            )
            
            step_indicators.append(step_indicator)
            
            # Add connector line (except for last step)
            if i < len(steps_list) - 1:
                connector_color = (self.theme.get_semantic_color('success') if is_completed
                                 else self.theme.get_semantic_color('neutral', '300'))
                
                step_indicators.append(
                    ft.Container(
                        width=50,
                        height=2,
                        bgcolor=connector_color,
                        margin=ft.margin.only(top=25)
                    )
                )
        
        # Current step info
        current_config = self.steps[self.current_step]
        step_info = ft.Container(
            content=ft.Column([
                ft.Text(
                    f"Step {current_index + 1} of {len(steps_list)}: {current_config.title}",
                    size=20,
                    weight=ft.FontWeight.W_600,
                    color=self.theme.get_text_colors()['primary']
                ),
                ft.Text(
                    current_config.description,
                    size=14,
                    color=self.theme.get_text_colors()['secondary']
                ),
                ft.Row([
                    ft.Icon(ft.Icons.ACCESS_TIME, size=16, color=self.theme.get_semantic_color('info')),
                    ft.Text(
                        f"Estimated time: {current_config.estimated_time}",
                        size=12,
                        color=self.theme.get_text_colors()['tertiary']
                    )
                ], spacing=4)
            ], spacing=4),
            padding=ft.padding.all(16),
            bgcolor=self.theme.get_semantic_color('primary', '50'),
            border_radius=12,
            border=ft.border.all(1, self.theme.get_semantic_color('primary', '200'))
        )
        
        return ft.Column([
            ft.Row(step_indicators, alignment=ft.MainAxisAlignment.CENTER, spacing=0),
            ft.Container(height=20),
            step_info
        ])
    
    def _create_step_cards(self) -> ft.Control:
        """Create cards for each workflow step with details."""
        current_config = self.steps[self.current_step]
        
        # Prerequisites check
        prerequisites_met = self._check_prerequisites(self.current_step)
        
        # Main action card
        action_card = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(current_config.icon, size=32, color=self.theme.get_semantic_color('primary')),
                    ft.Column([
                        ft.Text(
                            current_config.title,
                            size=18,
                            weight=ft.FontWeight.W_600,
                            color=self.theme.get_text_colors()['primary']
                        ),
                        ft.Text(
                            current_config.description,
                            size=14,
                            color=self.theme.get_text_colors()['secondary']
                        )
                    ], expand=True, spacing=4)
                ], spacing=16),
                
                ft.Container(height=16),
                
                # Prerequisites
                self._create_prerequisites_section(current_config),
                
                ft.Container(height=16),
                
                # Action button
                self._create_step_action_button(self.current_step, prerequisites_met),
                
                # Help text
                ft.Container(
                    content=ft.Text(
                        f"✓ Completion criteria: {current_config.completion_criteria}",
                        size=12,
                        color=self.theme.get_semantic_color('success', '700'),
                        italic=True
                    ),
                    padding=ft.padding.only(top=8)
                )
            ]),
            padding=ft.padding.all(24),
            bgcolor=self.theme.get_background_colors()['surface'],
            border_radius=16,
            border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
            shadow=self.theme.tokens.shadows['md']
        )
        
        # Quick access cards for completed steps
        quick_access_cards = self._create_quick_access_cards()
        
        return ft.Column([
            action_card,
            ft.Container(height=20),
            quick_access_cards
        ] if quick_access_cards.controls else [action_card])
    
    def _create_prerequisites_section(self, config: StepConfig) -> ft.Control:
        """Create prerequisites section."""
        if not config.prerequisites:
            return ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.CHECK_CIRCLE, color=self.theme.get_semantic_color('success'), size=16),
                    ft.Text("No prerequisites required", 
                           size=12, color=self.theme.get_semantic_color('success', '700'))
                ], spacing=8),
                padding=ft.padding.all(12),
                bgcolor=self.theme.get_semantic_color('success', '50'),
                border_radius=8
            )
        
        prerequisite_items = []
        all_met = True
        
        for prereq in config.prerequisites:
            is_met = self._is_prerequisite_met(prereq)
            all_met = all_met and is_met
            
            icon = ft.Icons.CHECK_CIRCLE if is_met else ft.Icons.RADIO_BUTTON_UNCHECKED
            color = self.theme.get_semantic_color('success') if is_met else self.theme.get_semantic_color('warning')
            
            prerequisite_items.append(
                ft.Row([
                    ft.Icon(icon, color=color, size=16),
                    ft.Text(prereq, size=12, color=self.theme.get_text_colors()['secondary'])
                ], spacing=8)
            )
        
        status_color = 'success' if all_met else 'warning'
        
        return ft.Container(
            content=ft.Column([
                ft.Text("Prerequisites:", size=12, weight=ft.FontWeight.W_500,
                       color=self.theme.get_text_colors()['primary']),
                ft.Column(prerequisite_items, spacing=4)
            ], spacing=8),
            padding=ft.padding.all(12),
            bgcolor=self.theme.get_semantic_color(status_color, '50'),
            border_radius=8,
            border=ft.border.all(1, self.theme.get_semantic_color(status_color, '200'))
        )
    
    def _create_step_action_button(self, step: WorkflowStep, enabled: bool) -> ft.Control:
        """Create action button for current step."""
        actions = {
            WorkflowStep.SETUP: {
                'text': '📝 Configure Project',
                'action': 'navigate_setup',
                'description': 'Set up your project parameters'
            },
            WorkflowStep.ANALYSIS: {
                'text': '🚀 Run Enhanced Analysis',
                'action': 'run_analysis',
                'description': 'Execute comprehensive financial modeling'
            },
            WorkflowStep.RESULTS: {
                'text': '📊 View Interactive Dashboard',
                'action': 'view_results',
                'description': 'Explore your results and insights'
            },
            WorkflowStep.EXPORT: {
                'text': '📁 Generate Reports',
                'action': 'generate_exports',
                'description': 'Create professional reports and presentations'
            }
        }
        
        action_config = actions[step]
        
        button = ModernButton(
            action_config['text'],
            variant=ButtonVariant.PRIMARY if enabled else ButtonVariant.GHOST,
            size=ComponentSize.LG,
            icon=None,  # Icon is in text
            on_click=lambda e: self._handle_action(action_config['action']) if enabled else None,
            disabled=not enabled
        ).build()
        
        return ft.Column([
            button,
            ft.Text(
                action_config['description'],
                size=12,
                color=self.theme.get_text_colors()['tertiary'],
                text_align=ft.TextAlign.CENTER
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8)
    
    def _create_quick_access_cards(self) -> ft.Control:
        """Create quick access cards for completed steps."""
        completed_steps = [step for step, completed in self.step_completion.items() if completed]
        
        if not completed_steps:
            return ft.Container()
        
        quick_cards = []
        for step in completed_steps:
            if step == self.current_step:
                continue
                
            config = self.steps[step]
            
            card = ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(config.icon, color=self.theme.get_semantic_color('success'), size=20),
                        ft.Column([
                            ft.Text(config.title, size=14, weight=ft.FontWeight.W_500),
                            ft.Text("Completed ✓", size=11, color=self.theme.get_semantic_color('success'))
                        ], expand=True, spacing=2)
                    ], spacing=12),
                    ft.TextButton(
                        "Quick Access",
                        style=ft.ButtonStyle(
                            color=self.theme.get_semantic_color('primary'),
                            padding=ft.padding.symmetric(horizontal=8, vertical=4)
                        ),
                        on_click=lambda e, s=step: self._handle_step_click(s)
                    )
                ], spacing=8),
                padding=ft.padding.all(16),
                bgcolor=self.theme.get_semantic_color('success', '50'),
                border_radius=12,
                border=ft.border.all(1, self.theme.get_semantic_color('success', '200')),
                width=200
            )
            
            quick_cards.append(card)
        
        if not quick_cards:
            return ft.Container()
        
        return ft.Column([
            ft.Text("Quick Access:", size=14, weight=ft.FontWeight.W_500,
                   color=self.theme.get_text_colors()['primary']),
            ft.Row(quick_cards, spacing=12, scroll=ft.ScrollMode.AUTO)
        ], spacing=8)
    
    def _create_navigation_controls(self) -> ft.Control:
        """Create navigation controls."""
        steps_list = list(WorkflowStep)
        current_index = steps_list.index(self.current_step)
        
        controls = []
        
        # Previous button
        if current_index > 0:
            prev_button = ModernButton(
                "← Previous",
                variant=ButtonVariant.SECONDARY,
                size=ComponentSize.MD,
                on_click=lambda e: self._navigate_to_step(steps_list[current_index - 1])
            ).build()
            controls.append(prev_button)
        
        # Skip/Next button
        if current_index < len(steps_list) - 1:
            next_enabled = self.step_completion[self.current_step]
            next_button = ModernButton(
                "Next →" if next_enabled else "Skip →",
                variant=ButtonVariant.PRIMARY if next_enabled else ButtonVariant.GHOST,
                size=ComponentSize.MD,
                on_click=lambda e: self._navigate_to_step(steps_list[current_index + 1])
            ).build()
            controls.append(next_button)
        
        # Help button
        help_button = ft.IconButton(
            icon=ft.Icons.HELP_OUTLINE,
            tooltip="Get help with this step",
            on_click=self._show_help,
            icon_color=self.theme.get_semantic_color('info')
        )
        controls.append(help_button)
        
        return ft.Row(
            controls,
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN if len(controls) > 1 else ft.MainAxisAlignment.CENTER,
            spacing=16
        )
    
    def _handle_step_click(self, step: WorkflowStep):
        """Handle step indicator click."""
        if self._can_navigate_to_step(step):
            self._navigate_to_step(step)
    
    def _navigate_to_step(self, step: WorkflowStep):
        """Navigate to specific step."""
        self.current_step = step
        if self.on_step_change:
            self.on_step_change(step)
    
    def _handle_action(self, action: str):
        """Handle step action."""
        if self.on_action:
            self.on_action(action, {'current_step': self.current_step})
    
    def _can_navigate_to_step(self, step: WorkflowStep) -> bool:
        """Check if user can navigate to specific step."""
        steps_list = list(WorkflowStep)
        step_index = steps_list.index(step)
        current_index = steps_list.index(self.current_step)
        
        # Can always go backwards
        if step_index <= current_index:
            return True
            
        # Can go forward if previous steps are completed
        for i in range(current_index, step_index):
            if not self.step_completion[steps_list[i]]:
                return False
        
        return True
    
    def _check_prerequisites(self, step: WorkflowStep) -> bool:
        """Check if prerequisites for step are met."""
        config = self.steps[step]
        return all(self._is_prerequisite_met(prereq) for prereq in config.prerequisites)
    
    def _is_prerequisite_met(self, prerequisite: str) -> bool:
        """Check if specific prerequisite is met."""
        # This would be implemented based on actual app state
        prerequisite_checks = {
            "Project parameters configured": True,  # Would check actual state
            "Analysis completed": self.step_completion[WorkflowStep.ANALYSIS],
            "Results available": self.step_completion[WorkflowStep.RESULTS]
        }
        return prerequisite_checks.get(prerequisite, False)
    
    def _show_help(self, e):
        """Show contextual help for current step."""
        # This would show a help dialog or tooltip
        pass
    
    def mark_step_completed(self, step: WorkflowStep):
        """Mark a step as completed."""
        self.step_completion[step] = True
    
    def mark_step_incomplete(self, step: WorkflowStep):
        """Mark a step as incomplete."""
        self.step_completion[step] = False
    
    def get_current_step(self) -> WorkflowStep:
        """Get current workflow step."""
        return self.current_step
    
    def get_completion_progress(self) -> float:
        """Get overall completion progress (0-100)."""
        completed_count = sum(1 for completed in self.step_completion.values() if completed)
        return (completed_count / len(self.step_completion)) * 100
