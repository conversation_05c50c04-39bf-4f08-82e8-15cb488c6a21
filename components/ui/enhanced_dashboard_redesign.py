"""
Enhanced Dashboard Redesign
==========================

Redesigned dashboard with progressive disclosure and improved UX.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass

from components.ui.modern_theme_system import get_theme, ComponentSize
from components.ui.modern_components import ModernButton, ModernCard, ButtonVariant

class DashboardSection(Enum):
    """Dashboard sections for progressive disclosure."""
    OVERVIEW = "overview"
    FINANCIAL = "financial"
    TECHNICAL = "technical"
    RISK = "risk"
    INSIGHTS = "insights"

@dataclass
class DashboardWidget:
    """Configuration for dashboard widgets."""
    id: str
    title: str
    description: str
    icon: str
    section: DashboardSection
    priority: int  # 1=highest, 3=lowest
    size: str  # "small", "medium", "large"
    requires_data: List[str]

class EnhancedDashboardRedesign:
    """Enhanced dashboard with progressive disclosure and better UX."""
    
    def __init__(self, 
                 financial_results: Optional[Dict[str, Any]] = None,
                 ml_predictions: Optional[Dict[str, Any]] = None,
                 charts_3d: Optional[Dict[str, str]] = None,
                 on_action: Optional[Callable[[str, Dict[str, Any]], None]] = None):
        
        self.financial_results = financial_results
        self.ml_predictions = ml_predictions
        self.charts_3d = charts_3d
        self.on_action = on_action
        self.theme = get_theme()
        
        # Dashboard customization
        self.selected_section = DashboardSection.OVERVIEW
        self.expanded_widgets = set()
        self.hidden_widgets = set()
        self.layout_mode = "adaptive"  # "compact", "adaptive", "detailed"
        
        # Define dashboard widgets
        self.widgets = self._define_dashboard_widgets()
        
        # User preferences (would be persisted)
        self.user_preferences = {
            'default_section': DashboardSection.OVERVIEW,
            'auto_expand_insights': True,
            'show_help_hints': True,
            'preferred_layout': 'adaptive'
        }
    
    def _define_dashboard_widgets(self) -> Dict[str, DashboardWidget]:
        """Define all available dashboard widgets."""
        return {
            # Overview Section - High Priority
            'kpi_summary': DashboardWidget(
                id='kpi_summary',
                title='Key Performance Indicators',
                description='Essential financial metrics at a glance',
                icon=ft.Icons.ANALYTICS,
                section=DashboardSection.OVERVIEW,
                priority=1,
                size='large',
                requires_data=['financial_results']
            ),
            'project_status': DashboardWidget(
                id='project_status',
                title='Project Health',
                description='Overall project status and progress',
                icon=ft.Icons.HEALTH_AND_SAFETY,
                section=DashboardSection.OVERVIEW,
                priority=1,
                size='medium',
                requires_data=['financial_results']
            ),
            'quick_insights': DashboardWidget(
                id='quick_insights',
                title='Quick Insights',
                description='AI-powered key findings and recommendations',
                icon=ft.Icons.LIGHTBULB,
                section=DashboardSection.OVERVIEW,
                priority=2,
                size='medium',
                requires_data=['ml_predictions']
            ),
            
            # Financial Section
            'financial_overview': DashboardWidget(
                id='financial_overview',
                title='Financial Overview',
                description='Revenue, costs, and profitability metrics',
                icon=ft.Icons.ACCOUNT_BALANCE,
                section=DashboardSection.FINANCIAL,
                priority=1,
                size='large',
                requires_data=['financial_results']
            ),
            'cashflow_chart': DashboardWidget(
                id='cashflow_chart',
                title='Cashflow Analysis',
                description='Project cashflow over time with key milestones',
                icon=ft.Icons.TRENDING_UP,
                section=DashboardSection.FINANCIAL,
                priority=1,
                size='large',
                requires_data=['financial_results']
            ),
            'dcf_breakdown': DashboardWidget(
                id='dcf_breakdown',
                title='DCF Model Breakdown',
                description='Detailed discounted cash flow analysis',
                icon=ft.Icons.CALCULATE,
                section=DashboardSection.FINANCIAL,
                priority=2,
                size='medium',
                requires_data=['financial_results']
            ),
            
            # Technical Section
            'technical_metrics': DashboardWidget(
                id='technical_metrics',
                title='Technical Performance',
                description='Capacity factor, efficiency, and production metrics',
                icon=ft.Icons.ELECTRIC_BOLT,
                section=DashboardSection.TECHNICAL,
                priority=1,
                size='medium',
                requires_data=['financial_results']
            ),
            'energy_production': DashboardWidget(
                id='energy_production',
                title='Energy Production Forecast',
                description='Expected energy output over project lifetime',
                icon=ft.Icons.SOLAR_POWER,
                section=DashboardSection.TECHNICAL,
                priority=1,
                size='large',
                requires_data=['financial_results']
            ),
            
            # Risk Section
            'risk_assessment': DashboardWidget(
                id='risk_assessment',
                title='Risk Assessment',
                description='Financial and technical risk analysis',
                icon=ft.Icons.SECURITY,
                section=DashboardSection.RISK,
                priority=1,
                size='large',
                requires_data=['financial_results']
            ),
            'sensitivity_preview': DashboardWidget(
                id='sensitivity_preview',
                title='Sensitivity Analysis',
                description='Impact of key variables on project returns',
                icon=ft.Icons.TUNE,
                section=DashboardSection.RISK,
                priority=2,
                size='medium',
                requires_data=['financial_results']
            ),
            
            # Insights Section
            'ml_predictions': DashboardWidget(
                id='ml_predictions',
                title='ML Predictions',
                description='Machine learning insights and forecasts',
                icon=ft.Icons.PSYCHOLOGY,
                section=DashboardSection.INSIGHTS,
                priority=1,
                size='large',
                requires_data=['ml_predictions']
            ),
            'chart_3d_preview': DashboardWidget(
                id='chart_3d_preview',
                title='3D Visualizations',
                description='Interactive 3D charts for complex analysis',
                icon=ft.Icons.VIEW_IN_AR,
                section=DashboardSection.INSIGHTS,
                priority=2,
                size='medium',
                requires_data=['charts_3d']
            )
        }
    
    def build(self) -> ft.Control:
        """Build the enhanced dashboard."""
        if not self.financial_results and not self.ml_predictions:
            return self._create_empty_state()
        
        return ft.Container(
            content=ft.Column([
                self._create_dashboard_header(),
                ft.Container(height=16),
                self._create_section_navigation(),
                ft.Container(height=16),
                self._create_dashboard_content(),
                ft.Container(height=16),
                self._create_customization_panel()
            ]),
            padding=ft.padding.all(20),
            bgcolor=self.theme.get_background_colors()['primary'],
            expand=True
        )
    
    def _create_dashboard_header(self) -> ft.Control:
        """Create dashboard header with overview and controls."""
        # Quick stats
        stats = self._calculate_quick_stats()
        
        quick_stats = ft.Row([
            self._create_stat_chip("Project IRR", stats.get('irr', 'N/A'), ft.Icons.TRENDING_UP, 'success'),
            self._create_stat_chip("NPV", stats.get('npv', 'N/A'), ft.Icons.MONETIZATION_ON, 'primary'),
            self._create_stat_chip("Payback", stats.get('payback', 'N/A'), ft.Icons.SCHEDULE, 'info'),
            self._create_stat_chip("Risk Level", stats.get('risk', 'N/A'), ft.Icons.SECURITY, 'warning')
        ], spacing=12, scroll=ft.ScrollMode.AUTO)
        
        # Header with title and controls
        header = ft.Row([
            ft.Column([
                ft.Text(
                    "📊 Enhanced Financial Dashboard",
                    size=24,
                    weight=ft.FontWeight.W_700,
                    color=self.theme.get_text_colors()['primary']
                ),
                ft.Text(
                    "Interactive analysis with AI insights and 3D visualizations",
                    size=14,
                    color=self.theme.get_text_colors()['secondary']
                )
            ], expand=True),
            ft.Row([
                self._create_layout_toggle(),
                self._create_export_button(),
                self._create_help_button()
            ], spacing=8)
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        
        return ft.Column([
            header,
            ft.Container(height=16),
            quick_stats
        ])
    
    def _create_stat_chip(self, label: str, value: str, icon: str, color: str) -> ft.Control:
        """Create a quick stat chip."""
        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, color=self.theme.get_semantic_color(color), size=20),
                ft.Column([
                    ft.Text(label, size=11, color=self.theme.get_text_colors()['tertiary']),
                    ft.Text(value, size=14, weight=ft.FontWeight.W_600, 
                           color=self.theme.get_text_colors()['primary'])
                ], spacing=0)
            ], spacing=8),
            padding=ft.padding.symmetric(horizontal=12, vertical=8),
            bgcolor=self.theme.get_semantic_color(color, '50'),
            border_radius=20,
            border=ft.border.all(1, self.theme.get_semantic_color(color, '200'))
        )
    
    def _create_section_navigation(self) -> ft.Control:
        """Create section navigation tabs."""
        nav_items = []
        
        for section in DashboardSection:
            available_widgets = self._get_available_widgets_for_section(section)
            widget_count = len(available_widgets)
            
            is_selected = section == self.selected_section
            has_data = widget_count > 0
            
            # Section button
            button_color = (self.theme.get_semantic_color('primary') if is_selected 
                          else self.theme.get_text_colors()['secondary'])
            bg_color = (self.theme.get_semantic_color('primary', '50') if is_selected 
                       else 'transparent')
            
            section_info = {
                DashboardSection.OVERVIEW: ("Overview", ft.Icons.DASHBOARD, "Key metrics and status"),
                DashboardSection.FINANCIAL: ("Financial", ft.Icons.ACCOUNT_BALANCE, "Revenue and costs"),
                DashboardSection.TECHNICAL: ("Technical", ft.Icons.ELECTRIC_BOLT, "Performance metrics"),
                DashboardSection.RISK: ("Risk", ft.Icons.SECURITY, "Risk assessment"),
                DashboardSection.INSIGHTS: ("Insights", ft.Icons.PSYCHOLOGY, "AI predictions")
            }
            
            title, icon, description = section_info[section]
            
            nav_item = ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(icon, color=button_color, size=18),
                        ft.Text(title, color=button_color, weight=ft.FontWeight.W_500),
                        ft.Container(
                            content=ft.Text(str(widget_count), size=10, color=ft.Colors.WHITE),
                            width=16, height=16,
                            bgcolor=button_color if has_data else self.theme.get_semantic_color('neutral', '300'),
                            border_radius=8,
                            alignment=ft.alignment.center
                        ) if widget_count > 0 else ft.Container()
                    ], spacing=8),
                    ft.Text(description, size=10, color=self.theme.get_text_colors()['tertiary'])
                    if is_selected else ft.Container()
                ], spacing=4),
                padding=ft.padding.symmetric(horizontal=16, vertical=12),
                bgcolor=bg_color,
                border_radius=8,
                on_click=lambda e, s=section: self._select_section(s),
                ink=True,
                animate=ft.Animation(200, ft.AnimationCurve.EASE_OUT)
            )
            
            nav_items.append(nav_item)
        
        return ft.Container(
            content=ft.Row(nav_items, spacing=4, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.symmetric(vertical=8),
            bgcolor=self.theme.get_background_colors()['secondary'],
            border_radius=12
        )
    
    def _create_dashboard_content(self) -> ft.Control:
        """Create main dashboard content area."""
        available_widgets = self._get_available_widgets_for_section(self.selected_section)
        
        if not available_widgets:
            return self._create_no_data_state()
        
        # Sort widgets by priority and size
        sorted_widgets = sorted(available_widgets, key=lambda w: (w.priority, w.size))
        
        # Create widget grid based on layout mode
        if self.layout_mode == "compact":
            return self._create_compact_layout(sorted_widgets)
        elif self.layout_mode == "detailed":
            return self._create_detailed_layout(sorted_widgets)
        else:  # adaptive
            return self._create_adaptive_layout(sorted_widgets)
    
    def _create_adaptive_layout(self, widgets: List[DashboardWidget]) -> ft.Control:
        """Create adaptive layout that adjusts to content."""
        widget_rows = []
        current_row = []
        current_row_weight = 0
        
        for widget in widgets:
            widget_control = self._create_widget_card(widget)
            widget_weight = {'small': 1, 'medium': 2, 'large': 3}[widget.size]
            
            # Check if widget fits in current row (max weight 3)
            if current_row_weight + widget_weight <= 3:
                current_row.append(widget_control)
                current_row_weight += widget_weight
            else:
                # Start new row
                if current_row:
                    widget_rows.append(ft.Row(current_row, spacing=16))
                current_row = [widget_control]
                current_row_weight = widget_weight
        
        # Add remaining widgets
        if current_row:
            widget_rows.append(ft.Row(current_row, spacing=16))
        
        return ft.Column(widget_rows, spacing=16)
    
    def _create_compact_layout(self, widgets: List[DashboardWidget]) -> ft.Control:
        """Create compact layout for smaller screens."""
        # Single column layout with smaller widgets
        widget_controls = [self._create_widget_card(widget, compact=True) for widget in widgets]
        return ft.Column(widget_controls, spacing=12)
    
    def _create_detailed_layout(self, widgets: List[DashboardWidget]) -> ft.Control:
        """Create detailed layout with expanded widgets."""
        # Expand all widgets by default in detailed mode
        for widget in widgets:
            self.expanded_widgets.add(widget.id)
        
        widget_controls = [self._create_widget_card(widget, detailed=True) for widget in widgets]
        return ft.Column(widget_controls, spacing=20)
    
    def _create_widget_card(self, widget: DashboardWidget, compact: bool = False, detailed: bool = False) -> ft.Control:
        """Create individual widget card."""
        is_expanded = widget.id in self.expanded_widgets
        has_data = self._has_required_data(widget)
        
        if not has_data:
            return self._create_placeholder_widget(widget)
        
        # Widget header
        header = ft.Row([
            ft.Icon(widget.icon, color=self.theme.get_semantic_color('primary'), size=20),
            ft.Column([
                ft.Text(widget.title, size=16, weight=ft.FontWeight.W_600),
                ft.Text(widget.description, size=12, color=self.theme.get_text_colors()['secondary'])
                if not compact else ft.Container()
            ], expand=True, spacing=2),
            ft.Row([
                self._create_widget_menu(widget),
                ft.IconButton(
                    icon=ft.Icons.EXPAND_LESS if is_expanded else ft.Icons.EXPAND_MORE,
                    icon_size=16,
                    on_click=lambda e, w=widget: self._toggle_widget_expansion(w),
                    tooltip="Expand/Collapse"
                )
            ], spacing=4)
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        
        # Widget content
        content = self._create_widget_content(widget, is_expanded or detailed, compact)
        
        # Widget container
        return ft.Container(
            content=ft.Column([
                header,
                ft.Container(height=8),
                content
            ]),
            padding=ft.padding.all(16 if not compact else 12),
            bgcolor=self.theme.get_background_colors()['surface'],
            border_radius=12,
            border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
            shadow=self.theme.tokens.shadows['sm'],
            animate=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
    
    def _create_widget_content(self, widget: DashboardWidget, expanded: bool, compact: bool) -> ft.Control:
        """Create content for specific widget types."""
        if widget.id == 'kpi_summary':
            return self._create_kpi_summary_content(expanded, compact)
        elif widget.id == 'project_status':
            return self._create_project_status_content(expanded, compact)
        elif widget.id == 'quick_insights':
            return self._create_quick_insights_content(expanded, compact)
        elif widget.id == 'financial_overview':
            return self._create_financial_overview_content(expanded, compact)
        elif widget.id == 'cashflow_chart':
            return self._create_cashflow_chart_content(expanded, compact)
        elif widget.id == 'dcf_breakdown':
            return self._create_dcf_breakdown_content(expanded, compact)
        elif widget.id == 'technical_metrics':
            return self._create_technical_metrics_content(expanded, compact)
        elif widget.id == 'energy_production':
            return self._create_energy_production_content(expanded, compact)
        elif widget.id == 'risk_assessment':
            return self._create_risk_assessment_content(expanded, compact)
        elif widget.id == 'sensitivity_preview':
            return self._create_sensitivity_preview_content(expanded, compact)
        elif widget.id == 'ml_predictions':
            return self._create_ml_predictions_content(expanded, compact)
        elif widget.id == 'chart_3d_preview':
            return self._create_3d_charts_content(expanded, compact)
        else:
            return ft.Text("Widget content not implemented", color=self.theme.get_text_colors()['tertiary'])
    
    def _create_kpi_summary_content(self, expanded: bool, compact: bool) -> ft.Control:
        """Create KPI summary content."""
        if not self.financial_results:
            return ft.Text("No financial data available")
        
        kpis = self.financial_results.get('kpis', {})
        
        # Core KPIs
        core_kpis = [
            ("Project IRR", f"{kpis.get('IRR_project', 0):.1%}", ft.Icons.TRENDING_UP, 'success'),
            ("Equity IRR", f"{kpis.get('IRR_equity', 0):.1%}", ft.Icons.ACCOUNT_BALANCE, 'primary'),
            ("NPV Project", f"€{kpis.get('NPV_project', 0)/1e6:.1f}M", ft.Icons.MONETIZATION_ON, 'info'),
            ("LCOE", f"{kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh", ft.Icons.ELECTRIC_BOLT, 'warning')
        ]
        
        kpi_cards = [
            self._create_kpi_card(label, value, icon, color, compact)
            for label, value, icon, color in core_kpis
        ]
        
        if compact:
            return ft.Row(kpi_cards[:2], spacing=8)
        elif expanded:
            # Additional KPIs in expanded view
            additional_kpis = [
                ("Min DSCR", f"{kpis.get('Min_DSCR', 0):.2f}", ft.Icons.SECURITY),
                ("Payback Period", f"{kpis.get('Payback_years', 0):.1f} years", ft.Icons.SCHEDULE)
            ]
            
            additional_cards = [
                self._create_kpi_card(label, value, icon, 'neutral', compact)
                for label, value, icon in additional_kpis
            ]
            
            return ft.Column([
                ft.Row(kpi_cards, spacing=12),
                ft.Container(height=12),
                ft.Row(additional_cards, spacing=12)
            ])
        else:
            return ft.Row(kpi_cards, spacing=12)
    
    def _create_kpi_card(self, label: str, value: str, icon: str, color: str, compact: bool = False) -> ft.Control:
        """Create individual KPI card."""
        return ft.Container(
            content=ft.Column([
                ft.Icon(icon, color=self.theme.get_semantic_color(color), size=24 if not compact else 20),
                ft.Text(value, size=18 if not compact else 16, weight=ft.FontWeight.W_700,
                       color=self.theme.get_text_colors()['primary']),
                ft.Text(label, size=12 if not compact else 10, 
                       color=self.theme.get_text_colors()['secondary'],
                       text_align=ft.TextAlign.CENTER)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=4),
            padding=ft.padding.all(16 if not compact else 12),
            bgcolor=self.theme.get_semantic_color(color, '50'),
            border_radius=8,
            border=ft.border.all(1, self.theme.get_semantic_color(color, '200')),
            expand=True
        )
    
    def _create_project_status_content(self, expanded: bool, compact: bool) -> ft.Control:
        """Create project status content."""
        # Mock status - would be calculated from actual data
        status_items = [
            ("Financial Model", "Complete", ft.Icons.CHECK_CIRCLE, 'success'),
            ("Risk Assessment", "Complete", ft.Icons.CHECK_CIRCLE, 'success'),
            ("Validation", "Pending", ft.Icons.PENDING, 'warning'),
            ("Export Ready", "Ready", ft.Icons.CHECK_CIRCLE, 'success')
        ]
        
        if compact:
            status_items = status_items[:2]
        
        status_rows = []
        for label, status, icon, color in status_items:
            status_rows.append(
                ft.Row([
                    ft.Icon(icon, color=self.theme.get_semantic_color(color), size=16),
                    ft.Text(label, expand=True),
                    ft.Text(status, color=self.theme.get_semantic_color(color), weight=ft.FontWeight.W_500)
                ], spacing=8)
            )
        
        return ft.Column(status_rows, spacing=8)
    
    def _create_quick_insights_content(self, expanded: bool, compact: bool) -> ft.Control:
        """Create quick insights content."""
        if not self.ml_predictions:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.PSYCHOLOGY, color=self.theme.get_semantic_color('info'), size=32),
                    ft.Text("ML Insights Available After Analysis",
                           color=self.theme.get_text_colors()['secondary'])
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(20),
                alignment=ft.alignment.center
            )
        
        # Mock insights - would come from ML predictions
        insights = [
            "💡 Project IRR is 15% above industry average",
            "⚠️ CAPEX sensitivity is higher than recommended",
            "✅ Strong debt coverage ratio indicates low risk"
        ]
        
        if compact:
            insights = insights[:1]
        
        insight_items = []
        for insight in insights:
            insight_items.append(
                ft.Container(
                    content=ft.Text(insight, size=12),
                    padding=ft.padding.all(8),
                    bgcolor=self.theme.get_semantic_color('info', '50'),
                    border_radius=6
                )
            )
        
        return ft.Column(insight_items, spacing=8)
    
    def _create_financial_overview_content(self, expanded: bool, compact: bool) -> ft.Control:
        """Create financial overview content."""
        # Placeholder for financial charts
        return ft.Container(
            content=ft.Text("Financial charts would be embedded here",
                           color=self.theme.get_text_colors()['secondary']),
            height=200 if expanded else 150,
            bgcolor=self.theme.get_semantic_color('neutral', '50'),
            border_radius=8,
            alignment=ft.alignment.center
        )
    
    # Additional widget content methods would follow the same pattern...
    
    def _create_customization_panel(self) -> ft.Control:
        """Create dashboard customization panel."""
        return ft.Container(
            content=ft.Row([
                ft.Text("💡 Tip: Click widget headers to expand/collapse. Use section tabs to focus on specific areas.",
                       size=12, color=self.theme.get_text_colors()['tertiary'],
                       expand=True),
                ft.TextButton(
                    "Customize Dashboard",
                    style=ft.ButtonStyle(color=self.theme.get_semantic_color('primary')),
                    on_click=self._show_customization_dialog
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(12),
            bgcolor=self.theme.get_semantic_color('info', '50'),
            border_radius=8,
            border=ft.border.all(1, self.theme.get_semantic_color('info', '200'))
        )
    
    # Helper methods
    def _calculate_quick_stats(self) -> Dict[str, str]:
        """Calculate quick stats for header."""
        if not self.financial_results:
            return {'irr': 'N/A', 'npv': 'N/A', 'payback': 'N/A', 'risk': 'N/A'}
        
        kpis = self.financial_results.get('kpis', {})
        return {
            'irr': f"{kpis.get('IRR_project', 0):.1%}",
            'npv': f"€{kpis.get('NPV_project', 0)/1e6:.1f}M",
            'payback': f"{kpis.get('Payback_years', 0):.1f}y",
            'risk': "Low" if kpis.get('Min_DSCR', 0) > 1.35 else "Medium"
        }
    
    def _get_available_widgets_for_section(self, section: DashboardSection) -> List[DashboardWidget]:
        """Get available widgets for a section based on data availability."""
        section_widgets = [w for w in self.widgets.values() if w.section == section]
        available_widgets = [w for w in section_widgets if self._has_required_data(w)]
        return available_widgets
    
    def _has_required_data(self, widget: DashboardWidget) -> bool:
        """Check if widget has required data."""
        data_map = {
            'financial_results': self.financial_results,
            'ml_predictions': self.ml_predictions,
            'charts_3d': self.charts_3d
        }
        
        return all(data_map.get(req) is not None for req in widget.requires_data)
    
    def _select_section(self, section: DashboardSection):
        """Select dashboard section."""
        self.selected_section = section
    
    def _toggle_widget_expansion(self, widget: DashboardWidget):
        """Toggle widget expansion state."""
        if widget.id in self.expanded_widgets:
            self.expanded_widgets.remove(widget.id)
        else:
            self.expanded_widgets.add(widget.id)
    
    def _create_layout_toggle(self) -> ft.Control:
        """Create layout mode toggle."""
        return ft.PopupMenuButton(
            icon=ft.Icons.VIEW_MODULE,
            tooltip="Layout Options",
            items=[
                ft.PopupMenuItem(text="Compact", on_click=lambda e: self._set_layout_mode("compact")),
                ft.PopupMenuItem(text="Adaptive", on_click=lambda e: self._set_layout_mode("adaptive")),
                ft.PopupMenuItem(text="Detailed", on_click=lambda e: self._set_layout_mode("detailed"))
            ]
        )
    
    def _create_export_button(self) -> ft.Control:
        """Create export button."""
        return ft.IconButton(
            icon=ft.Icons.DOWNLOAD,
            tooltip="Export Dashboard",
            on_click=lambda e: self._handle_action("export_dashboard") if self.on_action else None
        )
    
    def _create_help_button(self) -> ft.Control:
        """Create help button."""
        return ft.IconButton(
            icon=ft.Icons.HELP_OUTLINE,
            tooltip="Dashboard Help",
            on_click=self._show_help_dialog
        )
    
    def _create_widget_menu(self, widget: DashboardWidget) -> ft.Control:
        """Create widget menu."""
        return ft.PopupMenuButton(
            icon=ft.Icons.MORE_VERT,
            icon_size=16,
            items=[
                ft.PopupMenuItem(text="View Details", on_click=lambda e, w=widget: self._view_widget_details(w)),
                ft.PopupMenuItem(text="Hide Widget", on_click=lambda e, w=widget: self._hide_widget(w))
            ]
        )
    
    def _set_layout_mode(self, mode: str):
        """Set layout mode."""
        self.layout_mode = mode
    
    def _handle_action(self, action: str):
        """Handle dashboard actions."""
        if self.on_action:
            self.on_action(action, {})
    
    def _show_customization_dialog(self, e):
        """Show customization dialog."""
        pass
    
    def _show_help_dialog(self, e):
        """Show help dialog."""
        pass
    
    def _view_widget_details(self, widget: DashboardWidget):
        """View widget details."""
        pass
    
    def _hide_widget(self, widget: DashboardWidget):
        """Hide widget."""
        self.hidden_widgets.add(widget.id)
    
    def _create_empty_state(self) -> ft.Control:
        """Create empty state when no data is available."""
        return ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.DASHBOARD, size=64, color=self.theme.get_semantic_color('neutral', '300')),
                ft.Text("Dashboard Ready", size=20, weight=ft.FontWeight.W_600,
                       color=self.theme.get_text_colors()['primary']),
                ft.Text("Run your financial analysis to see interactive results here",
                       size=14, color=self.theme.get_text_colors()['secondary'],
                       text_align=ft.TextAlign.CENTER),
                ft.Container(height=20),
                ModernButton(
                    "🚀 Start Analysis",
                    variant=ButtonVariant.PRIMARY,
                    size=ComponentSize.LG,
                    on_click=lambda e: self._handle_action("start_analysis") if self.on_action else None
                ).build()
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=12),
            padding=ft.padding.all(40),
            alignment=ft.alignment.center,
            expand=True
        )
    
    def _create_no_data_state(self) -> ft.Control:
        """Create no data state for selected section."""
        section_info = {
            DashboardSection.OVERVIEW: ("dashboard", "Overview data will appear after running analysis"),
            DashboardSection.FINANCIAL: ("account_balance", "Financial metrics available after model execution"),
            DashboardSection.TECHNICAL: ("electric_bolt", "Technical data calculated during analysis"),
            DashboardSection.RISK: ("security", "Risk assessment requires completed financial model"),
            DashboardSection.INSIGHTS: ("psychology", "AI insights generated after comprehensive analysis")
        }
        
        icon_name, message = section_info.get(self.selected_section, ("help", "No data available"))
        
        return ft.Container(
            content=ft.Column([
                ft.Icon(getattr(ft.Icons, icon_name.upper()), size=48, 
                       color=self.theme.get_semantic_color('neutral', '300')),
                ft.Text(message, size=14, color=self.theme.get_text_colors()['secondary'],
                       text_align=ft.TextAlign.CENTER)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=12),
            padding=ft.padding.all(40),
            alignment=ft.alignment.center,
            height=200
        )
    
    def _create_placeholder_widget(self, widget: DashboardWidget) -> ft.Control:
        """Create placeholder for widgets without data."""
        return ft.Container(
            content=ft.Column([
                ft.Icon(widget.icon, size=32, color=self.theme.get_semantic_color('neutral', '300')),
                ft.Text(widget.title, weight=ft.FontWeight.W_500,
                       color=self.theme.get_text_colors()['tertiary']),
                ft.Text("Data will be available after analysis", size=12,
                       color=self.theme.get_text_colors()['tertiary'],
                       text_align=ft.TextAlign.CENTER)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
            padding=ft.padding.all(20),
            bgcolor=self.theme.get_semantic_color('neutral', '50'),
            border_radius=8,
            border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
            alignment=ft.alignment.center,
            height=150
        )
    
    # Placeholder methods for remaining widget content creators
    def _create_cashflow_chart_content(self, expanded: bool, compact: bool) -> ft.Control:
        return ft.Container(content=ft.Text("Cashflow chart placeholder"), height=200 if expanded else 150)
    
    def _create_dcf_breakdown_content(self, expanded: bool, compact: bool) -> ft.Control:
        return ft.Container(content=ft.Text("DCF breakdown placeholder"), height=150)
    
    def _create_technical_metrics_content(self, expanded: bool, compact: bool) -> ft.Control:
        return ft.Container(content=ft.Text("Technical metrics placeholder"), height=150)
    
    def _create_energy_production_content(self, expanded: bool, compact: bool) -> ft.Control:
        return ft.Container(content=ft.Text("Energy production chart placeholder"), height=200 if expanded else 150)
    
    def _create_risk_assessment_content(self, expanded: bool, compact: bool) -> ft.Control:
        return ft.Container(content=ft.Text("Risk assessment placeholder"), height=200 if expanded else 150)
    
    def _create_sensitivity_preview_content(self, expanded: bool, compact: bool) -> ft.Control:
        return ft.Container(content=ft.Text("Sensitivity analysis placeholder"), height=150)
    
    def _create_ml_predictions_content(self, expanded: bool, compact: bool) -> ft.Control:
        return ft.Container(content=ft.Text("ML predictions placeholder"), height=200 if expanded else 150)
    
    def _create_3d_charts_content(self, expanded: bool, compact: bool) -> ft.Control:
        return ft.Container(content=ft.Text("3D charts preview placeholder"), height=150)
