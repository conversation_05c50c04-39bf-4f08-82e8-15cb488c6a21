"""
Loading Overlay
===============

Advanced loading overlay with progress tracking.
"""

import flet as ft
from typing import Optional, Callable
import asyncio


class LoadingOverlay:
    """Advanced loading overlay component."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.overlay: Optional[ft.Container] = None
        self.progress_ring: Optional[ft.ProgressRing] = None
        self.progress_bar: Optional[ft.ProgressBar] = None
        self.status_text: Optional[ft.Text] = None
        self.progress_text: Optional[ft.Text] = None
        self.is_visible = False
        self.current_progress = 0.0
        
    def create_overlay(self, 
                      title: str = "Loading...",
                      show_progress: bool = False,
                      show_cancel: bool = False,
                      cancel_callback: Optional[Callable] = None) -> ft.Container:
        """Create the loading overlay."""
        
        # Progress ring (always shown)
        self.progress_ring = ft.ProgressRing(
            width=60,
            height=60,
            stroke_width=4,
            color=ft.Colors.BLUE_600
        )
        
        # Status text
        self.status_text = ft.Text(
            title,
            size=16,
            weight=ft.FontWeight.BOLD,
            text_align=ft.TextAlign.CENTER,
            color=ft.Colors.BLACK87
        )
        
        # Progress components
        progress_components = []
        
        if show_progress:
            self.progress_bar = ft.ProgressBar(
                width=300,
                height=8,
                color=ft.Colors.BLUE_600,
                bgcolor=ft.Colors.GREY_300,
                value=0.0
            )
            
            self.progress_text = ft.Text(
                "0%",
                size=14,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            )
            
            progress_components.extend([
                ft.Container(height=10),
                self.progress_bar,
                ft.Container(height=5),
                self.progress_text
            ])
        
        # Cancel button
        cancel_components = []
        if show_cancel and cancel_callback:
            cancel_button = ft.TextButton(
                "Cancel",
                on_click=lambda _: cancel_callback(),
                style=ft.ButtonStyle(
                    color=ft.Colors.RED_600
                )
            )
            cancel_components.extend([
                ft.Container(height=20),
                cancel_button
            ])
        
        # Main content
        content = ft.Column([
            self.progress_ring,
            ft.Container(height=15),
            self.status_text,
            *progress_components,
            *cancel_components
        ], 
        alignment=ft.MainAxisAlignment.CENTER,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        
        # Loading card
        loading_card = ft.Card(
            content=ft.Container(
                content=content,
                padding=30,
                width=350,
                bgcolor=ft.Colors.WHITE
            ),
            elevation=8
        )
        
        # Overlay container
        self.overlay = ft.Container(
            content=ft.Stack([
                # Semi-transparent background
                ft.Container(
                    bgcolor=ft.Colors.BLACK54,
                    expand=True
                ),
                # Centered loading card
                ft.Container(
                    content=loading_card,
                    alignment=ft.alignment.center,
                    expand=True
                )
            ]),
            visible=False,
            expand=True
        )
        
        return self.overlay
    
    def show(self, 
             title: str = "Loading...",
             show_progress: bool = False,
             show_cancel: bool = False,
             cancel_callback: Optional[Callable] = None):
        """Show the loading overlay."""
        if not self.overlay:
            overlay = self.create_overlay(title, show_progress, show_cancel, cancel_callback)
            self.page.overlay.append(overlay)
        
        self.overlay.visible = True
        self.is_visible = True
        
        if self.status_text:
            self.status_text.value = title
        
        self.page.update()
    
    def hide(self):
        """Hide the loading overlay."""
        if self.overlay:
            self.overlay.visible = False
            self.is_visible = False
            self.page.update()
    
    def update_progress(self, progress: float, message: str = ""):
        """Update progress (0-100)."""
        self.current_progress = progress
        
        if self.progress_bar:
            self.progress_bar.value = progress / 100.0
        
        if self.progress_text:
            self.progress_text.value = f"{progress:.0f}%"
        
        if message and self.status_text:
            self.status_text.value = message
        
        if self.is_visible:
            self.page.update()
    
    def update_message(self, message: str):
        """Update the status message."""
        if self.status_text:
            self.status_text.value = message
            
        if self.is_visible:
            self.page.update()
    
    def pulse_progress_ring(self):
        """Create a pulsing effect on the progress ring."""
        if not self.progress_ring or not self.is_visible:
            return
        
        async def pulse():
            original_width = self.progress_ring.width
            original_height = self.progress_ring.height
            
            # Expand
            self.progress_ring.width = original_width * 1.2
            self.progress_ring.height = original_height * 1.2
            self.page.update()
            await asyncio.sleep(0.3)
            
            # Contract
            self.progress_ring.width = original_width
            self.progress_ring.height = original_height
            self.page.update()
        
        asyncio.create_task(pulse())


class ProgressTracker:
    """Progress tracking utility for long operations."""
    
    def __init__(self, loading_overlay: LoadingOverlay, total_steps: int):
        self.loading_overlay = loading_overlay
        self.total_steps = total_steps
        self.current_step = 0
        self.step_messages = {}
    
    def set_step_message(self, step: int, message: str):
        """Set message for a specific step."""
        self.step_messages[step] = message
    
    def next_step(self, custom_message: str = ""):
        """Move to next step and update progress."""
        self.current_step += 1
        progress = (self.current_step / self.total_steps) * 100
        
        message = custom_message
        if not message and self.current_step in self.step_messages:
            message = self.step_messages[self.current_step]
        elif not message:
            message = f"Step {self.current_step} of {self.total_steps}"
        
        self.loading_overlay.update_progress(progress, message)
    
    def set_progress(self, step: int, custom_message: str = ""):
        """Set specific step progress."""
        self.current_step = step
        progress = (step / self.total_steps) * 100
        
        message = custom_message
        if not message and step in self.step_messages:
            message = self.step_messages[step]
        elif not message:
            message = f"Step {step} of {self.total_steps}"
        
        self.loading_overlay.update_progress(progress, message)
    
    def complete(self, message: str = "Completed!"):
        """Mark as completed."""
        self.loading_overlay.update_progress(100, message)
        
        # Auto-hide after a short delay
        async def auto_hide():
            await asyncio.sleep(1)
            self.loading_overlay.hide()
        
        asyncio.create_task(auto_hide())
