import flet as ft
from typing import List, Dict, Callable


class ModernHistoryDialog:
    """Modern dialog for displaying project history."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.restore_callback: Callable[[str, int], None] = None
        self.close_callback: Callable[[], None] = None
        self.dialog: ft.AlertDialog = None
    
    def set_restore_callback(self, callback: Callable[[str, int], None]):
        self.restore_callback = callback
    
    def set_close_callback(self, callback: Callable[[], None]):
        self.close_callback = callback
    
    async def show(self, project_name: str, versions: List[Dict], title: str):
        """Show the history dialog with versions."""
        
        version_items = []
        for version_info in versions:
            version_num = version_info.get('version', 'unknown')
            created_at = version_info.get('created_at', 'unknown')
            project_id = version_info.get('project_id', project_name)
            
            restore_btn = ft.ElevatedButton(
                "Restore",
                on_click=lambda e, p=project_id, v=version_num: self.restore_callback(p, v) if self.restore_callback else None
            )
            
            version_item = ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(f"Version {version_num}", weight=ft.FontWeight.BOLD),
                        ft.Text(f"Created: {created_at}"),
                        restore_btn
                    ]),
                    padding=10
                )
            )
            version_items.append(version_item)
        
        content = ft.Column(version_items, scroll=ft.ScrollMode.AUTO, expand=True)
        
        self.dialog = ft.AlertDialog(
            title=ft.Text(title),
            content=content,
            actions=[ft.TextButton("Close", on_click=self._close)],
            actions_alignment=ft.MainAxisAlignment.END
        )
        
        self.page.overlay.append(self.dialog)
        self.dialog.open = True
        self.page.update()
    
    def _close(self, e):
        if self.dialog:
            self.dialog.open = False
            self.page.overlay.remove(self.dialog)
            self.page.update()
        if self.close_callback:
            self.close_callback() 