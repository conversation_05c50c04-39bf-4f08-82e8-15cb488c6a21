"""
Client Profile Form
===================

Form component for client profile data entry.
"""

import flet as ft
from typing import Optional, Callable, Any, Dict

from models.client_profile import ClientProfile


class ClientProfileForm:
    """Form component for client profile data."""
    
    def __init__(self, client_profile: ClientProfile):
        self.client_profile = client_profile
        self.on_data_changed: Optional[Callable[[str, Any], None]] = None
        
        # Form fields
        self.company_name_field: Optional[ft.TextField] = None
        self.client_name_field: Optional[ft.TextField] = None
        self.contact_email_field: Optional[ft.TextField] = None
        self.phone_field: Optional[ft.TextField] = None
        self.project_name_field: Optional[ft.TextField] = None
        self.project_location_field: Optional[ft.TextField] = None
        self.project_capacity_field: Optional[ft.TextField] = None
        self.preferred_currency_dropdown: Optional[ft.Dropdown] = None
    
    def build(self) -> ft.Container:
        """Build the client profile form."""
        
        # Company information section with modern styling
        company_section = ft.Container(
            content=ft.Column([
                # Modern section header
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Icon(ft.Icons.BUSINESS_CENTER, color=ft.Colors.WHITE, size=18),
                            width=36,
                            height=36,
                            bgcolor=ft.Colors.BLUE_600,
                            border_radius=18,
                            alignment=ft.alignment.center
                        ),
                        ft.Container(width=10),
                        ft.Text("Company Information", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700)
                    ]),
                    padding=ft.padding.only(bottom=15)
                ),

                self._create_modern_company_name_field(),
                self._create_modern_client_name_field(),
                self._create_modern_contact_email_field(),
                self._create_modern_phone_field()
            ], spacing=12),
            padding=20,
            bgcolor=ft.Colors.BLUE_50,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.BLUE_100)
        )

        # Project information section with modern styling
        project_section = ft.Container(
            content=ft.Column([
                # Modern section header
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Icon(ft.Icons.SOLAR_POWER, color=ft.Colors.WHITE, size=18),
                            width=36,
                            height=36,
                            bgcolor=ft.Colors.GREEN_600,
                            border_radius=18,
                            alignment=ft.alignment.center
                        ),
                        ft.Container(width=10),
                        ft.Text("Project Information", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700)
                    ]),
                    padding=ft.padding.only(bottom=15)
                ),

                self._create_modern_project_name_field(),
                self._create_modern_project_location_field(),
                ft.Row([
                    self._create_modern_project_capacity_field(),
                    ft.Container(width=15),
                    self._create_modern_currency_dropdown()
                ])
            ], spacing=12),
            padding=20,
            bgcolor=ft.Colors.GREEN_50,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREEN_100)
        )
        
        return ft.Container(
            content=ft.Column([
                company_section,
                ft.Container(height=20),
                project_section
            ]),
            padding=0  # Remove padding since sections have their own
        )
    
    def _create_company_name_field(self) -> ft.TextField:
        """Create company name field."""
        self.company_name_field = ft.TextField(
            label="Company Name *",
            value=self.client_profile.company_name,
            hint_text="Enter company name",
            on_change=lambda e: self._on_field_changed('company_name', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.BUSINESS,
            tooltip="Required"
        )
        return self.company_name_field

    def _create_modern_company_name_field(self) -> ft.TextField:
        """Create modern styled company name field."""
        self.company_name_field = ft.TextField(
            label="Company Name *",
            value=self.client_profile.company_name,
            hint_text="Enter your company name",
            on_change=lambda e: self._on_field_changed('company_name', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.BUSINESS_CENTER,
            tooltip="Required field",
            # Modern styling
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.BLUE_500,
            focused_bgcolor=ft.Colors.BLUE_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )
        return self.company_name_field
    
    def _create_client_name_field(self) -> ft.TextField:
        """Create client name field."""
        self.client_name_field = ft.TextField(
            label="Client Name *",
            value=self.client_profile.client_name,
            hint_text="Enter client contact name",
            on_change=lambda e: self._on_field_changed('client_name', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.PERSON,
            tooltip="Required"
        )
        return self.client_name_field
    
    def _create_contact_email_field(self) -> ft.TextField:
        """Create contact email field."""
        self.contact_email_field = ft.TextField(
            label="Contact Email",
            value=self.client_profile.contact_email,
            hint_text="Enter email address",
            keyboard_type=ft.KeyboardType.EMAIL,
            on_change=lambda e: self._on_field_changed('contact_email', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.EMAIL,
            tooltip="Format: <EMAIL>"
        )
        return self.contact_email_field
    
    def _create_phone_field(self) -> ft.TextField:
        """Create phone field."""
        self.phone_field = ft.TextField(
            label="Phone Number",
            value=self.client_profile.phone,
            hint_text="Enter phone number",
            keyboard_type=ft.KeyboardType.PHONE,
            on_change=lambda e: self._on_field_changed('phone', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.PHONE,
            tooltip="Required, Format: +123456789"
        )
        return self.phone_field
    
    def _create_project_name_field(self) -> ft.TextField:
        """Create project name field."""
        self.project_name_field = ft.TextField(
            label="Project Name *",
            value=self.client_profile.project_name,
            hint_text="Enter project name",
            on_change=lambda e: self._on_field_changed('project_name', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.WORK,
            tooltip="Required"
        )
        return self.project_name_field
    
    def _create_project_location_field(self) -> ft.TextField:
        """Create project location field."""
        self.project_location_field = ft.TextField(
            label="Project Location",
            value=self.client_profile.project_location,
            hint_text="Enter project location",
            on_change=lambda e: self._on_field_changed('project_location', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.LOCATION_ON
        )
        return self.project_location_field
    
    def _create_project_capacity_field(self) -> ft.TextField:
        """Create project capacity field."""
        self.project_capacity_field = ft.TextField(
            label="Project Capacity (MW)",
            value=str(self.client_profile.project_capacity_mw) if self.client_profile.project_capacity_mw else "",
            hint_text="Enter capacity in MW",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=self._on_capacity_changed,
            expand=True,
            prefix_icon=ft.Icons.FLASH_ON
        )
        return self.project_capacity_field
    
    def _create_currency_dropdown(self) -> ft.Dropdown:
        """Create currency dropdown."""
        self.preferred_currency_dropdown = ft.Dropdown(
            label="Preferred Currency",
            value=self.client_profile.preferred_currency,
            options=[
                ft.dropdown.Option("EUR", "EUR - Euro"),
                ft.dropdown.Option("USD", "USD - US Dollar"),
                ft.dropdown.Option("MAD", "MAD - Moroccan Dirham"),
                ft.dropdown.Option("GBP", "GBP - British Pound")
            ],
            on_change=lambda e: self._on_field_changed('preferred_currency', e.control.value),
            expand=True
        )
        return self.preferred_currency_dropdown
    
    def _create_modern_client_name_field(self) -> ft.TextField:
        """Create modern styled client name field."""
        self.client_name_field = ft.TextField(
            label="Contact Person *",
            value=self.client_profile.client_name,
            hint_text="Enter contact person name",
            on_change=lambda e: self._on_field_changed('client_name', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.PERSON,
            tooltip="Required field",
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.BLUE_500,
            focused_bgcolor=ft.Colors.BLUE_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )
        return self.client_name_field

    def _create_modern_contact_email_field(self) -> ft.TextField:
        """Create modern styled contact email field."""
        self.contact_email_field = ft.TextField(
            label="Email Address",
            value=self.client_profile.contact_email,
            hint_text="<EMAIL>",
            keyboard_type=ft.KeyboardType.EMAIL,
            on_change=lambda e: self._on_field_changed('contact_email', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.EMAIL,
            tooltip="Business email address",
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.BLUE_500,
            focused_bgcolor=ft.Colors.BLUE_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )
        return self.contact_email_field

    def _create_modern_phone_field(self) -> ft.TextField:
        """Create modern styled phone field."""
        self.phone_field = ft.TextField(
            label="Phone Number",
            value=self.client_profile.phone,
            hint_text="+****************",
            keyboard_type=ft.KeyboardType.PHONE,
            on_change=lambda e: self._on_field_changed('phone', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.PHONE,
            tooltip="Business phone number",
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.BLUE_500,
            focused_bgcolor=ft.Colors.BLUE_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )
        return self.phone_field

    def _create_modern_project_name_field(self) -> ft.TextField:
        """Create modern styled project name field."""
        self.project_name_field = ft.TextField(
            label="Project Name *",
            value=self.client_profile.project_name,
            hint_text="Enter descriptive project name",
            on_change=lambda e: self._on_field_changed('project_name', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.SOLAR_POWER,
            tooltip="Required field",
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.GREEN_500,
            focused_bgcolor=ft.Colors.GREEN_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )
        return self.project_name_field

    def _create_modern_project_location_field(self) -> ft.TextField:
        """Create modern styled project location field."""
        self.project_location_field = ft.TextField(
            label="Project Location",
            value=self.client_profile.project_location,
            hint_text="Enter project location",
            on_change=lambda e: self._on_field_changed('project_location', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.LOCATION_ON,
            tooltip="Project geographical location",
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.GREEN_500,
            focused_bgcolor=ft.Colors.GREEN_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )
        return self.project_location_field

    def _create_modern_project_capacity_field(self) -> ft.Container:
        """Create modern styled project capacity field."""
        self.project_capacity_field = ft.TextField(
            label="Capacity",
            value=str(self.client_profile.project_capacity_mw) if self.client_profile.project_capacity_mw else "",
            hint_text="10.0",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=lambda e: self._on_field_changed('project_capacity_mw', e.control.value),
            expand=True,
            prefix_icon=ft.Icons.BOLT,
            suffix_text="MW",
            tooltip="Project capacity in megawatts",
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.GREEN_500,
            focused_bgcolor=ft.Colors.GREEN_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )
        return ft.Container(content=self.project_capacity_field, expand=True)

    def _create_modern_currency_dropdown(self) -> ft.Container:
        """Create modern styled currency dropdown."""
        self.preferred_currency_dropdown = ft.Dropdown(
            label="Currency",
            value=self.client_profile.preferred_currency,
            options=[
                ft.dropdown.Option("EUR", "🇪🇺 Euro"),
                ft.dropdown.Option("USD", "🇺🇸 US Dollar"),
                ft.dropdown.Option("MAD", "🇲🇦 Dirham"),
                ft.dropdown.Option("GBP", "🇬🇧 Pound")
            ],
            on_change=lambda e: self._on_field_changed('preferred_currency', e.control.value),
            expand=True,
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.GREEN_500,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )
        return ft.Container(content=self.preferred_currency_dropdown, expand=True)

    def _on_field_changed(self, field_name: str, value: str):
        """Handle field value changes."""
        if self.on_data_changed:
            self.on_data_changed(field_name, value)
    
    def _on_capacity_changed(self, e):
        """Handle capacity field changes with validation."""
        try:
            value = float(e.control.value) if e.control.value else None
            if self.on_data_changed:
                self.on_data_changed('project_capacity_mw', value)
        except ValueError:
            # Invalid number, ignore
            pass
    
    def update_data(self, client_profile: ClientProfile):
        """Update form with new client profile data."""
        self.client_profile = client_profile

        if self.company_name_field:
            self.company_name_field.value = client_profile.company_name
            self.company_name_field.update()
        if self.client_name_field:
            self.client_name_field.value = client_profile.client_name
            self.client_name_field.update()
        if self.contact_email_field:
            self.contact_email_field.value = client_profile.contact_email
            self.contact_email_field.update()
        if self.phone_field:
            self.phone_field.value = client_profile.phone
            self.phone_field.update()
        if self.project_name_field:
            self.project_name_field.value = client_profile.project_name
            self.project_name_field.update()
        if self.project_location_field:
            self.project_location_field.value = client_profile.project_location
            self.project_location_field.update()
        if self.project_capacity_field:
            self.project_capacity_field.value = str(client_profile.project_capacity_mw) if client_profile.project_capacity_mw else ""
            self.project_capacity_field.update()
        if self.preferred_currency_dropdown:
            self.preferred_currency_dropdown.value = client_profile.preferred_currency
            self.preferred_currency_dropdown.update()
    
    def validate(self) -> Dict[str, str]:
        """Validate form data."""
        return self.client_profile.validate()
    
    def get_data(self) -> ClientProfile:
        """Get current form data as ClientProfile."""
        return self.client_profile
