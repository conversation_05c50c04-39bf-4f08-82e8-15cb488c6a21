"""
Project Parameters Form
=======================

Form component for project parameters data entry.
"""

import flet as ft
import logging
from typing import Optional, Callable, Any, Dict, Union

from models.project_assumptions import EnhancedProjectAssumptions
from services.config_service import ConfigurationService

# Try to import logging utilities
try:
    from utils.logging_utils import (
        CorrelationIDManager,
        LoggerFactory,
        EnhancedAnalysisLogger
    )
    LOGGING_UTILS_AVAILABLE = True
except ImportError:
    # Fallback for when logging utilities are not available
    LOGGING_UTILS_AVAILABLE = False
    
    class LoggerFactory:
        @staticmethod
        def get_component_logger(name):
            return logging.getLogger(f"components.{name}")
    
    class CorrelationIDManager:
        @staticmethod
        def get_correlation_id():
            return "NO-CORR-ID"
        
        @staticmethod
        def ensure_correlation_id():
            return "NO-CORR-ID"


class ProjectParamsForm:
    """Form component for project parameters."""
    
    def __init__(self, project_assumptions: EnhancedProjectAssumptions):
        self.project_assumptions = project_assumptions
        self.on_data_changed: Optional[Callable[[str, Any], None]] = None
        
        # Form fields - will be created in build()
        self.fields: Dict[str, Union[ft.TextField, ft.Dropdown, ft.Checkbox]] = {}
        
        # SIMEST configuration service
        try:
            self.config_service = ConfigurationService()
        except Exception:
            self.config_service = None
        
        # SIMEST display fields for real-time calculations
        self.simest_grant_display: Optional[ft.Text] = None
        self.simest_soft_loan_display: Optional[ft.Text] = None
        self.simest_terms_display: Optional[ft.Text] = None
        
        # Initialize logging
        self.logger = LoggerFactory.get_component_logger("project_params_form")
        self.logger.info("ProjectParamsForm initialized")
    
    def build(self) -> ft.Container:
        """Build the project parameters form."""
        
        # Technical parameters section
        technical_section = self._create_technical_section()
        
        # Financial parameters section
        financial_section = self._create_financial_section()
        
        # Grant parameters section
        grant_section = self._create_grant_section()
        
        return ft.Container(
            content=ft.Column([
                technical_section,
                ft.Container(height=20),
                financial_section,
                ft.Container(height=20),
                grant_section
            ]),
            padding=15
        )
    
    def _create_technical_section(self) -> ft.Container:
        """Create technical parameters section with modern styling."""
        return ft.Container(
            content=ft.Column([
                # Modern section header
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Icon(ft.Icons.ENGINEERING, color=ft.Colors.WHITE, size=20),
                            width=40,
                            height=40,
                            bgcolor=ft.Colors.BLUE_600,
                            border_radius=20,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(width=12),
                        ft.Text("Technical Parameters", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                    ]),
                    padding=ft.padding.only(bottom=20),
                ),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("capacity_mw", "Capacity", "10.0", "MW", ft.Icons.BOLT, "Total installed capacity of the project")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("project_life_years", "Project Life", "25", "years", ft.Icons.SCHEDULE, "Expected operational lifetime of the project")
                    ], col={"sm": 6})
                ]),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("production_mwh_year1", "Production Year 1", "18000", "MWh", ft.Icons.ENERGY_SAVINGS_LEAF, "Expected energy production in first year")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("degradation_rate", "Degradation Rate", "0.5", "%/year", ft.Icons.TRENDING_DOWN, "Annual degradation rate as percentage")
                    ], col={"sm": 6})
                ]),

                # Add project location field
                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_location_field("project_location", "Project Location", "Ouarzazate", "Select the primary location for your project")
                    ], col={"sm": 12})
                ])
            ]),
            padding=25,
            bgcolor=ft.Colors.BLUE_50,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.BLUE_100)
        )
    
    def _create_financial_section(self) -> ft.Container:
        """Create financial parameters section with modern styling."""
        return ft.Container(
            content=ft.Column([
                # Modern section header
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Icon(ft.Icons.EURO_SYMBOL, color=ft.Colors.WHITE, size=20),
                            width=40,
                            height=40,
                            bgcolor=ft.Colors.GREEN_600,
                            border_radius=20,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(width=12),
                        ft.Text("Financial Parameters", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                    ]),
                    padding=ft.padding.only(bottom=20),
                ),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("ppa_price_eur_kwh", "PPA Price", "0.045", "€/kWh",
                                                ft.Icons.HANDSHAKE, "Power Purchase Agreement price in euros per kWh")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("ppa_escalation", "PPA Escalation", "0.0", "%",
                                                ft.Icons.TRENDING_UP, "Annual escalation rate of PPA price as percentage")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("debt_ratio", "Debt Ratio", "0.75", "ratio",
                                                ft.Icons.PIE_CHART, "Debt-to-total-financing ratio (0-1)")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("interest_rate", "Interest Rate", "6.0", "%",
                                                ft.Icons.PERCENT, "Annual interest rate on debt as percentage")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("debt_years", "Debt Tenor", "15", "years",
                                                ft.Icons.SCHEDULE, "Duration of debt repayment period")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("discount_rate", "Discount Rate", "8.0", "%",
                                                ft.Icons.CALCULATE, "Discount rate for NPV calculations as percentage")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("tax_rate", "Tax Rate", "15.0", "%",
                                                ft.Icons.ACCOUNT_BALANCE, "Corporate tax rate as percentage")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("land_lease_eur_mw_year", "Land Lease", "2000", "€/MW/year",
                                                ft.Icons.LANDSCAPE, "Annual land lease cost per MW in euros")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("tax_holiday", "Tax Grace Period", "5", "years",
                                                ft.Icons.HOLIDAY_VILLAGE, "Number of years with tax exemption")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("grace_years", "Debt Grace Period", "2", "years",
                                                ft.Icons.PAUSE_CIRCLE, "Number of years before debt principal repayment starts")
                    ], col={"sm": 6})
                ])
            ]),
            padding=25,
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_grant_section(self) -> ft.Container:
        """Create grant parameters section with modern styling."""
        return ft.Container(
            content=ft.Column([
                # Modern section header
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Icon(ft.Icons.CARD_GIFTCARD, color=ft.Colors.WHITE, size=20),
                            width=40,
                            height=40,
                            bgcolor=ft.Colors.PURPLE_600,
                            border_radius=20,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(width=12),
                        ft.Text("Grant Parameters", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_700),
                    ]),
                    padding=ft.padding.only(bottom=20),
                ),

                # SIMEST - Piano Mattei Program Section
                self._create_simest_section(),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("grant_meur_masen", "MASEN Grant", "0.0", "M€",
                                                ft.Icons.SOLAR_POWER, "Grant funding from MASEN in millions of euros")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("grant_meur_connection", "Connection Grant", "0.0", "M€",
                                                ft.Icons.ELECTRICAL_SERVICES, "Grant funding for grid connection in millions of euros")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("grant_meur_cri", "CRI Regional Investment Grant", "0.0", "M€",
                                                ft.Icons.LOCATION_CITY, "Grant funding from CRI Regional Investment Center in millions of euros")
                    ], col={"sm": 12})
                ])
            ]),
            padding=25,
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_field(self, field_name: str, label: str, default_value: str, tooltip: str = None) -> ft.TextField:
        """Create a form field."""
        current_value = getattr(self.project_assumptions, field_name, default_value)

        # Handle percentage display for percentage fields
        if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
            display_value = float(current_value) * 100.0 if current_value else 0.0
        else:
            display_value = current_value

        field = ft.TextField(
            label=label,
            value=str(display_value),
            hint_text=f"Enter {label.lower()}",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            expand=True,
            tooltip=tooltip
        )

        self.fields[field_name] = field
        return field

    def _create_modern_field(self, field_name: str, label: str, default_value: str,
                            unit: str, icon: str, tooltip: str = None) -> ft.Container:
        """Create a modern styled form field."""
        current_value = getattr(self.project_assumptions, field_name, default_value)

        # Handle percentage display for percentage fields
        if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
            display_value = float(current_value) * 100.0 if current_value else 0.0
        else:
            display_value = current_value

        field = ft.TextField(
            label=label,
            value=str(display_value),
            hint_text=f"Enter {label.lower()}",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            prefix_icon=icon,
            suffix_text=unit,
            expand=True,
            tooltip=tooltip,
            # Modern styling
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.BLUE_500,
            focused_bgcolor=ft.Colors.BLUE_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )

        self.fields[field_name] = field
        return ft.Container(
            content=field,
            margin=ft.margin.only(bottom=15)
        )
    
    def _create_location_field(self, field_name: str, label: str, default_value: str, tooltip: str = None) -> ft.Dropdown:
        """Create a location dropdown field with specific cities instead of countries."""
        current_value = getattr(self.project_assumptions, field_name, default_value)

        # Use specific cities instead of generic country names
        dropdown = ft.Dropdown(
            label=label,
            value=current_value,
            options=[
                # Morocco locations
                ft.dropdown.Option("Ouarzazate"),
                ft.dropdown.Option("Dakhla"),
                ft.dropdown.Option("Laâyoune"),
                ft.dropdown.Option("Noor_Midelt"),
                ft.dropdown.Option("Tarfaya"),
                # Italy locations
                ft.dropdown.Option("Sicily_Catania"),
                ft.dropdown.Option("Puglia_Brindisi"),
                # Spain locations
                ft.dropdown.Option("Andalusia_Sevilla"),
                # Other countries (for backward compatibility)
                ft.dropdown.Option("France"),
                ft.dropdown.Option("Tunisia"),
                ft.dropdown.Option("Algeria"),
                ft.dropdown.Option("Other")
            ],
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            expand=True,
            tooltip=tooltip
        )

        self.fields[field_name] = dropdown
        return dropdown

    def _create_modern_location_field(self, field_name: str, label: str, default_value: str, tooltip: str = None) -> ft.Container:
        """Create a modern styled location dropdown field."""
        current_value = getattr(self.project_assumptions, field_name, default_value)

        dropdown = ft.Dropdown(
            label=label,
            value=current_value,
            options=[
                # Morocco locations (primary focus)
                ft.dropdown.Option("Ouarzazate", text="🇲🇦 Ouarzazate"),
                ft.dropdown.Option("Dakhla", text="🇲🇦 Dakhla"),
                ft.dropdown.Option("Laâyoune", text="🇲🇦 Laâyoune"),
                ft.dropdown.Option("Noor_Midelt", text="🇲🇦 Noor Midelt"),
                ft.dropdown.Option("Tarfaya", text="🇲🇦 Tarfaya"),
                # Italy locations
                ft.dropdown.Option("Sicily_Catania", text="🇮🇹 Sicily (Catania)"),
                ft.dropdown.Option("Puglia_Brindisi", text="🇮🇹 Puglia (Brindisi)"),
                # Spain locations
                ft.dropdown.Option("Andalusia_Sevilla", text="🇪🇸 Andalusia (Sevilla)"),
                # Other countries (for backward compatibility)
                ft.dropdown.Option("France", text="🇫🇷 France"),
                ft.dropdown.Option("Tunisia", text="🇹🇳 Tunisia"),
                ft.dropdown.Option("Algeria", text="🇩🇿 Algeria"),
                ft.dropdown.Option("Other", text="🌍 Other")
            ],
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            expand=True,
            tooltip=tooltip,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.BLUE_200,
            border_radius=8,
            content_padding=ft.padding.all(12)
        )

        self.fields[field_name] = dropdown

        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.LOCATION_ON, color=ft.Colors.BLUE_600, size=20),
                    ft.Text(label, size=14, weight=ft.FontWeight.W_500, color=ft.Colors.BLUE_700),
                ], spacing=8),
                ft.Container(height=8),
                dropdown,
                ft.Container(
                    content=ft.Text(
                        tooltip or "Select the primary location for your project",
                        size=11,
                        color=ft.Colors.GREY_600,
                        italic=True
                    ),
                    margin=ft.margin.only(top=4)
                ) if tooltip else ft.Container()
            ]),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.BLUE_100),
            margin=ft.margin.only(bottom=15)
        )
    
    def _create_simest_section(self) -> ft.Container:
        """Create SIMEST - Piano Mattei Program section with split structure display."""
        # Get current SIMEST facility amount
        current_facility = getattr(self.project_assumptions, '_grant_meur_piano_mattei_unified', 0.0)
        current_southern_italy = getattr(self.project_assumptions, 'company_southern_italy', False)
        
        # Create Southern Italy toggle
        southern_italy_toggle = ft.Checkbox(
            label="Company based in Southern Italy (20% grant vs 10% standard)",
            value=current_southern_italy,
            on_change=lambda e: self._on_southern_italy_changed(e.control.value)
        )
        self.fields['company_southern_italy'] = southern_italy_toggle
        
        # Create SIMEST facility field
        simest_field = ft.TextField(
            label="SIMEST - Piano Mattei Program (Total Facility)",
            value=str(current_facility),
            hint_text="Enter total SIMEST facility amount",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=lambda e: self._on_simest_facility_changed(e.control.value),
            prefix_icon=ft.Icons.ACCOUNT_BALANCE,
            suffix_text="M€",
            expand=True,
            tooltip="Total SIMEST facility amount (includes both grant and soft loan components)",
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.BLUE_300,
            focused_border_color=ft.Colors.BLUE_600,
            focused_bgcolor=ft.Colors.BLUE_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )
        self.fields['_grant_meur_piano_mattei_unified'] = simest_field
        
        # Create real-time calculation displays
        self.simest_grant_display = ft.Text(
            self._format_grant_display(current_facility, current_southern_italy),
            size=13,
            color=ft.Colors.GREEN_700,
            weight=ft.FontWeight.W_500
        )
        
        self.simest_soft_loan_display = ft.Text(
            self._format_soft_loan_display(current_facility, current_southern_italy),
            size=13,
            color=ft.Colors.BLUE_700,
            weight=ft.FontWeight.W_500
        )
        
        self.simest_terms_display = ft.Text(
            self._format_terms_display(),
            size=12,
            color=ft.Colors.GREY_700,
            italic=True
        )
        
        # Get program limits for display
        try:
            if self.config_service:
                config = self.config_service.load_simest_config()
                min_facility = config.get('minimum_facility_meur', 0.1)
                max_facility = config.get('maximum_facility_meur', 50.0)
                limits_text = f"Program limits: {min_facility} - {max_facility} M€"
            else:
                limits_text = "Program limits: 0.1 - 50.0 M€"
        except Exception:
            limits_text = "Program limits: 0.1 - 50.0 M€"
        
        return ft.Container(
            content=ft.Column([
                # Section header with enhanced styling
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Icon(ft.Icons.ACCOUNT_BALANCE, color=ft.Colors.WHITE, size=20),
                            width=40,
                            height=40,
                            bgcolor=ft.Colors.BLUE_600,
                            border_radius=20,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(width=12),
                        ft.Column([
                            ft.Text("SIMEST - Piano Mattei Program", 
                                   size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                            ft.Text("€200M Italian Development Fund for African Projects", 
                                   size=12, color=ft.Colors.BLUE_600, italic=True)
                        ], spacing=2)
                    ]),
                    padding=ft.padding.only(bottom=15),
                ),
                
                # Program description
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "The SIMEST 'Strengthening African Markets' program provides combined grant and preferential financing for Italian companies investing in Africa.",
                            size=13,
                            color=ft.Colors.GREY_700
                        ),
                        ft.Container(height=8),
                        ft.Row([
                            ft.Icon(ft.Icons.INFO_OUTLINE, size=16, color=ft.Colors.BLUE_600),
                            ft.Text(
                                "Structure: Non-repayable grant (10-20%) + Preferential soft loan (80-90%)",
                                size=12,
                                color=ft.Colors.BLUE_700,
                                weight=ft.FontWeight.W_500
                            )
                        ], spacing=8)
                    ]),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.BLUE_100),
                    margin=ft.margin.only(bottom=15)
                ),
                
                # Southern Italy toggle
                ft.Container(
                    content=southern_italy_toggle,
                    padding=ft.padding.symmetric(vertical=8),
                    margin=ft.margin.only(bottom=10)
                ),
                
                # SIMEST facility amount field
                ft.Container(
                    content=simest_field,
                    margin=ft.margin.only(bottom=15)
                ),
                
                # Real-time split calculations display
                ft.Container(
                    content=ft.Column([
                        ft.Text("Program Structure Breakdown:", 
                               size=14, weight=ft.FontWeight.W_600, color=ft.Colors.GREY_800),
                        ft.Container(height=8),
                        
                        # Grant component
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.CARD_GIFTCARD, size=18, color=ft.Colors.GREEN_600),
                                ft.Container(width=8),
                                self.simest_grant_display
                            ]),
                            padding=ft.padding.symmetric(vertical=4)
                        ),
                        
                        # Soft loan component
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.ACCOUNT_BALANCE_WALLET, size=18, color=ft.Colors.BLUE_600),
                                ft.Container(width=8),
                                self.simest_soft_loan_display
                            ]),
                            padding=ft.padding.symmetric(vertical=4)
                        ),
                        
                        # Loan terms
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.SCHEDULE, size=16, color=ft.Colors.GREY_600),
                                ft.Container(width=10),
                                self.simest_terms_display
                            ]),
                            padding=ft.padding.symmetric(vertical=4)
                        ),
                        
                        ft.Container(height=8),
                        
                        # Program limits and eligibility
                        ft.Text(limits_text, size=11, color=ft.Colors.GREY_600),
                        ft.Text("Eligible: Italian companies investing in African countries", 
                               size=11, color=ft.Colors.GREY_600, italic=True)
                    ]),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.GREY_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.GREY_200),
                    margin=ft.margin.only(bottom=10)
                )
            ]),
            padding=ft.padding.all(15),
            border=ft.border.all(1, ft.Colors.BLUE_200),
            border_radius=12,
            margin=ft.margin.only(bottom=15)
        )
    
    def _format_grant_display(self, facility_amount: float, is_southern_italy: bool) -> str:
        """Format the grant component display text."""
        if facility_amount <= 0:
            return "Non-repayable Grant: 0.00 M€ (0%)"
        
        try:
            if self.config_service:
                grant_percentage = self.config_service.get_simest_grant_percentage(is_southern_italy)
            else:
                grant_percentage = 0.20 if is_southern_italy else 0.10
        except Exception:
            grant_percentage = 0.20 if is_southern_italy else 0.10
        
        grant_amount = facility_amount * grant_percentage
        return f"Non-repayable Grant: {grant_amount:.2f} M€ ({grant_percentage*100:.0f}%)"
    
    def _format_soft_loan_display(self, facility_amount: float, is_southern_italy: bool) -> str:
        """Format the soft loan component display text."""
        if facility_amount <= 0:
            return "Preferential Soft Loan: 0.00 M€ (0%)"
        
        try:
            if self.config_service:
                grant_percentage = self.config_service.get_simest_grant_percentage(is_southern_italy)
            else:
                grant_percentage = 0.20 if is_southern_italy else 0.10
        except Exception:
            grant_percentage = 0.20 if is_southern_italy else 0.10
        
        soft_loan_percentage = 1.0 - grant_percentage
        soft_loan_amount = facility_amount * soft_loan_percentage
        return f"Preferential Soft Loan: {soft_loan_amount:.2f} M€ ({soft_loan_percentage*100:.0f}%)"
    
    def _format_terms_display(self) -> str:
        """Format the loan terms display text."""
        try:
            if self.config_service:
                terms = self.config_service.get_simest_soft_loan_terms()
                interest_rate = terms['interest_rate'] * 100  # Convert to percentage
                tenor = terms['tenor_years']
                grace = terms['grace_years']
            else:
                interest_rate = 0.511
                tenor = 6
                grace = 2
        except Exception:
            interest_rate = 0.511
            tenor = 6
            grace = 2
        
        return f"Soft Loan Terms: {interest_rate:.3f}% interest, {tenor} years tenor, {grace} years grace"
    
    def _on_simest_facility_changed(self, value: str):
        """Handle SIMEST facility amount changes with real-time updates."""
        try:
            facility_amount = float(value) if value else 0.0
            
            # Update the unified field in project assumptions
            if self.on_data_changed:
                self.on_data_changed('_grant_meur_piano_mattei_unified', facility_amount)
            
            # Update real-time displays
            self._update_simest_displays()
            
            # Validate against program limits
            self._validate_simest_amount(facility_amount)
            
        except ValueError:
            # Invalid number, ignore
            pass
    
    def _on_southern_italy_changed(self, value: bool):
        """Handle Southern Italy toggle changes."""
        if self.on_data_changed:
            self.on_data_changed('company_southern_italy', value)
        
        # Update real-time displays
        self._update_simest_displays()
    
    def _update_simest_displays(self):
        """Update the real-time SIMEST calculation displays."""
        try:
            # Get current values
            facility_field = self.fields.get('_grant_meur_piano_mattei_unified')
            southern_italy_field = self.fields.get('company_southern_italy')
            
            if facility_field and southern_italy_field:
                facility_amount = float(facility_field.value) if facility_field.value else 0.0
                is_southern_italy = southern_italy_field.value
                
                # Update display texts
                if self.simest_grant_display:
                    self.simest_grant_display.value = self._format_grant_display(facility_amount, is_southern_italy)
                    self.simest_grant_display.update()
                
                if self.simest_soft_loan_display:
                    self.simest_soft_loan_display.value = self._format_soft_loan_display(facility_amount, is_southern_italy)
                    self.simest_soft_loan_display.update()
        except Exception:
            pass
    
    def _validate_simest_amount(self, amount: float):
        """Validate SIMEST facility amount against program limits."""
        correlation_id = CorrelationIDManager.get_correlation_id()
        
        self.logger.info(
            f"Validating SIMEST facility amount: {amount:.2f} M€",
            extra={
                'correlation_id': correlation_id,
                'amount': amount,
                'action': 'simest_validation'
            }
        )
        
        if amount <= 0:
            self.logger.debug(
                "SIMEST amount is zero or negative, skipping validation",
                extra={'correlation_id': correlation_id, 'amount': amount}
            )
            return
        
        validation_result = {'amount': amount, 'valid': True, 'issues': []}
        
        try:
            if self.config_service:
                config = self.config_service.load_simest_config()
                min_facility = config.get('minimum_facility_meur', 0.1)
                max_facility = config.get('maximum_facility_meur', 50.0)
                
                self.logger.debug(
                    f"Using config service limits: min={min_facility}, max={max_facility}",
                    extra={'correlation_id': correlation_id, 'min_facility': min_facility, 'max_facility': max_facility}
                )
                
                if amount < min_facility:
                    message = f"SIMEST facility amount ({amount:.2f} M€) is below minimum ({min_facility} M€)"
                    validation_result['valid'] = False
                    validation_result['issues'].append({'type': 'below_minimum', 'message': message})
                    
                    self.logger.warning(
                        f"SIMEST validation failed: below minimum - {message}",
                        extra={'correlation_id': correlation_id, 'validation_result': validation_result}
                    )
                    
                    self._show_validation_message(message, ft.Colors.ORANGE_700)
                    
                elif amount > max_facility:
                    message = f"SIMEST facility amount ({amount:.2f} M€) exceeds maximum ({max_facility} M€)"
                    validation_result['valid'] = False
                    validation_result['issues'].append({'type': 'above_maximum', 'message': message})
                    
                    self.logger.warning(
                        f"SIMEST validation failed: above maximum - {message}",
                        extra={'correlation_id': correlation_id, 'validation_result': validation_result}
                    )
                    
                    self._show_validation_message(message, ft.Colors.RED_700)
                    
        except Exception as e:
            self.logger.error(
                f"Error loading SIMEST config, using fallback validation: {str(e)}",
                extra={'correlation_id': correlation_id, 'error': str(e)}
            )
            
            # Fallback validation
            if amount < 0.1:
                message = f"SIMEST facility amount ({amount:.2f} M€) is below minimum (0.1 M€)"
                validation_result['valid'] = False
                validation_result['issues'].append({'type': 'below_minimum_fallback', 'message': message})
                
                self.logger.warning(
                    f"SIMEST fallback validation failed: below minimum - {message}",
                    extra={'correlation_id': correlation_id, 'validation_result': validation_result}
                )
                
                self._show_validation_message(message, ft.Colors.ORANGE_700)
                
            elif amount > 50.0:
                message = f"SIMEST facility amount ({amount:.2f} M€) exceeds maximum (50.0 M€)"
                validation_result['valid'] = False
                validation_result['issues'].append({'type': 'above_maximum_fallback', 'message': message})
                
                self.logger.warning(
                    f"SIMEST fallback validation failed: above maximum - {message}",
                    extra={'correlation_id': correlation_id, 'validation_result': validation_result}
                )
                
                self._show_validation_message(message, ft.Colors.RED_700)
        
        # Log final validation result
        if validation_result['valid']:
            self.logger.info(
                f"SIMEST validation passed for amount: {amount:.2f} M€",
                extra={'correlation_id': correlation_id, 'validation_result': validation_result}
            )
        else:
            self.logger.warning(
                f"SIMEST validation failed with {len(validation_result['issues'])} issues",
                extra={'correlation_id': correlation_id, 'validation_result': validation_result}
            )
    
    def _show_validation_message(self, message: str, color: str):
        """Show validation message to user."""
        if hasattr(self, 'page') and self.page:
            self.page.show_snack_bar(
                ft.SnackBar(
                    content=ft.Text(message, color=color),
                    bgcolor=ft.Colors.WHITE
                )
            )
    
    def _on_field_changed(self, field_name: str, value: str):
        """Handle field value changes."""
        try:
            # Convert to appropriate type
            if field_name in ['capacity_mw', 'production_mwh_year1', 'capex_meur', 'opex_keuros_year1',
                             'ppa_price_eur_kwh', 'debt_ratio', 'interest_rate', 'discount_rate',
                             'tax_rate', 'degradation_rate', 'ppa_escalation', 'land_lease_eur_mw_year',
                             'grant_meur_masen', 'grant_meur_connection', 'grant_meur_cri']:
                numeric_value = float(value) if value else 0.0
                
                # Convert percentages to decimals
                if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
                    numeric_value = numeric_value / 100.0

                if self.on_data_changed:
                    self.on_data_changed(field_name, numeric_value)
            
            elif field_name in ['project_life_years', 'debt_years', 'tax_holiday', 'grace_years']:
                int_value = int(float(value)) if value else 0
                if self.on_data_changed:
                    self.on_data_changed(field_name, int_value)
            
            elif field_name == 'company_southern_italy':
                # Handle boolean field
                if self.on_data_changed:
                    self.on_data_changed(field_name, value)
            
            else:
                if self.on_data_changed:
                    self.on_data_changed(field_name, value)
        
        except ValueError:
            # Invalid number, ignore
            pass
    
    def update_data(self, project_assumptions: EnhancedProjectAssumptions):
        """Update form with new project assumptions data."""
        correlation_id = CorrelationIDManager.get_correlation_id()
        
        self.logger.info(
            "Updating form data with new project assumptions",
            extra={'correlation_id': correlation_id, 'action': 'form_data_update'}
        )
        
        # Log Piano Mattei/SIMEST grant field states before update
        old_grant_states = self._get_grant_field_states()
        self.logger.info(
            "Grant field states before update",
            extra={
                'correlation_id': correlation_id,
                'grant_states': old_grant_states,
                'action': 'grant_field_tracking'
            }
        )
        
        self.project_assumptions = project_assumptions

        # Track field changes for logging
        field_changes = {}
        
        # Update all fields
        for field_name, field in self.fields.items():
            old_value = field.value if hasattr(field, 'value') else None
            
            if field_name == 'company_southern_italy':
                # Handle boolean field
                value = getattr(project_assumptions, field_name, False)
                field.value = value
                new_value = value
            else:
                value = getattr(project_assumptions, field_name, 0)

                # Convert decimals to percentages for display
                if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
                    value = value * 100.0

                field.value = str(value)
                new_value = str(value)
            
            # Track changes
            if old_value != new_value:
                field_changes[field_name] = {'old': old_value, 'new': new_value}

        # Log Piano Mattei/SIMEST grant field states after update
        new_grant_states = self._get_grant_field_states()
        self.logger.info(
            "Grant field states after update",
            extra={
                'correlation_id': correlation_id,
                'grant_states': new_grant_states,
                'action': 'grant_field_tracking'
            }
        )
        
        # Log grant field changes specifically
        grant_changes = {}
        for field in ['_grant_meur_piano_mattei_unified', 'company_southern_italy']:
            if field in field_changes:
                grant_changes[field] = field_changes[field]
        
        if grant_changes:
            self.logger.info(
                "Piano Mattei/SIMEST grant fields changed during update",
                extra={
                    'correlation_id': correlation_id,
                    'grant_changes': grant_changes,
                    'action': 'grant_field_changes'
                }
            )

        # Update SIMEST displays
        self._update_simest_displays()

        # Trigger UI update for all fields
        for field in self.fields.values():
            field.update()
        
        # Log completion
        self.logger.info(
            f"Form data update completed. {len(field_changes)} fields changed.",
            extra={
                'correlation_id': correlation_id,
                'total_fields': len(self.fields),
                'changed_fields': len(field_changes),
                'field_changes': field_changes,
                'action': 'form_data_update_complete'
            }
        )
    
    def validate(self) -> Dict[str, str]:
        """Validate form data."""
        correlation_id = CorrelationIDManager.get_correlation_id()
        
        self.logger.info(
            "Starting form validation",
            extra={'correlation_id': correlation_id, 'action': 'form_validation_start'}
        )
        
        # Log Piano Mattei/SIMEST grant field states before validation
        grant_states = self._get_grant_field_states()
        self.logger.info(
            "Piano Mattei/SIMEST grant field states during validation",
            extra={
                'correlation_id': correlation_id,
                'grant_states': grant_states,
                'action': 'grant_field_validation_state'
            }
        )
        
        # Perform validation
        try:
            validation_errors = self.project_assumptions.validate_all()
            
            # Log validation results
            if validation_errors:
                self.logger.warning(
                    f"Form validation failed with {len(validation_errors)} errors",
                    extra={
                        'correlation_id': correlation_id,
                        'validation_errors': validation_errors,
                        'error_count': len(validation_errors),
                        'action': 'form_validation_failed'
                    }
                )
                
                # Log specific Piano Mattei/SIMEST validation errors
                simest_errors = {k: v for k, v in validation_errors.items() 
                               if 'piano_mattei' in k.lower() or 'simest' in k.lower() or 'southern_italy' in k.lower()}
                if simest_errors:
                    self.logger.error(
                        "Piano Mattei/SIMEST specific validation errors found",
                        extra={
                            'correlation_id': correlation_id,
                            'simest_errors': simest_errors,
                            'grant_states': grant_states,
                            'action': 'simest_validation_errors'
                        }
                    )
            else:
                self.logger.info(
                    "Form validation passed successfully",
                    extra={
                        'correlation_id': correlation_id,
                        'grant_states': grant_states,
                        'action': 'form_validation_passed'
                    }
                )
            
            return validation_errors
            
        except Exception as e:
            self.logger.error(
                f"Exception during form validation: {str(e)}",
                extra={
                    'correlation_id': correlation_id,
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'grant_states': grant_states,
                    'action': 'form_validation_exception'
                },
                exc_info=True
            )
            
            # Return error indicating validation failure
            return {'validation_error': f"Validation failed due to exception: {str(e)}"}
    
    def _get_grant_field_states(self) -> Dict[str, Any]:
        """Get current Piano Mattei/SIMEST grant field states for logging."""
        try:
            states = {
                'unified_grant_field': getattr(self.project_assumptions, '_grant_meur_piano_mattei_unified', 0.0),
                'southern_italy_flag': getattr(self.project_assumptions, 'company_southern_italy', False),
                'legacy_piano_mattei': getattr(self.project_assumptions, 'grant_meur_piano_mattei', None),
                'legacy_simest': getattr(self.project_assumptions, 'grant_meur_simest', None),
            }
            
            # Calculate derived values
            if states['unified_grant_field'] > 0:
                grant_percentage = 0.20 if states['southern_italy_flag'] else 0.10
                states['calculated_grant_amount'] = states['unified_grant_field'] * grant_percentage
                states['calculated_soft_loan_amount'] = states['unified_grant_field'] * (1.0 - grant_percentage)
                states['grant_percentage'] = grant_percentage
            else:
                states['calculated_grant_amount'] = 0.0
                states['calculated_soft_loan_amount'] = 0.0
                states['grant_percentage'] = 0.0
            
            return states
            
        except Exception as e:
            self.logger.error(f"Error getting grant field states: {str(e)}")
            return {'error': str(e)}
    
    def get_data(self) -> EnhancedProjectAssumptions:
        """Get current form data as EnhancedProjectAssumptions."""
        correlation_id = CorrelationIDManager.get_correlation_id()
        
        self.logger.debug(
            "Getting form data as EnhancedProjectAssumptions",
            extra={'correlation_id': correlation_id, 'action': 'get_form_data'}
        )
        
        return self.project_assumptions
