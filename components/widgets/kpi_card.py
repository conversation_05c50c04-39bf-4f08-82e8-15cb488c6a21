"""
KPI Card Widget
===============

Reusable KPI display card component.
"""

import flet as ft
from typing import Optional


class KPICard:
    """Reusable KPI display card."""
    
    def __init__(self, 
                 title: str,
                 value: str,
                 color: str = ft.Colors.BLUE,
                 icon: Optional[str] = None,
                 target_value: Optional[float] = None,
                 current_value: Optional[float] = None,
                 lower_is_better: bool = False):
        self.title = title
        self.value = value
        self.color = color
        self.icon = icon
        self.target_value = target_value
        self.current_value = current_value
        self.lower_is_better = lower_is_better
    
    def build(self) -> ft.Card:
        """Build the KPI card."""
        content_controls = []
        
        # Icon
        if self.icon:
            content_controls.append(
                ft.Icon(self.icon, color=self.color, size=30)
            )
        
        # Title
        content_controls.append(
            ft.Text(self.title, size=14, color=ft.Colors.GREY_700, 
                   text_align=ft.TextAlign.CENTER)
        )
        
        # Value
        content_controls.append(
            ft.Text(self.value, size=20, weight=ft.FontWeight.BOLD, 
                   color=self.color, text_align=ft.TextAlign.CENTER)
        )
        
        # Progress indicator if target is provided
        if self.target_value is not None and self.current_value is not None:
            progress_indicator = self._create_progress_indicator()
            content_controls.append(progress_indicator)
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    content_controls,
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=5
                ),
                padding=15,
                width=180,
                height=140
            ),
            elevation=2
        )
    
    def _create_progress_indicator(self) -> ft.Container:
        """Create progress indicator based on target."""
        if self.target_value == 0:
            return ft.Container()

        # Calculate progress
        if self.lower_is_better:
            # For metrics where lower is better (like LCOE)
            if self.current_value <= 0:
                # Handle negative or zero current values
                progress = 0.0
                status_color = ft.Colors.RED
            elif self.current_value <= self.target_value:
                progress = 1.0
                status_color = ft.Colors.GREEN
            else:
                progress = max(0.0, self.target_value / self.current_value)
                status_color = ft.Colors.RED if progress < 0.8 else ft.Colors.ORANGE
        else:
            # For metrics where higher is better (like IRR)
            if self.current_value <= 0:
                # Handle negative or zero current values
                progress = 0.0
                status_color = ft.Colors.RED
            else:
                progress = min(max(0.0, self.current_value / self.target_value), 1.0)
                if progress >= 1.0:
                    status_color = ft.Colors.GREEN
                elif progress >= 0.8:
                    status_color = ft.Colors.ORANGE
                else:
                    status_color = ft.Colors.RED
        
        return ft.Container(
            content=ft.Column([
                ft.ProgressBar(
                    value=progress,
                    color=status_color,
                    bgcolor=ft.Colors.GREY_300,
                    height=4
                ),
                ft.Text(
                    f"Target: {self.target_value:.1f}{'%' if self.title.endswith('IRR') else ''}",
                    size=10,
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                )
            ]),
            width=150
        )
