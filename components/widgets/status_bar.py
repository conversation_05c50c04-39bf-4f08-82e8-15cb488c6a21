"""
Status Bar Widget
=================

Application status bar component.
"""

import flet as ft
from typing import Optional
from datetime import datetime


class StatusBar:
    """Application status bar widget."""
    
    def __init__(self):
        self.status = "Ready"
        self.message = ""
        self.progress = 0.0
        self.show_progress = False
        self.last_update = datetime.now()
    
    def build(self) -> ft.Container:
        """Build the status bar."""
        controls = []
        
        # Progress bar (if shown)
        if self.show_progress:
            progress_bar = ft.ProgressBar(
                value=self.progress / 100.0,
                color=ft.Colors.BLUE,
                bgcolor=ft.Colors.GREY_300,
                height=3
            )
            controls.append(progress_bar)
        
        # Status row
        status_row = ft.Row([
            # Status icon and text
            ft.Row([
                self._get_status_icon(),
                ft.Text(self.status, size=12, weight=ft.FontWeight.BOLD),
                ft.Text(self.message, size=12, color=ft.Colors.GREY_600, expand=True)
            ]),
            
            # Right side info
            ft.Row([
                ft.Text(f"Last update: {self.last_update.strftime('%H:%M:%S')}", 
                       size=10, color=ft.Colors.GREY_500),
                ft.VerticalDivider(width=1),
                ft.Text("Agevolami SRL - Financial Modeling", 
                       size=10, color=ft.Colors.GREY_500)
            ])
        ])
        
        controls.append(status_row)
        
        return ft.Container(
            content=ft.Column(controls, spacing=2),
            padding=ft.padding.symmetric(horizontal=20, vertical=8),
            bgcolor=ft.Colors.GREY_100,
            border=ft.border.only(top=ft.border.BorderSide(1, ft.Colors.GREY_300))
        )
    
    def _get_status_icon(self) -> ft.Icon:
        """Get appropriate icon for current status."""
        icon_map = {
            "Ready": (ft.Icons.CHECK_CIRCLE, ft.Colors.GREEN),
            "Loading": (ft.Icons.HOURGLASS_EMPTY, ft.Colors.BLUE),
            "Error": (ft.Icons.ERROR, ft.Colors.RED),
            "Warning": (ft.Icons.WARNING, ft.Colors.ORANGE),
            "Success": (ft.Icons.CHECK_CIRCLE, ft.Colors.GREEN),
            "Processing": (ft.Icons.SYNC, ft.Colors.BLUE)
        }

        icon, color = icon_map.get(self.status, (ft.Icons.INFO, ft.Colors.GREY))
        return ft.Icon(icon, color=color, size=16)
    
    def set_status(self, status: str, message: str = ""):
        """Set status and message."""
        self.status = status
        self.message = message
        self.last_update = datetime.now()
    
    def set_progress(self, progress: float, message: str = ""):
        """Set progress and show progress bar."""
        self.progress = progress
        self.show_progress = True
        if message:
            self.message = message
        self.last_update = datetime.now()
    
    def hide_progress(self):
        """Hide progress bar."""
        self.show_progress = False
        self.progress = 0.0
    
    def set_ready(self, message: str = "Ready"):
        """Set status to ready."""
        self.set_status("Ready", message)
        self.hide_progress()
    
    def set_loading(self, message: str = "Loading..."):
        """Set status to loading."""
        self.set_status("Loading", message)
    
    def set_error(self, message: str):
        """Set status to error."""
        self.set_status("Error", message)
        self.hide_progress()
    
    def set_success(self, message: str):
        """Set status to success."""
        self.set_status("Success", message)
        self.hide_progress()
    
    def set_warning(self, message: str):
        """Set status to warning."""
        self.set_status("Warning", message)
