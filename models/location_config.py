"""
Location Configuration Data Models
==================================

Data models for location configurations and comparison functionality.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
import json
from pathlib import Path


@dataclass
class LocationConfig:
    """Configuration for a specific project location."""
    
    name: str
    production_mwh_year1: float
    capex_meur: float
    opex_keuros_year1: float
    ppa_price_eur_kwh: float
    land_lease_eur_mw_year: float
    description: str
    advantages: List[str] = field(default_factory=list)
    challenges: List[str] = field(default_factory=list)
    irradiation_kwh_m2: float = 2000.0
    
    # Additional technical parameters
    grid_connection_cost_meur: float = 0.0
    transmission_losses_percent: float = 2.0
    availability_factor: float = 0.98
    
    # Environmental and regulatory factors
    environmental_impact_score: float = 5.0  # 1-10 scale
    regulatory_complexity_score: float = 5.0  # 1-10 scale
    permitting_time_months: int = 12
    
    def __post_init__(self):
        """Validate data after initialization."""
        if self.production_mwh_year1 <= 0:
            raise ValueError("Production must be positive")
        if self.capex_meur <= 0:
            raise ValueError("CAPEX must be positive")
        if not 0 <= self.ppa_price_eur_kwh <= 1:
            raise ValueError("PPA price must be between 0 and 1 EUR/kWh")
    
    def calculate_capacity_factor(self, capacity_mw: float) -> float:
        """Calculate capacity factor based on production and capacity."""
        if capacity_mw <= 0:
            return 0.0
        
        max_annual_production = capacity_mw * 8760  # MWh
        return self.production_mwh_year1 / max_annual_production
    
    def get_lcoe_estimate(self, discount_rate: float = 0.08, project_life: int = 25) -> float:
        """Get rough LCOE estimate for comparison."""
        # Simplified LCOE calculation for comparison
        annual_opex = self.opex_keuros_year1 * 1000
        total_capex = self.capex_meur * 1e6
        
        # Present value of costs
        pv_opex = sum(annual_opex / (1 + discount_rate) ** year for year in range(1, project_life + 1))
        pv_costs = total_capex + pv_opex
        
        # Present value of energy
        annual_degradation = 0.005  # 0.5% per year
        pv_energy = 0
        for year in range(1, project_life + 1):
            energy_year = self.production_mwh_year1 * (1 - annual_degradation) ** (year - 1) * 1000  # kWh
            pv_energy += energy_year / (1 + discount_rate) ** year
        
        return pv_costs / pv_energy if pv_energy > 0 else float('inf')
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'name': self.name,
            'production_mwh_year1': self.production_mwh_year1,
            'capex_meur': self.capex_meur,
            'opex_keuros_year1': self.opex_keuros_year1,
            'ppa_price_eur_kwh': self.ppa_price_eur_kwh,
            'land_lease_eur_mw_year': self.land_lease_eur_mw_year,
            'description': self.description,
            'advantages': self.advantages,
            'challenges': self.challenges,
            'irradiation_kwh_m2': self.irradiation_kwh_m2,
            'grid_connection_cost_meur': self.grid_connection_cost_meur,
            'transmission_losses_percent': self.transmission_losses_percent,
            'availability_factor': self.availability_factor,
            'environmental_impact_score': self.environmental_impact_score,
            'regulatory_complexity_score': self.regulatory_complexity_score,
            'permitting_time_months': self.permitting_time_months
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LocationConfig':
        """Create instance from dictionary."""
        return cls(**{k: v for k, v in data.items() if k in cls.__dataclass_fields__})


class LocationManager:
    """Manager for location configurations and comparisons."""
    
    def __init__(self):
        self.locations: Dict[str, LocationConfig] = {}
        self._load_default_locations()
    
    def _load_default_locations(self):
        """Load default location configurations - Updated for 2025 market conditions."""
        default_locations = {
            # Morocco Locations - Updated 2025 data
            "Ouarzazate": LocationConfig(
                name="Ouarzazate",
                production_mwh_year1=18500,  # Improved with new technology
                capex_meur=7.8,  # Reduced costs due to technology improvements
                opex_keuros_year1=165,  # Lower O&M costs
                ppa_price_eur_kwh=0.042,  # Competitive 2025 pricing
                land_lease_eur_mw_year=1800,  # Optimized land costs
                description="Established solar hub with world-class infrastructure (2025 optimized)",
                advantages=["Proven track record", "Excellent grid connection", "Skilled workforce", "Government support"],
                challenges=["Land competition", "Water scarcity"],
                irradiation_kwh_m2=2250,  # Updated measurements
                grid_connection_cost_meur=0.3,
                transmission_losses_percent=3.5,
                availability_factor=0.98,
                environmental_impact_score=7.5,
                regulatory_complexity_score=6.0,
                permitting_time_months=18
            ),
            "Dakhla": LocationConfig(
                name="Dakhla",
                production_mwh_year1=19200,  # Excellent resource
                capex_meur=8.2,  # Improved logistics
                opex_keuros_year1=170,
                ppa_price_eur_kwh=0.039,  # Very competitive
                land_lease_eur_mw_year=2000,
                description="Premium solar resource with strategic Atlantic location (2025)",
                advantages=["Highest solar irradiation", "Atlantic wind synergy", "Export potential to Europe", "Green hydrogen hub potential"],
                challenges=["Remote location", "Infrastructure development needed", "Logistics costs"],
                irradiation_kwh_m2=2420,
                grid_connection_cost_meur=1.2,
                transmission_losses_percent=5.5,
                availability_factor=0.97,
                environmental_impact_score=8.0,
                regulatory_complexity_score=7.0,
                permitting_time_months=24
            ),

            # Italy Locations - 2025 market data
            "Sicily_Catania": LocationConfig(
                name="Sicily_Catania",
                production_mwh_year1=16800,
                capex_meur=9.2,  # Higher EU costs but better financing
                opex_keuros_year1=195,
                ppa_price_eur_kwh=0.065,  # EU market pricing
                land_lease_eur_mw_year=3500,
                description="Strategic Mediterranean location with EU market access",
                advantages=["EU market access", "Stable regulatory framework", "Grid stability", "Skilled workforce"],
                challenges=["Higher costs", "Complex permitting", "Land availability"],
                irradiation_kwh_m2=1950,
                grid_connection_cost_meur=0.8,
                transmission_losses_percent=2.5,
                availability_factor=0.99,
                environmental_impact_score=6.5,
                regulatory_complexity_score=8.5,
                permitting_time_months=36
            ),

            "Puglia_Brindisi": LocationConfig(
                name="Puglia_Brindisi",
                production_mwh_year1=16200,
                capex_meur=9.0,
                opex_keuros_year1=190,
                ppa_price_eur_kwh=0.068,
                land_lease_eur_mw_year=3200,
                description="Southern Italy with excellent grid connection and industrial synergies",
                advantages=["Industrial infrastructure", "Port access", "EU incentives", "Grid connection"],
                challenges=["Bureaucracy", "Environmental assessments", "Competition"],
                irradiation_kwh_m2=1880,
                grid_connection_cost_meur=0.5,
                transmission_losses_percent=2.0,
                availability_factor=0.99,
                environmental_impact_score=6.0,
                regulatory_complexity_score=9.0,
                permitting_time_months=42
            ),
            # Spain Locations - 2025 competitive market
            "Andalusia_Sevilla": LocationConfig(
                name="Andalusia_Sevilla",
                production_mwh_year1=17500,
                capex_meur=8.8,
                opex_keuros_year1=185,
                ppa_price_eur_kwh=0.055,
                land_lease_eur_mw_year=2800,
                description="Mature Spanish solar market with excellent infrastructure",
                advantages=["Mature market", "Excellent grid", "EU access", "Competitive auctions"],
                challenges=["Market saturation", "Grid congestion", "Cannibalization effects"],
                irradiation_kwh_m2=2050,
                grid_connection_cost_meur=0.4,
                transmission_losses_percent=2.2,
                availability_factor=0.98,
                environmental_impact_score=7.0,
                regulatory_complexity_score=7.5,
                permitting_time_months=30
            ),

            # Additional Morocco locations
            "Noor_Midelt": LocationConfig(
                name="Noor_Midelt",
                production_mwh_year1=17800,  # Updated with hybrid potential
                capex_meur=8.1,  # Optimized costs
                opex_keuros_year1=175,
                ppa_price_eur_kwh=0.044,
                land_lease_eur_mw_year=1900,
                description="Hybrid solar-storage hub with CSP synergies (2025 optimized)",
                advantages=["Hybrid potential", "Storage integration", "Government flagship", "Grid stability"],
                challenges=["Altitude effects", "Temperature variations", "Complex technology"],
                irradiation_kwh_m2=2180,
                grid_connection_cost_meur=0.6,
                transmission_losses_percent=4.0,
                availability_factor=0.96,
                environmental_impact_score=7.8,
                regulatory_complexity_score=6.5,
                permitting_time_months=20
            ),

            "Laayoune_Advanced": LocationConfig(
                name="Laayoune_Advanced",
                production_mwh_year1=18900,  # Premium location
                capex_meur=7.9,  # Economies of scale
                opex_keuros_year1=160,
                ppa_price_eur_kwh=0.038,  # Lowest cost
                land_lease_eur_mw_year=1700,
                description="Next-generation solar hub with export infrastructure (2025)",
                advantages=["Highest efficiency", "Export infrastructure", "Green hydrogen ready", "Strategic location"],
                challenges=["Remote location", "Infrastructure investment", "Political considerations"],
                irradiation_kwh_m2=2380,
                grid_connection_cost_meur=1.5,
                transmission_losses_percent=6.0,
                availability_factor=0.97,
                environmental_impact_score=8.2,
                regulatory_complexity_score=7.5,
                permitting_time_months=28
            ),

            # Alternative spelling for compatibility
            "Laâyoune": LocationConfig(
                name="Laâyoune",
                production_mwh_year1=18200,
                capex_meur=8.4,
                opex_keuros_year1=180,
                ppa_price_eur_kwh=0.043,
                land_lease_eur_mw_year=2000,
                description="Excellent southern location with high irradiation",
                advantages=["High solar irradiation", "Strategic location", "Government priority"],
                challenges=["Remote location", "Limited infrastructure", "Security considerations"],
                irradiation_kwh_m2=2320,
                grid_connection_cost_meur=1.2,
                transmission_losses_percent=5.5,
                availability_factor=0.97,
                environmental_impact_score=8.0,
                regulatory_complexity_score=7.0,
                permitting_time_months=24
            )
        }
        
        for name, config in default_locations.items():
            self.locations[name] = config
    
    def get_location(self, name: str) -> Optional[LocationConfig]:
        """Get location configuration by name."""
        return self.locations.get(name)
    
    def get_all_locations(self) -> Dict[str, LocationConfig]:
        """Get all location configurations."""
        return self.locations.copy()
    
    def get_location_names(self) -> List[str]:
        """Get list of all location names."""
        return list(self.locations.keys())
    
    def add_location(self, location: LocationConfig):
        """Add a new location configuration."""
        self.locations[location.name] = location
    
    def remove_location(self, name: str) -> bool:
        """Remove a location configuration."""
        if name in self.locations:
            del self.locations[name]
            return True
        return False
    
    def compare_locations(self, location_names: List[str]) -> Dict[str, LocationConfig]:
        """Get configurations for comparison."""
        return {name: self.locations[name] for name in location_names if name in self.locations}
    
    def save_to_file(self, filepath: Path):
        """Save location configurations to file."""
        data = {name: config.to_dict() for name, config in self.locations.items()}
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_from_file(self, filepath: Path):
        """Load location configurations from file."""
        if filepath.exists():
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            self.locations = {}
            for name, config_data in data.items():
                self.locations[name] = LocationConfig.from_dict(config_data)
