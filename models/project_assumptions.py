"""
Enhanced Project Assumptions Data Model
=======================================

Enhanced data model for project assumptions with validation and calculations.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
import logging

# Import correlation logging utilities
try:
    from utils.logging_utils import (
        CorrelationLoggerAdapter, 
        LoggerFactory, 
        CorrelationIDManager
    )
    CORRELATION_LOGGING_AVAILABLE = True
except ImportError:
    # Fallback if logging utils not available
    CORRELATION_LOGGING_AVAILABLE = False
    CorrelationLoggerAdapter = None


@dataclass
class EnhancedProjectAssumptions:
    """Enhanced project assumptions with additional validation and methods."""
    
    # Technical parameters
    technology_type: str = "Solar PV"
    capacity_mw: float = 10.0
    production_mwh_year1: float = 18000.0
    degradation_rate: float = 0.005
    project_life_years: int = 25
    
    # Financial parameters
    capex_meur: float = 8.5
    opex_keuros_year1: float = 180.0
    ppa_price_eur_kwh: float = 0.045
    ppa_escalation: float = 0.0
    debt_ratio: float = 0.75
    interest_rate: float = 0.06
    debt_years: int = 15
    discount_rate: float = 0.08
    tax_rate: float = 0.15
    land_lease_eur_mw_year: float = 2000.0
    
    # Tax and financing parameters
    tax_holiday: int = 5
    grace_years: int = 2
    
    # Grant parameters
    # Internal canonical field for unified Piano Mattei/SIMEST grants
    _grant_meur_piano_mattei_unified: float = 0.0
    grant_meur_masen: float = 0.0  # MASEN commercial project incentives (primary for commercial projects)
    grant_meur_iresen: float = 0.0  # IRESEN R&D incentives (typically 0 for commercial projects)
    grant_meur_connection: float = 0.0  # Grid connection support grants
    grant_meur_cri: float = 0.0  # Regional investment center grants
    
    # Location parameters
    project_location: str = "Ouarzazate"
    comparable_location: Optional[str] = None  # For location comparison analysis
    
    # SIMEST program parameters
    company_location: str = "Italy"  # Company location to determine SIMEST grant percentage
    company_southern_italy: bool = False  # True for 20% grant, False for 10% grant

    # UI-specific fields
    is_validated: bool = False
    validation_errors: Dict[str, str] = field(default_factory=dict)
    last_modified: Optional[str] = None
    
    def __post_init__(self):
        """Enhanced post-initialization with validation."""
        # Initialize correlation-aware logger
        if CORRELATION_LOGGING_AVAILABLE:
            self.logger = LoggerFactory.get_module_logger(__name__)
        else:
            self.logger = logging.getLogger(__name__)
        
        # Get correlation ID for tracking
        correlation_id = None
        if CORRELATION_LOGGING_AVAILABLE:
            correlation_id = CorrelationIDManager.get_correlation_id()
        
        # Log object creation with Piano Mattei/SIMEST grant field states
        grant_field_states = {
            'unified_grant_meur': self._grant_meur_piano_mattei_unified,
            'masen_grant_meur': self.grant_meur_masen,
            'iresen_grant_meur': self.grant_meur_iresen,
            'connection_grant_meur': self.grant_meur_connection,
            'cri_grant_meur': self.grant_meur_cri,
            'company_location': self.company_location,
            'company_southern_italy': self.company_southern_italy,
            'project_location': self.project_location
        }
        
        if hasattr(self.logger, 'info_context'):
            self.logger.info_context(
                "EnhancedProjectAssumptions object created",
                correlation_id=correlation_id,
                grant_field_states=grant_field_states,
                capacity_mw=self.capacity_mw,
                capex_meur=self.capex_meur,
                action="object_creation"
            )
        else:
            self.logger.info(f"EnhancedProjectAssumptions created - Correlation ID: {correlation_id}, "
                           f"Unified Grant: {self._grant_meur_piano_mattei_unified} MEUR, "
                           f"CAPEX: {self.capex_meur} MEUR")
        
        # Ensure calculated fields are available for validation
        if not hasattr(self, 'investment_for_debt_sizing_meur'):
            self.investment_for_debt_sizing_meur = self.capex_meur - self.calculate_total_grants()

        # Log pre-validation state
        if hasattr(self.logger, 'debug_context'):
            self.logger.debug_context(
                "Starting validation process",
                correlation_id=correlation_id,
                pre_validation_state={
                    'total_grants_calculated': self.calculate_total_grants(),
                    'simest_grant_component': self.calculate_simest_grant_component(),
                    'simest_soft_loan_component': self.calculate_simest_soft_loan_component()
                },
                action="pre_validation"
            )

        self.validate_all()
    
    def validate_all(self) -> Dict[str, str]:
        """Comprehensive validation of all parameters."""
        # Get correlation ID for tracking
        correlation_id = None
        if CORRELATION_LOGGING_AVAILABLE:
            correlation_id = CorrelationIDManager.get_correlation_id()
        
        if hasattr(self.logger, 'info_context'):
            self.logger.info_context(
                "Starting comprehensive validation",
                correlation_id=correlation_id,
                action="validation_start"
            )
        else:
            self.logger.info(f"Starting validation - Correlation ID: {correlation_id}")
        
        errors = {}
        
        # Basic parameter validation
        if self.capacity_mw <= 0:
            errors['capacity_mw'] = "Capacity must be positive"
            if hasattr(self.logger, 'warning_context'):
                self.logger.warning_context(
                    "Capacity validation failed: must be positive",
                    correlation_id=correlation_id,
                    capacity_mw=self.capacity_mw,
                    validation_step="capacity_positive",
                    action="validation_error"
                )
        elif self.capacity_mw > 1000:
            errors['capacity_mw'] = "Capacity seems unreasonably high (>1000 MW)"
            if hasattr(self.logger, 'warning_context'):
                self.logger.warning_context(
                    "Capacity validation warning: unreasonably high",
                    correlation_id=correlation_id,
                    capacity_mw=self.capacity_mw,
                    validation_step="capacity_reasonable",
                    action="validation_warning"
                )
        
        if self.project_life_years < 10 or self.project_life_years > 50:
            errors['project_life_years'] = "Project life should be between 10-50 years"
        
        if self.capex_meur <= 0:
            errors['capex_meur'] = "CAPEX must be positive"
        elif self.capex_meur / self.capacity_mw > 2.0:
            errors['capex_meur'] = "CAPEX per MW seems high (>2.0 M EUR/MW)"
        
        # Financial validation
        if not 0 < self.debt_ratio < 1:
            errors['debt_ratio'] = "Debt ratio must be between 0 and 1"
        
        if self.interest_rate < 0 or self.interest_rate > 0.2:
            errors['interest_rate'] = "Interest rate should be between 0-20%"
        
        if self.discount_rate < 0 or self.discount_rate > 0.3:
            errors['discount_rate'] = "Discount rate should be between 0-30%"
        
        if self.debt_years > self.project_life_years:
            errors['debt_years'] = "Debt tenor cannot exceed project life"
        
        # Tax and grace period validation
        if self.tax_rate < 0 or self.tax_rate > 0.5:
            errors['tax_rate'] = "Tax rate should be between 0-50%"

        if self.tax_holiday < 0 or self.tax_holiday > self.project_life_years:
            errors['tax_holiday'] = "Tax holiday period must be between 0 and project life"

        if self.grace_years < 0 or self.grace_years > self.debt_years:
            errors['grace_years'] = "Debt grace period cannot exceed debt tenor"
        
        # Revenue validation
        if self.production_mwh_year1 <= 0:
            errors['production_mwh_year1'] = "Production must be positive"
        
        capacity_factor = self.production_mwh_year1 / (self.capacity_mw * 8760)
        if capacity_factor > 0.6:
            errors['production_mwh_year1'] = "Capacity factor seems high (>60%)"
        elif capacity_factor < 0.1:
            errors['production_mwh_year1'] = "Capacity factor seems low (<10%)"
        
        if self.ppa_price_eur_kwh <= 0 or self.ppa_price_eur_kwh > 0.5:
            errors['ppa_price_eur_kwh'] = "PPA price should be between 0-0.5 EUR/kWh"
        
        # Grant validation with detailed logging
        total_grants = self.calculate_total_grants()
        simest_grant_component = self.calculate_simest_grant_component()
        simest_soft_loan_component = self.calculate_simest_soft_loan_component()
        
        # Log grant calculation results
        if hasattr(self.logger, 'debug_context'):
            self.logger.debug_context(
                "Grant calculations completed",
                correlation_id=correlation_id,
                grant_calculations={
                    'total_grants': total_grants,
                    'simest_grant_component': simest_grant_component,
                    'simest_soft_loan_component': simest_soft_loan_component,
                    'unified_grant_field': self._grant_meur_piano_mattei_unified,
                    'masen_grant': self.grant_meur_masen,
                    'iresen_grant': self.grant_meur_iresen,
                    'connection_grant': self.grant_meur_connection,
                    'cri_grant': self.grant_meur_cri
                },
                validation_step="grant_calculations",
                action="validation_calculation"
            )
        
        if total_grants > self.capex_meur:
            errors['grants'] = "Total grants cannot exceed CAPEX"
            if hasattr(self.logger, 'error_context'):
                self.logger.error_context(
                    "Grant validation failed: total grants exceed CAPEX",
                    correlation_id=correlation_id,
                    total_grants=total_grants,
                    capex_meur=self.capex_meur,
                    grant_breakdown={
                        'simest_grant': simest_grant_component,
                        'masen_grant': self.grant_meur_masen,
                        'iresen_grant': self.grant_meur_iresen,
                        'connection_grant': self.grant_meur_connection,
                        'cri_grant': self.grant_meur_cri
                    },
                    validation_step="total_grants_vs_capex",
                    action="validation_error"
                )
        
        # CRI grant specific validation
        if self.grant_meur_cri > 0:
            # CRI grant cannot exceed 30% of CAPEX
            max_cri_grant = 0.3 * self.capex_meur
            if self.grant_meur_cri > max_cri_grant:
                errors['grant_meur_cri'] = f"CRI grant cannot exceed 30% of CAPEX (max: {max_cri_grant:.2f} M EUR)"
            
            # CRI grant is primarily for Morocco - warn if used elsewhere
            morocco_locations = ['morocco', 'maroc', 'ouarzazate', 'dakhla', 'laâyoune', 'laayoune', 'noor_midelt', 'tarfaya']
            if self.project_location.lower() not in morocco_locations:
                errors['grant_meur_cri_location'] = "CRI grant is designed for Morocco-based projects. Using CRI grant for non-Morocco projects may not be compliant with grant terms."
        
        # IRESEN grant validation (typically for R&D, not commercial projects)
        if self.grant_meur_iresen > 0:
            # Warning for commercial projects using IRESEN
            if self.grant_meur_iresen > 0.5:  # Significant IRESEN funding
                errors['grant_meur_iresen_commercial'] = "IRESEN grants are primarily for R&D and non-commercial projects. Consider using MASEN grants for commercial projects instead."
            
            # IRESEN should be much smaller than MASEN for commercial projects
            if self.grant_meur_iresen > self.grant_meur_masen and self.grant_meur_masen > 0:
                errors['grant_meur_iresen_vs_masen'] = "For commercial projects, MASEN grants should typically be larger than IRESEN grants. IRESEN is primarily for R&D."
        
        # SIMEST program validation with detailed logging
        if self._grant_meur_piano_mattei_unified > 0:
            if hasattr(self.logger, 'info_context'):
                self.logger.info_context(
                    "Starting Piano Mattei/SIMEST validation",
                    correlation_id=correlation_id,
                    unified_grant_amount=self._grant_meur_piano_mattei_unified,
                    company_location=self.company_location,
                    company_southern_italy=self.company_southern_italy,
                    project_location=self.project_location,
                    validation_step="simest_validation_start",
                    action="validation_step"
                )
            
            # Validate SIMEST eligibility using configuration
            try:
                eligibility = self._validate_simest_eligibility()
                if not eligibility['is_eligible']:
                    errors['simest_eligibility'] = "; ".join(eligibility['errors'])
                    if hasattr(self.logger, 'error_context'):
                        self.logger.error_context(
                            "SIMEST eligibility validation failed",
                            correlation_id=correlation_id,
                            eligibility_errors=eligibility['errors'],
                            company_location=self.company_location,
                            project_location=self.project_location,
                            validation_step="simest_eligibility",
                            action="validation_error"
                        )
                else:
                    if hasattr(self.logger, 'info_context'):
                        self.logger.info_context(
                            "SIMEST eligibility validation passed",
                            correlation_id=correlation_id,
                            validation_step="simest_eligibility",
                            action="validation_success"
                        )
                
                # Validate SIMEST facility amounts
                facility_validation = self._validate_simest_facility_amounts()
                if facility_validation:
                    errors.update(facility_validation)
                    if hasattr(self.logger, 'error_context'):
                        self.logger.error_context(
                            "SIMEST facility amount validation failed",
                            correlation_id=correlation_id,
                            facility_errors=facility_validation,
                            facility_amount=self._grant_meur_piano_mattei_unified,
                            validation_step="simest_facility_amounts",
                            action="validation_error"
                        )
                else:
                    if hasattr(self.logger, 'debug_context'):
                        self.logger.debug_context(
                            "SIMEST facility amount validation passed",
                            correlation_id=correlation_id,
                            facility_amount=self._grant_meur_piano_mattei_unified,
                            validation_step="simest_facility_amounts",
                            action="validation_success"
                        )
                
                # Validate grant percentage limits
                grant_validation = self._validate_simest_grant_limits()
                if grant_validation:
                    errors.update(grant_validation)
                    if hasattr(self.logger, 'error_context'):
                        self.logger.error_context(
                            "SIMEST grant limit validation failed",
                            correlation_id=correlation_id,
                            grant_limit_errors=grant_validation,
                            grant_component=simest_grant_component,
                            capex_meur=self.capex_meur,
                            validation_step="simest_grant_limits",
                            action="validation_error"
                        )
                else:
                    if hasattr(self.logger, 'debug_context'):
                        self.logger.debug_context(
                            "SIMEST grant limit validation passed",
                            correlation_id=correlation_id,
                            grant_component=simest_grant_component,
                            validation_step="simest_grant_limits",
                            action="validation_success"
                        )
                    
            except Exception as e:
                errors['simest_config'] = f"SIMEST configuration error: {str(e)}"
                if hasattr(self.logger, 'error_context'):
                    self.logger.error_context(
                        "SIMEST configuration error during validation",
                        correlation_id=correlation_id,
                        error_message=str(e),
                        error_type=type(e).__name__,
                        validation_step="simest_config",
                        action="validation_error"
                    )
        
        self.validation_errors = errors
        self.is_validated = len(errors) == 0
        
        # Log validation completion
        if hasattr(self.logger, 'info_context'):
            self.logger.info_context(
                "Validation completed",
                correlation_id=correlation_id,
                is_validated=self.is_validated,
                error_count=len(errors),
                validation_errors=list(errors.keys()) if errors else [],
                piano_mattei_simest_state={
                    'unified_grant_amount': self._grant_meur_piano_mattei_unified,
                    'grant_component': simest_grant_component,
                    'soft_loan_component': simest_soft_loan_component,
                    'total_grants': total_grants,
                    'grant_percentage': self.calculate_grant_percentage()
                },
                action="validation_complete"
            )
        else:
            self.logger.info(f"Validation completed - Correlation ID: {correlation_id}, "
                           f"Valid: {self.is_validated}, Errors: {len(errors)}")
        
        return errors
    
    def calculate_total_grants(self) -> float:
        """Calculate total grants from all sources.

        Includes grants from:
        - SIMEST Grant Component (only the non-repayable grant portion, not the full facility)
        - MASEN (grant_meur_masen) - Primary Moroccan incentives for commercial projects
        - IRESEN (grant_meur_iresen) - R&D incentives (typically 0 for commercial projects)
        - Connection (grant_meur_connection) - Grid connection support
        - CRI (grant_meur_cri) - Regional investment support

        Returns:
            float: Total grants in MEUR
        """
        # Use only the grant component of SIMEST, not the full facility
        simest_grant_component = self.calculate_simest_grant_component()
        
        return (simest_grant_component +
                self.grant_meur_masen +
                self.grant_meur_iresen +
                self.grant_meur_connection +
                self.grant_meur_cri)
    
    def calculate_grant_percentage(self) -> float:
        """Calculate grant percentage of CAPEX."""
        if self.capex_meur <= 0:
            return 0.0
        return (self.calculate_total_grants() / self.capex_meur) * 100
    
    def calculate_capacity_factor(self) -> float:
        """Calculate capacity factor."""
        if self.capacity_mw <= 0:
            return 0.0
        return self.production_mwh_year1 / (self.capacity_mw * 8760)
    
    def calculate_specific_capex(self) -> float:
        """Calculate specific CAPEX (EUR/kW)."""
        if self.capacity_mw <= 0:
            return 0.0
        return (self.capex_meur * 1e6) / (self.capacity_mw * 1000)
    
    def calculate_specific_opex(self) -> float:
        """Calculate specific OPEX (EUR/kW/year)."""
        if self.capacity_mw <= 0:
            return 0.0
        return (self.opex_keuros_year1 * 1000) / (self.capacity_mw * 1000)

    @property
    def equity_percentage(self) -> float:
        """Calculate equity percentage from debt ratio."""
        return 1.0 - self.debt_ratio

    @property
    def equity_meur(self) -> float:
        """Calculate equity amount in MEUR."""
        return self.capex_meur * self.equity_percentage

    @property
    def debt_meur(self) -> float:
        """Calculate debt amount in MEUR."""
        return self.capex_meur * self.debt_ratio

    @property
    def investment_for_debt_sizing_meur(self) -> float:
        """Calculate investment amount for debt sizing, accounting for SIMEST soft loan."""
        # CAPEX minus grants, but SIMEST soft loan is additional debt financing
        base_investment = self.capex_meur - self.calculate_total_grants()
        simest_soft_loan = self.calculate_simest_soft_loan_component()
        return base_investment + simest_soft_loan

    @property
    def simest_total_facility_meur(self) -> float:
        """Total SIMEST facility amount."""
        return self._grant_meur_piano_mattei_unified

    @property
    def simest_grant_meur(self) -> float:
        """SIMEST non-repayable grant component."""
        return self.calculate_simest_grant_component()

    @property
    def simest_soft_loan_meur(self) -> float:
        """SIMEST soft loan component."""
        return self.calculate_simest_soft_loan_component()

    @property
    def opex_meur_per_year(self) -> float:
        """Convert OPEX to MEUR per year."""
        return self.opex_keuros_year1 / 1000.0
    
    def get_financial_summary(self) -> Dict[str, Any]:
        """Get financial summary for display."""
        return {
            'capacity_mw': self.capacity_mw,
            'capex_meur': self.capex_meur,
            'specific_capex_eur_kw': self.calculate_specific_capex(),
            'opex_keuros_year1': self.opex_keuros_year1,
            'specific_opex_eur_kw_year': self.calculate_specific_opex(),
            'production_mwh_year1': self.production_mwh_year1,
            'capacity_factor': self.calculate_capacity_factor(),
            'ppa_price_eur_kwh': self.ppa_price_eur_kwh,
            'total_grants_meur': self.calculate_total_grants(),
            'grant_percentage': self.calculate_grant_percentage(),
            'debt_ratio': self.debt_ratio,
            'interest_rate': self.interest_rate,
            'discount_rate': self.discount_rate
        }
    
    def get_validation_status(self) -> Dict[str, Any]:
        """Get validation status for UI."""
        return {
            'is_valid': self.is_validated,
            'error_count': len(self.validation_errors),
            'errors': self.validation_errors,
            'warnings': self._get_warnings()
        }
    
    def _get_warnings(self) -> Dict[str, str]:
        """Get warnings for parameters that are valid but potentially concerning."""
        warnings = {}
        
        # Check for high debt ratio
        if self.debt_ratio > 0.8:
            warnings['debt_ratio'] = "High debt ratio may increase financial risk"
        
        # Check for low capacity factor
        capacity_factor = self.calculate_capacity_factor()
        if capacity_factor < 0.2:
            warnings['capacity_factor'] = "Low capacity factor may impact economics"
        
        # Check for high specific CAPEX
        specific_capex = self.calculate_specific_capex()
        if specific_capex > 1500:  # EUR/kW
            warnings['capex'] = "High specific CAPEX compared to industry benchmarks"
        
        # Check for low PPA price
        if self.ppa_price_eur_kwh < 0.03:
            warnings['ppa_price'] = "Low PPA price may impact project viability"
        
        return warnings
    
    def copy_with_modifications(self, **kwargs) -> 'EnhancedProjectAssumptions':
        """Create a copy with modifications for scenario analysis."""
        current_dict = self.to_dict()
        current_dict.update(kwargs)
        return EnhancedProjectAssumptions.from_dict(current_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        # Get all dataclass fields
        import dataclasses
        base_dict = dataclasses.asdict(self)

        # Add legacy fields for backward compatibility
        # Both map to the unified field but are included for compatibility
        base_dict['grant_meur_italy'] = self._grant_meur_piano_mattei_unified
        base_dict['grant_meur_simest_africa'] = 0.0  # Always 0 to prevent double-counting

        # Add SIMEST computed fields for dashboard and analysis
        base_dict['simest_total_facility_meur'] = self.simest_total_facility_meur
        base_dict['simest_grant_meur'] = self.calculate_simest_grant_component()
        base_dict['simest_soft_loan_meur'] = self.calculate_simest_soft_loan_component()

        # Add other computed fields that are needed for analysis
        base_dict['total_grants_meur'] = self.calculate_total_grants()

        # Remove calculated fields that shouldn't be serialized for reconstruction
        calculated_fields = {'total_grant_meur_maroc', 'investment_for_debt_sizing_meur'}
        for field in calculated_fields:
            base_dict.pop(field, None)

        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnhancedProjectAssumptions':
        """Create instance from dictionary."""
        # Initialize logger for migration tracking
        if CORRELATION_LOGGING_AVAILABLE:
            logger = LoggerFactory.get_module_logger(__name__)
            correlation_id = CorrelationIDManager.get_correlation_id()
        else:
            logger = logging.getLogger(__name__)
            correlation_id = None
        
        # Handle case where data is already an EnhancedProjectAssumptions object
        if isinstance(data, cls):
            if hasattr(logger, 'debug_context'):
                logger.debug_context(
                    "from_dict called with existing EnhancedProjectAssumptions object",
                    correlation_id=correlation_id,
                    action="from_dict_passthrough"
                )
            return data

        # Handle case where data is not a dictionary
        if not isinstance(data, dict):
            error_msg = f"Expected dict, got {type(data)}"
            if hasattr(logger, 'error_context'):
                logger.error_context(
                    "from_dict called with invalid data type",
                    correlation_id=correlation_id,
                    expected_type="dict",
                    actual_type=type(data).__name__,
                    action="from_dict_error"
                )
            raise TypeError(error_msg)

        # Create a copy to avoid modifying the original data
        data_copy = data.copy()

        # Log pre-migration state
        grant_italy = data_copy.get('grant_meur_italy', 0.0)
        grant_simest = data_copy.get('grant_meur_simest_africa', 0.0)
        existing_unified = data_copy.get('_grant_meur_piano_mattei_unified', 0.0)
        
        if hasattr(logger, 'info_context'):
            logger.info_context(
                "Starting Piano Mattei/SIMEST grant field migration",
                correlation_id=correlation_id,
                pre_migration_state={
                    'grant_meur_italy': grant_italy,
                    'grant_meur_simest_africa': grant_simest,
                    'existing_unified_field': existing_unified
                },
                action="grant_migration_start"
            )

        # Handle Piano Mattei/SIMEST migration - consolidate legacy fields
        migration_performed = False
        
        # If both legacy fields are populated, sum them and log the migration
        if grant_italy > 0 and grant_simest > 0:
            unified_value = grant_italy + grant_simest
            data_copy['_grant_meur_piano_mattei_unified'] = unified_value
            migration_performed = True
            
            if hasattr(logger, 'warning_context'):
                logger.warning_context(
                    "Piano Mattei/SIMEST migration: Consolidating both legacy fields",
                    correlation_id=correlation_id,
                    grant_italy=grant_italy,
                    grant_simest=grant_simest,
                    unified_value=unified_value,
                    migration_type="consolidation",
                    action="grant_migration"
                )
            else:
                logger.warning(f"Migration: Consolidating Piano Mattei ({grant_italy}) + SIMEST ({grant_simest}) = {unified_value} MEUR")
                
        elif grant_italy > 0:
            data_copy['_grant_meur_piano_mattei_unified'] = grant_italy
            migration_performed = True
            
            if hasattr(logger, 'info_context'):
                logger.info_context(
                    "Piano Mattei/SIMEST migration: Using Piano Mattei field",
                    correlation_id=correlation_id,
                    grant_italy=grant_italy,
                    migration_type="piano_mattei_only",
                    action="grant_migration"
                )
            else:
                logger.info(f"Migration: Using Piano Mattei grant ({grant_italy} MEUR)")
                
        elif grant_simest > 0:
            data_copy['_grant_meur_piano_mattei_unified'] = grant_simest
            migration_performed = True
            
            if hasattr(logger, 'info_context'):
                logger.info_context(
                    "Piano Mattei/SIMEST migration: Using SIMEST field",
                    correlation_id=correlation_id,
                    grant_simest=grant_simest,
                    migration_type="simest_only",
                    action="grant_migration"
                )
            else:
                logger.info(f"Migration: Using SIMEST grant ({grant_simest} MEUR)")
                
        else:
            data_copy['_grant_meur_piano_mattei_unified'] = 0.0
            if hasattr(logger, 'debug_context'):
                logger.debug_context(
                    "Piano Mattei/SIMEST migration: No legacy grants found",
                    correlation_id=correlation_id,
                    migration_type="no_legacy_grants",
                    action="grant_migration"
                )

        # Remove legacy fields from data_copy to prevent conflicts
        data_copy.pop('grant_meur_italy', None)
        data_copy.pop('grant_meur_simest_africa', None)
        
        # Log post-migration state
        final_unified_value = data_copy.get('_grant_meur_piano_mattei_unified', 0.0)
        if hasattr(logger, 'info_context'):
            logger.info_context(
                "Piano Mattei/SIMEST grant field migration completed",
                correlation_id=correlation_id,
                migration_performed=migration_performed,
                final_unified_value=final_unified_value,
                post_migration_state={
                    'unified_grant_field': final_unified_value,
                    'legacy_fields_removed': True
                },
                action="grant_migration_complete"
            )

        # Handle legacy/invalid field names and calculated fields
        calculated_fields = {'total_grant_meur_maroc', 'total_grants_meur', 'investment_for_debt_sizing_meur'}

        # Remove calculated fields that shouldn't be passed to constructor
        for field in calculated_fields:
            if field in data_copy:
                del data_copy[field]

        # Filter out fields that don't belong to the dataclass or can't be initialized
        valid_fields = {
            k: v for k, v in data_copy.items()
            if k in cls.__dataclass_fields__ and
            cls.__dataclass_fields__[k].init
        }
        
        # Log object creation
        if hasattr(logger, 'debug_context'):
            logger.debug_context(
                "Creating EnhancedProjectAssumptions from validated fields",
                correlation_id=correlation_id,
                valid_field_count=len(valid_fields),
                filtered_out_count=len(data_copy) - len(valid_fields),
                key_fields={
                    'capacity_mw': valid_fields.get('capacity_mw'),
                    'capex_meur': valid_fields.get('capex_meur'),
                    'unified_grant': valid_fields.get('_grant_meur_piano_mattei_unified')
                },
                action="from_dict_object_creation"
            )
        
        return cls(**valid_fields)
    
    def get_benchmark_comparison(self) -> Dict[str, Dict[str, Any]]:
        """Compare against industry benchmarks."""
        benchmarks = {
            'capacity_factor': {'value': 0.25, 'unit': '', 'description': 'Industry average'},
            'specific_capex': {'value': 1000, 'unit': 'EUR/kW', 'description': 'Industry average'},
            'specific_opex': {'value': 15, 'unit': 'EUR/kW/year', 'description': 'Industry average'},
            'debt_ratio': {'value': 0.75, 'unit': '', 'description': 'Typical project finance'},
            'interest_rate': {'value': 0.06, 'unit': '', 'description': 'Current market rates'},
            'project_irr_target': {'value': 0.12, 'unit': '', 'description': 'Minimum acceptable'}
        }
        
        current_values = {
            'capacity_factor': self.calculate_capacity_factor(),
            'specific_capex': self.calculate_specific_capex(),
            'specific_opex': self.calculate_specific_opex(),
            'debt_ratio': self.debt_ratio,
            'interest_rate': self.interest_rate,
            'project_irr_target': self.discount_rate  # Using discount rate as proxy
        }
        
        comparison = {}
        for metric, benchmark in benchmarks.items():
            current = current_values.get(metric, 0)
            comparison[metric] = {
                'current': current,
                'benchmark': benchmark['value'],
                'unit': benchmark['unit'],
                'description': benchmark['description'],
                'ratio': current / benchmark['value'] if benchmark['value'] != 0 else 0,
                'status': 'good' if abs(current - benchmark['value']) / benchmark['value'] < 0.2 else 'warning'
            }
        
        return comparison

    # SIMEST Split Structure Methods

    def calculate_simest_grant_component(self) -> float:
        """Calculate the non-repayable grant portion of SIMEST facility.
        
        Returns 10% or 20% of total SIMEST facility based on company location.
        
        Returns:
            float: Grant component in MEUR
        """
        # Get correlation ID for tracking
        correlation_id = None
        if CORRELATION_LOGGING_AVAILABLE:
            correlation_id = CorrelationIDManager.get_correlation_id()
        
        if self._grant_meur_piano_mattei_unified <= 0:
            if hasattr(self.logger, 'debug_context'):
                self.logger.debug_context(
                    "SIMEST grant component calculation: no unified grant amount",
                    correlation_id=correlation_id,
                    unified_grant_amount=self._grant_meur_piano_mattei_unified,
                    action="simest_grant_calculation"
                )
            return 0.0
        
        try:
            grant_percentage = self._get_simest_grant_percentage()
            grant_component = self._grant_meur_piano_mattei_unified * grant_percentage
            
            if hasattr(self.logger, 'debug_context'):
                self.logger.debug_context(
                    "SIMEST grant component calculated successfully",
                    correlation_id=correlation_id,
                    unified_grant_amount=self._grant_meur_piano_mattei_unified,
                    grant_percentage=grant_percentage,
                    grant_component=grant_component,
                    company_southern_italy=self.company_southern_italy,
                    calculation_method="config_service",
                    action="simest_grant_calculation"
                )
            
            return grant_component
            
        except Exception as e:
            fallback_percentage = 0.10
            fallback_component = self._grant_meur_piano_mattei_unified * fallback_percentage
            
            if hasattr(self.logger, 'warning_context'):
                self.logger.warning_context(
                    "Error calculating SIMEST grant component, using fallback",
                    correlation_id=correlation_id,
                    error_message=str(e),
                    error_type=type(e).__name__,
                    unified_grant_amount=self._grant_meur_piano_mattei_unified,
                    fallback_percentage=fallback_percentage,
                    fallback_component=fallback_component,
                    calculation_method="fallback",
                    action="simest_grant_calculation"
                )
            else:
                self.logger.warning(f"Error calculating SIMEST grant component: {e}")
            
            return fallback_component

    def calculate_simest_soft_loan_component(self) -> float:
        """Calculate the soft loan portion of SIMEST facility.
        
        Returns 80-90% of total SIMEST facility based on company location.
        
        Returns:
            float: Soft loan component in MEUR
        """
        # Get correlation ID for tracking
        correlation_id = None
        if CORRELATION_LOGGING_AVAILABLE:
            correlation_id = CorrelationIDManager.get_correlation_id()
        
        if self._grant_meur_piano_mattei_unified <= 0:
            if hasattr(self.logger, 'debug_context'):
                self.logger.debug_context(
                    "SIMEST soft loan component calculation: no unified grant amount",
                    correlation_id=correlation_id,
                    unified_grant_amount=self._grant_meur_piano_mattei_unified,
                    action="simest_soft_loan_calculation"
                )
            return 0.0
        
        try:
            grant_percentage = self._get_simest_grant_percentage()
            soft_loan_percentage = 1.0 - grant_percentage
            soft_loan_component = self._grant_meur_piano_mattei_unified * soft_loan_percentage
            
            if hasattr(self.logger, 'debug_context'):
                self.logger.debug_context(
                    "SIMEST soft loan component calculated successfully",
                    correlation_id=correlation_id,
                    unified_grant_amount=self._grant_meur_piano_mattei_unified,
                    grant_percentage=grant_percentage,
                    soft_loan_percentage=soft_loan_percentage,
                    soft_loan_component=soft_loan_component,
                    company_southern_italy=self.company_southern_italy,
                    calculation_method="config_service",
                    action="simest_soft_loan_calculation"
                )
            
            return soft_loan_component
            
        except Exception as e:
            fallback_percentage = 0.90
            fallback_component = self._grant_meur_piano_mattei_unified * fallback_percentage
            
            if hasattr(self.logger, 'warning_context'):
                self.logger.warning_context(
                    "Error calculating SIMEST soft loan component, using fallback",
                    correlation_id=correlation_id,
                    error_message=str(e),
                    error_type=type(e).__name__,
                    unified_grant_amount=self._grant_meur_piano_mattei_unified,
                    fallback_percentage=fallback_percentage,
                    fallback_component=fallback_component,
                    calculation_method="fallback",
                    action="simest_soft_loan_calculation"
                )
            else:
                self.logger.warning(f"Error calculating SIMEST soft loan component: {e}")
            
            return fallback_component

    def get_simest_soft_loan_terms(self) -> Dict[str, Any]:
        """Get SIMEST soft loan terms from configuration.
        
        Returns:
            Dict containing interest_rate, tenor_years, grace_years
        """
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            return config_service.get_simest_soft_loan_terms()
        except Exception as e:
            self.logger.warning(f"Error getting SIMEST soft loan terms: {e}")
            # Fallback to default terms
            return {
                'interest_rate': 0.00511,  # 0.511%
                'tenor_years': 6,
                'grace_years': 2
            }

    def _get_simest_grant_percentage(self) -> float:
        """Get SIMEST grant percentage based on company location."""
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            return config_service.get_simest_grant_percentage(self.company_southern_italy)
        except Exception as e:
            self.logger.warning(f"Error getting SIMEST grant percentage: {e}")
            # Fallback based on company_southern_italy flag
            return 0.20 if self.company_southern_italy else 0.10

    def _get_simest_eligible_countries(self) -> list:
        """Get list of SIMEST eligible countries from configuration."""
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            return config_service.get_simest_eligible_countries()
        except Exception as e:
            self.logger.warning(f"Error getting SIMEST eligible countries: {e}")
            # Fallback to basic African countries list
            return ['morocco', 'algeria', 'tunisia', 'egypt', 'libya', 'sudan', 'ethiopia', 
                   'kenya', 'uganda', 'tanzania', 'rwanda', 'burundi', 'nigeria', 'ghana', 
                   'south_africa', 'angola', 'mozambique', 'zambia', 'zimbabwe', 'botswana']

    def _validate_simest_eligibility(self) -> Dict[str, Any]:
        """Validate SIMEST program eligibility."""
        # Get correlation ID for tracking
        correlation_id = None
        if CORRELATION_LOGGING_AVAILABLE:
            correlation_id = CorrelationIDManager.get_correlation_id()
        
        if hasattr(self.logger, 'debug_context'):
            self.logger.debug_context(
                "Starting SIMEST eligibility validation",
                correlation_id=correlation_id,
                project_location=self.project_location,
                company_location=self.company_location,
                validation_step="simest_eligibility_check",
                action="validation_step"
            )
        
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            result = config_service.validate_simest_eligibility(
                self.project_location, 
                self.company_location
            )
            
            if hasattr(self.logger, 'info_context'):
                self.logger.info_context(
                    "SIMEST eligibility validation completed via config service",
                    correlation_id=correlation_id,
                    is_eligible=result.get('is_eligible', False),
                    errors=result.get('errors', []),
                    validation_method="config_service",
                    action="simest_eligibility_result"
                )
            
            return result
            
        except Exception as e:
            if hasattr(self.logger, 'warning_context'):
                self.logger.warning_context(
                    "Error validating SIMEST eligibility via config service, using fallback",
                    correlation_id=correlation_id,
                    error_message=str(e),
                    error_type=type(e).__name__,
                    validation_method="fallback",
                    action="simest_eligibility_fallback"
                )
            else:
                self.logger.warning(f"Error validating SIMEST eligibility: {e}")
            
            # Fallback validation with improved location matching
            eligible_countries = self._get_simest_eligible_countries()
            errors = []

            # Improved location validation logic
            project_location_lower = self.project_location.lower()
            eligible_countries_lower = [c.lower() for c in eligible_countries]

            is_location_eligible = False

            # Direct match
            if project_location_lower in eligible_countries_lower:
                is_location_eligible = True
            else:
                # Check if location contains or is contained in any eligible country
                for country in eligible_countries_lower:
                    if (country in project_location_lower or
                        project_location_lower in country or
                        self._is_location_in_country_fallback(project_location_lower, country)):
                        is_location_eligible = True
                        break

            if not is_location_eligible:
                errors.append(f"Project location '{self.project_location}' is not eligible for SIMEST")

            if 'italy' not in self.company_location.lower():
                errors.append(f"Company location '{self.company_location}' must be in Italy for SIMEST eligibility")

            result = {'is_eligible': not errors, 'errors': errors}
            
            if hasattr(self.logger, 'info_context'):
                self.logger.info_context(
                    "SIMEST eligibility validation completed via fallback",
                    correlation_id=correlation_id,
                    is_eligible=result['is_eligible'],
                    errors=result['errors'],
                    eligible_countries_count=len(eligible_countries),
                    validation_method="fallback",
                    action="simest_eligibility_result"
                )
            
            return result

    def _validate_simest_facility_amounts(self) -> Dict[str, str]:
        """Validate SIMEST facility amounts against program limits."""
        # Get correlation ID for tracking
        correlation_id = None
        if CORRELATION_LOGGING_AVAILABLE:
            correlation_id = CorrelationIDManager.get_correlation_id()
        
        errors = {}
        
        if self._grant_meur_piano_mattei_unified <= 0:
            if hasattr(self.logger, 'debug_context'):
                self.logger.debug_context(
                    "Skipping SIMEST facility amount validation - no unified grant amount",
                    correlation_id=correlation_id,
                    unified_grant_amount=self._grant_meur_piano_mattei_unified,
                    validation_step="simest_facility_amounts",
                    action="validation_skip"
                )
            return errors
        
        if hasattr(self.logger, 'debug_context'):
            self.logger.debug_context(
                "Starting SIMEST facility amount validation",
                correlation_id=correlation_id,
                facility_amount=self._grant_meur_piano_mattei_unified,
                validation_step="simest_facility_amounts",
                action="validation_step"
            )
        
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            config = config_service.load_simest_config()
            
            min_facility = config.get('minimum_facility_meur', 0.1)
            max_facility = config.get('maximum_facility_meur', 50.0)
            
            if hasattr(self.logger, 'debug_context'):
                self.logger.debug_context(
                    "SIMEST facility limits loaded from config service",
                    correlation_id=correlation_id,
                    min_facility=min_facility,
                    max_facility=max_facility,
                    current_facility=self._grant_meur_piano_mattei_unified,
                    validation_method="config_service",
                    action="simest_facility_limits"
                )
            
            if self._grant_meur_piano_mattei_unified < min_facility:
                error_msg = f"SIMEST facility amount ({self._grant_meur_piano_mattei_unified:.2f} MEUR) is below minimum ({min_facility} MEUR)"
                errors['simest_min_facility'] = error_msg
                
                if hasattr(self.logger, 'warning_context'):
                    self.logger.warning_context(
                        "SIMEST facility amount below minimum",
                        correlation_id=correlation_id,
                        facility_amount=self._grant_meur_piano_mattei_unified,
                        minimum_required=min_facility,
                        validation_step="simest_min_facility",
                        action="validation_error"
                    )
            
            if self._grant_meur_piano_mattei_unified > max_facility:
                error_msg = f"SIMEST facility amount ({self._grant_meur_piano_mattei_unified:.2f} MEUR) exceeds maximum ({max_facility} MEUR)"
                errors['simest_max_facility'] = error_msg
                
                if hasattr(self.logger, 'warning_context'):
                    self.logger.warning_context(
                        "SIMEST facility amount exceeds maximum",
                        correlation_id=correlation_id,
                        facility_amount=self._grant_meur_piano_mattei_unified,
                        maximum_allowed=max_facility,
                        validation_step="simest_max_facility",
                        action="validation_error"
                    )
                
        except Exception as e:
            if hasattr(self.logger, 'warning_context'):
                self.logger.warning_context(
                    "Error validating SIMEST facility amounts via config service, using fallback",
                    correlation_id=correlation_id,
                    error_message=str(e),
                    error_type=type(e).__name__,
                    validation_method="fallback",
                    action="simest_facility_fallback"
                )
            else:
                self.logger.warning(f"Error validating SIMEST facility amounts: {e}")
            
            # Fallback validation with default limits
            min_facility_fallback = 0.1
            max_facility_fallback = 50.0
            
            if self._grant_meur_piano_mattei_unified < min_facility_fallback:
                errors['simest_min_facility'] = f"SIMEST facility amount ({self._grant_meur_piano_mattei_unified:.2f} MEUR) is below minimum ({min_facility_fallback} MEUR)"
            
            if self._grant_meur_piano_mattei_unified > max_facility_fallback:
                errors['simest_max_facility'] = f"SIMEST facility amount ({self._grant_meur_piano_mattei_unified:.2f} MEUR) exceeds maximum ({max_facility_fallback} MEUR)"
        
        if hasattr(self.logger, 'debug_context'):
            self.logger.debug_context(
                "SIMEST facility amount validation completed",
                correlation_id=correlation_id,
                facility_amount=self._grant_meur_piano_mattei_unified,
                validation_errors=list(errors.keys()),
                validation_passed=len(errors) == 0,
                validation_step="simest_facility_amounts",
                action="validation_complete"
            )
        
        return errors

    def _validate_simest_grant_limits(self) -> Dict[str, str]:
        """Validate SIMEST grant component against CAPEX limits."""
        # Get correlation ID for tracking
        correlation_id = None
        if CORRELATION_LOGGING_AVAILABLE:
            correlation_id = CorrelationIDManager.get_correlation_id()
        
        errors = {}
        
        if self._grant_meur_piano_mattei_unified <= 0:
            if hasattr(self.logger, 'debug_context'):
                self.logger.debug_context(
                    "Skipping SIMEST grant limit validation - no unified grant amount",
                    correlation_id=correlation_id,
                    unified_grant_amount=self._grant_meur_piano_mattei_unified,
                    validation_step="simest_grant_limits",
                    action="validation_skip"
                )
            return errors
        
        grant_component = self.calculate_simest_grant_component()
        grant_percentage = self._get_simest_grant_percentage()
        max_grant_percentage = grant_percentage  # 10% or 20%
        
        # Check if grant component exceeds the allowed percentage of CAPEX
        max_allowed_grant = self.capex_meur * max_grant_percentage
        
        if hasattr(self.logger, 'debug_context'):
            self.logger.debug_context(
                "Starting SIMEST grant limit validation",
                correlation_id=correlation_id,
                grant_component=grant_component,
                grant_percentage=grant_percentage,
                max_allowed_grant=max_allowed_grant,
                capex_meur=self.capex_meur,
                company_southern_italy=self.company_southern_italy,
                validation_step="simest_grant_limits",
                action="validation_step"
            )
        
        if grant_component > max_allowed_grant:
            error_msg = f"SIMEST grant component ({grant_component:.2f} MEUR) exceeds {max_grant_percentage*100:.0f}% of CAPEX limit ({max_allowed_grant:.2f} MEUR)"
            errors['simest_grant_limit'] = error_msg
            
            if hasattr(self.logger, 'error_context'):
                self.logger.error_context(
                    "SIMEST grant component exceeds CAPEX percentage limit",
                    correlation_id=correlation_id,
                    grant_component=grant_component,
                    max_allowed_grant=max_allowed_grant,
                    grant_percentage=grant_percentage,
                    capex_meur=self.capex_meur,
                    excess_amount=grant_component - max_allowed_grant,
                    validation_step="simest_grant_limits",
                    action="validation_error"
                )
        else:
            if hasattr(self.logger, 'debug_context'):
                self.logger.debug_context(
                    "SIMEST grant limit validation passed",
                    correlation_id=correlation_id,
                    grant_component=grant_component,
                    max_allowed_grant=max_allowed_grant,
                    remaining_capacity=max_allowed_grant - grant_component,
                    validation_step="simest_grant_limits",
                    action="validation_success"
                )
        
        return errors

    # Property aliases for backward compatibility and unified grant handling
    @property
    def grant_meur_italy(self) -> float:
        """Legacy property for Italian grants (Piano Mattei).

        Maps to the unified Piano Mattei/SIMEST field to prevent double-counting.
        Piano Mattei budget is managed through SIMEST.
        """
        return self._grant_meur_piano_mattei_unified

    @grant_meur_italy.setter
    def grant_meur_italy(self, value: float):
        """Set Italian grants value in the unified field."""
        self._grant_meur_piano_mattei_unified = value

    @property
    def grant_meur_simest_africa(self) -> float:
        """Legacy property for SIMEST African Fund.

        Maps to the unified Piano Mattei/SIMEST field to prevent double-counting.
        Piano Mattei budget is managed through SIMEST.
        """
        return self._grant_meur_piano_mattei_unified

    @grant_meur_simest_africa.setter
    def grant_meur_simest_africa(self, value: float):
        """Set SIMEST African Fund value in the unified field."""
        self._grant_meur_piano_mattei_unified = value

    def _is_location_in_country_fallback(self, location: str, country: str) -> bool:
        """Fallback method to check if a location is within a specific country using known mappings."""
        # Define known location-to-country mappings for better validation
        location_mappings = {
            'morocco': ['ouarzazate', 'dakhla', 'laayoune', 'laayoune_advanced', 'noor_midelt', 'casablanca', 'rabat', 'marrakech'],
            'algeria': ['algiers', 'oran', 'constantine', 'annaba'],
            'tunisia': ['tunis', 'sfax', 'sousse', 'kairouan'],
            'egypt': ['cairo', 'alexandria', 'giza', 'aswan'],
            'kenya': ['nairobi', 'mombasa', 'kisumu', 'nakuru'],
            'south_africa': ['cape_town', 'johannesburg', 'durban', 'pretoria'],
            'nigeria': ['lagos', 'abuja', 'kano', 'ibadan']
        }

        if country in location_mappings:
            return location in location_mappings[country]

        return False
