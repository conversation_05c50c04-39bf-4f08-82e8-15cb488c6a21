"""
Professional Business Login Layout - Left-Right Split Design
===========================================================

This demonstrates the improved login layout with:
- Left side: Company branding, info, and value proposition
- Right side: Clean, focused login form
- No scrolling required on desktop
- Professional business appearance
"""

import flet as ft

def create_professional_security_screen(page: ft.Page, on_authenticated: callable):
    """Create professional business login screen with left-right layout."""
    
    # Animation state
    login_in_progress = ft.Ref[bool]()
    login_in_progress.current = False

    def on_password_submit(e):
        password = password_field.value.strip()
        if not password:
            show_error("Please enter a password")
            return
        set_loading_state(True)
        if password == "agevolami2025":
            success_animation()
            page.run_task(delayed_authenticate)
        else:
            error_animation()
            set_loading_state(False)

    def show_error(message: str):
        error_text.value = message
        error_text.visible = True
        password_field.bgcolor = ft.Colors.RED_50
        password_field.border_color = ft.Colors.RED_400
        password_field.update()
        error_text.update()

    def on_password_change(e):
        if error_text.visible:
            error_text.visible = False
            password_field.bgcolor = ft.Colors.WHITE
            password_field.border_color = ft.Colors.GREY_400
            password_field.update()
            error_text.update()

    async def delayed_authenticate():
        await asyncio.sleep(1.5)
        on_authenticated()

    def set_loading_state(loading: bool):
        login_in_progress.current = loading
        login_button.disabled = loading
        if loading:
            login_button.text = "Authenticating..."
            login_button.icon = ft.Icons.HOURGLASS_EMPTY
            progress_ring.visible = True
        else:
            login_button.text = "Access Application"
            login_button.icon = ft.Icons.LOGIN
            progress_ring.visible = False
        login_button.update()
        progress_ring.update()

    def success_animation():
        password_field.bgcolor = ft.Colors.GREEN_50
        password_field.border_color = ft.Colors.GREEN_400
        password_field.update()
        success_icon.visible = True
        success_icon.update()

    def error_animation():
        password_field.bgcolor = ft.Colors.RED_50
        password_field.border_color = ft.Colors.RED_400
        password_field.update()

    # Login form components
    password_field = ft.TextField(
        label="Enter Access Password",
        password=True,
        autofocus=True,
        on_submit=on_password_submit,
        on_change=on_password_change,
        width=350,
        height=60,
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border_color=ft.Colors.GREY_400,
        focused_border_color=ft.Colors.BLUE_600,
        cursor_color=ft.Colors.BLUE_600,
        text_style=ft.TextStyle(size=16),
        label_style=ft.TextStyle(color=ft.Colors.GREY_600)
    )

    error_text = ft.Text(
        "Invalid password. Please try again.",
        color=ft.Colors.RED_400,
        visible=False,
        size=14,
        weight=ft.FontWeight.W_500
    )

    success_icon = ft.Icon(
        ft.Icons.CHECK_CIRCLE,
        color=ft.Colors.GREEN_400,
        size=24,
        visible=False
    )

    progress_ring = ft.ProgressRing(
        width=20,
        height=20,
        stroke_width=2,
        visible=False
    )

    login_button = ft.ElevatedButton(
        "Access Application",
        icon=ft.Icons.LOGIN,
        on_click=on_password_submit,
        width=350,
        height=56,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.W_600),
            shape=ft.RoundedRectangleBorder(radius=12),
            elevation=3,
            shadow_color=ft.Colors.BLUE_200,
        )
    )

    # LEFT SIDE: Company Branding & Information
    left_side = ft.Container(
        content=ft.Column([
            # Company Logo/Header
            ft.Container(
                content=ft.Column([
                    ft.Text("🚀 Hiel RnE Modeler",
                           size=36, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.WHITE),
                    ft.Text("v3.0 • 2025 Edition",
                           size=18, color=ft.Colors.BLUE_100,
                           weight=ft.FontWeight.W_500),
                ], spacing=8),
                margin=ft.margin.only(bottom=40)
            ),

            # Value Proposition
            ft.Container(
                content=ft.Column([
                    ft.Text("Advanced Renewable Energy",
                           size=24, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.WHITE),
                    ft.Text("Financial Analysis Platform",
                           size=24, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.WHITE),
                ], spacing=5),
                margin=ft.margin.only(bottom=30)
            ),

            # Key Features
            ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.ANALYTICS, color=ft.Colors.BLUE_200, size=24),
                    ft.Text("Comprehensive Financial Modeling", 
                           size=16, color=ft.Colors.BLUE_100)
                ], spacing=15),
                ft.Row([
                    ft.Icon(ft.Icons.LOCATION_ON, color=ft.Colors.BLUE_200, size=24),
                    ft.Text("Multi-Location Comparison", 
                           size=16, color=ft.Colors.BLUE_100)
                ], spacing=15),
                ft.Row([
                    ft.Icon(ft.Icons.TRENDING_UP, color=ft.Colors.BLUE_200, size=24),
                    ft.Text("Monte Carlo Risk Analysis", 
                           size=16, color=ft.Colors.BLUE_100)
                ], spacing=15),
                ft.Row([
                    ft.Icon(ft.Icons.ASSESSMENT, color=ft.Colors.BLUE_200, size=24),
                    ft.Text("Professional Reports & Charts", 
                           size=16, color=ft.Colors.BLUE_100)
                ], spacing=15),
            ], spacing=20),

            # Spacer
            ft.Container(expand=True),

            # Company Info
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.BUSINESS_CENTER, color=ft.Colors.BLUE_200, size=20),
                        ft.Text("Agevolami SRL", size=16, color=ft.Colors.BLUE_100, weight=ft.FontWeight.BOLD)
                    ], spacing=10),
                    ft.Text("Your gateway to cross-border opportunities", 
                           size=14, color=ft.Colors.BLUE_200, italic=True),
                    ft.Row([
                        ft.Text("www.agevolami.it", size=12, color=ft.Colors.BLUE_200),
                        ft.Text("•", size=12, color=ft.Colors.BLUE_200),
                        ft.Text("www.agevolami.ma", size=12, color=ft.Colors.BLUE_200),
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER)
                ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=20,
                bgcolor=ft.Colors.BLUE_800,
                border_radius=12,
                opacity=0.9
            )
        ], spacing=0),
        padding=50,
        expand=True,
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_700, ft.Colors.BLUE_900]
        )
    )

    # RIGHT SIDE: Login Form
    right_side = ft.Container(
        content=ft.Column([
            # Spacer for vertical centering
            ft.Container(expand=True),
            
            # Login Card
            ft.Container(
                content=ft.Column([
                    # Security Icon
                    ft.Container(
                        content=ft.Icon(ft.Icons.SHIELD, size=48, color=ft.Colors.BLUE_600),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=30,
                        padding=15,
                        margin=ft.margin.only(bottom=25)
                    ),

                    # Title
                    ft.Text("Secure Access Portal",
                           size=28, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.GREY_800,
                           text_align=ft.TextAlign.CENTER),

                    # Subtitle
                    ft.Text("Enter your credentials to access the platform",
                           size=16, color=ft.Colors.GREY_600,
                           text_align=ft.TextAlign.CENTER,
                           weight=ft.FontWeight.W_400),

                    ft.Container(height=35),

                    # Password Field with Success Icon
                    ft.Stack([
                        password_field,
                        ft.Container(
                            content=success_icon,
                            right=15,
                            top=18
                        )
                    ]),

                    # Error Text
                    ft.Container(
                        content=error_text,
                        height=25,
                        alignment=ft.alignment.center_left
                    ),

                    # Login Button with Progress
                    ft.Stack([
                        login_button,
                        ft.Container(
                            content=progress_ring,
                            right=20,
                            top=18
                        )
                    ]),

                    # Security Note
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.SECURITY, color=ft.Colors.GREEN_600, size=16),
                            ft.Text("Secure SSL Encryption", size=12, color=ft.Colors.GREEN_600)
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=8),
                        margin=ft.margin.only(top=25)
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=40,
                bgcolor=ft.Colors.WHITE,
                border_radius=20,
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=30,
                    color=ft.Colors.GREY_300,
                    offset=ft.Offset(0, 10)
                ),
                width=450
            ),
            
            # Spacer for vertical centering
            ft.Container(expand=True),
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=50,
        expand=True,
        bgcolor=ft.Colors.GREY_50
    )

    # MAIN LAYOUT: Responsive Row
    main_layout = ft.ResponsiveRow([
        ft.Container(
            content=left_side,
            col={"sm": 12, "md": 6, "lg": 6, "xl": 6}
        ),
        ft.Container(
            content=right_side,
            col={"sm": 12, "md": 6, "lg": 6, "xl": 6}
        )
    ], expand=True)

    page.add(main_layout)

# Usage example:
# create_professional_security_screen(page, start_main_application)
