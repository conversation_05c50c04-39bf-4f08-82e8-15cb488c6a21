"""
Logging Utilities Module
========================

Comprehensive logging utility module that provides correlation ID generation, 
structured logging with correlation ID support, and LoggerAdapter factories.

This module serves as the foundation for tracing user actions through the entire 
enhanced analysis pipeline, providing observability and debugging capabilities.

Author: Abdelhalim Serhani
Company: Agevolami SRL
"""

import logging
import uuid
import json
import threading
from typing import Dict, Any, Optional, Union
from datetime import datetime
from contextvars import ContextVar
from pathlib import Path


# Context variable for correlation ID (thread-safe)
correlation_id_context: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class CorrelationIDGenerator:
    """Generates and manages correlation IDs for request tracking."""
    
    @staticmethod
    def generate() -> str:
        """Generate a new UUID-based correlation ID."""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_short() -> str:
        """Generate a shorter correlation ID for UI display."""
        return str(uuid.uuid4())[:8]
    
    @staticmethod
    def from_timestamp() -> str:
        """Generate a timestamp-based correlation ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        short_uuid = str(uuid.uuid4())[:8]
        return f"{timestamp}_{short_uuid}"


class CorrelationIDManager:
    """Manages correlation ID context across the application."""
    
    @staticmethod
    def set_correlation_id(correlation_id: str) -> None:
        """Set the correlation ID for the current context."""
        correlation_id_context.set(correlation_id)
    
    @staticmethod
    def get_correlation_id() -> Optional[str]:
        """Get the current correlation ID from context."""
        return correlation_id_context.get()
    
    @staticmethod
    def clear_correlation_id() -> None:
        """Clear the correlation ID from current context."""
        correlation_id_context.set(None)
    
    @staticmethod
    def ensure_correlation_id() -> str:
        """Ensure a correlation ID exists, creating one if needed."""
        correlation_id = correlation_id_context.get()
        if not correlation_id:
            correlation_id = CorrelationIDGenerator.generate()
            correlation_id_context.set(correlation_id)
        return correlation_id


class StructuredLogFormatter(logging.Formatter):
    """Custom formatter that adds structured data and correlation IDs to log records."""
    
    def __init__(self, include_correlation_id: bool = True, json_format: bool = False):
        self.include_correlation_id = include_correlation_id
        self.json_format = json_format
        
        if json_format:
            super().__init__()
        else:
            # Standard format with correlation ID placeholder
            fmt = '%(asctime)s - %(name)s - %(levelname)s'
            if include_correlation_id:
                fmt += ' - [%(correlation_id)s]'
            fmt += ' - %(message)s'
            super().__init__(fmt)
    
    def format(self, record: logging.LogRecord) -> str:
        """Format the log record with correlation ID and structured data."""
        # Add correlation ID to record
        if self.include_correlation_id:
            correlation_id = CorrelationIDManager.get_correlation_id()
            record.correlation_id = correlation_id or 'NO-CORR-ID'
        
        # Add structured data if present
        if hasattr(record, 'structured_data'):
            if self.json_format:
                return self._format_json(record)
            else:
                # Add structured data to message
                structured_str = self._format_structured_data(record.structured_data)
                if structured_str:
                    record.msg = f"{record.msg} | {structured_str}"
        
        if self.json_format:
            return self._format_json(record)
        else:
            return super().format(record)
    
    def _format_json(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add correlation ID
        if self.include_correlation_id:
            correlation_id = CorrelationIDManager.get_correlation_id()
            log_data['correlation_id'] = correlation_id or 'NO-CORR-ID'
        
        # Add structured data
        if hasattr(record, 'structured_data'):
            log_data['structured_data'] = record.structured_data
        
        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_data, default=str)
    
    def _format_structured_data(self, data: Dict[str, Any]) -> str:
        """Format structured data for non-JSON output."""
        if not data:
            return ""
        
        formatted_items = []
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value, default=str)
            else:
                value_str = str(value)
            formatted_items.append(f"{key}={value_str}")
        
        return " | ".join(formatted_items)


class CorrelationLoggerAdapter(logging.LoggerAdapter):
    """LoggerAdapter that automatically injects correlation IDs and supports structured logging."""
    
    def __init__(self, logger: logging.Logger, extra: Optional[Dict[str, Any]] = None):
        super().__init__(logger, extra or {})
    
    def process(self, msg: Any, kwargs: Dict[str, Any]) -> tuple:
        """Process the log message and kwargs, adding correlation ID and structured data."""
        # Get correlation ID
        correlation_id = CorrelationIDManager.get_correlation_id()
        
        # Prepare extra data
        extra = self.extra.copy()
        if correlation_id:
            extra['correlation_id'] = correlation_id
        
        # Handle structured data
        structured_data = kwargs.pop('structured_data', None)
        if structured_data:
            extra['structured_data'] = structured_data
        
        # Update kwargs with extra data
        if 'extra' in kwargs:
            kwargs['extra'].update(extra)
        else:
            kwargs['extra'] = extra
        
        return msg, kwargs
    
    def log_with_context(self, level: int, msg: str, **context) -> None:
        """Log with additional context data."""
        self.log(level, msg, structured_data=context)
    
    def debug_context(self, msg: str, **context) -> None:
        """Debug log with context."""
        self.log_with_context(logging.DEBUG, msg, **context)
    
    def info_context(self, msg: str, **context) -> None:
        """Info log with context."""
        self.log_with_context(logging.INFO, msg, **context)
    
    def warning_context(self, msg: str, **context) -> None:
        """Warning log with context."""
        self.log_with_context(logging.WARNING, msg, **context)
    
    def error_context(self, msg: str, **context) -> None:
        """Error log with context."""
        self.log_with_context(logging.ERROR, msg, **context)
    
    def critical_context(self, msg: str, **context) -> None:
        """Critical log with context."""
        self.log_with_context(logging.CRITICAL, msg, **context)


class LoggerFactory:
    """Factory for creating loggers with correlation ID support."""
    
    @staticmethod
    def get_logger(name: str, 
                   include_correlation_id: bool = True,
                   json_format: bool = False) -> CorrelationLoggerAdapter:
        """Get a logger with correlation ID support."""
        logger = logging.getLogger(name)
        return CorrelationLoggerAdapter(logger)
    
    @staticmethod
    def get_module_logger(module_name: str) -> CorrelationLoggerAdapter:
        """Get a logger for a specific module."""
        return LoggerFactory.get_logger(module_name)
    
    @staticmethod
    def get_view_logger(view_name: str) -> CorrelationLoggerAdapter:
        """Get a logger for a specific view."""
        return LoggerFactory.get_logger(f"views.{view_name}")
    
    @staticmethod
    def get_service_logger(service_name: str) -> CorrelationLoggerAdapter:
        """Get a logger for a specific service."""
        return LoggerFactory.get_logger(f"services.{service_name}")
    
    @staticmethod
    def get_component_logger(component_name: str) -> CorrelationLoggerAdapter:
        """Get a logger for a specific component."""
        return LoggerFactory.get_logger(f"components.{component_name}")


class EnhancedAnalysisLogger:
    """Specialized logger for enhanced analysis pipeline tracking."""
    
    def __init__(self, correlation_id: Optional[str] = None):
        self.correlation_id = correlation_id or CorrelationIDManager.ensure_correlation_id()
        self.logger = LoggerFactory.get_logger("enhanced_analysis")
        
        # Set correlation ID in context
        CorrelationIDManager.set_correlation_id(self.correlation_id)
    
    def log_button_click(self, button_name: str, user_context: Dict[str, Any]) -> None:
        """Log enhanced analysis button click."""
        self.logger.info_context(
            f"Enhanced analysis button clicked: {button_name}",
            button=button_name,
            user_context=user_context,
            action="button_click"
        )
    
    def log_validation_step(self, step_name: str, result: bool, details: Dict[str, Any]) -> None:
        """Log validation step in enhanced analysis."""
        self.logger.info_context(
            f"Validation step: {step_name} - {'PASSED' if result else 'FAILED'}",
            step=step_name,
            result=result,
            details=details,
            action="validation"
        )
    
    def log_grant_field_state(self, grant_type: str, field_states: Dict[str, Any]) -> None:
        """Log Piano Mattei/SIMEST grant field states."""
        self.logger.info_context(
            f"Grant field state: {grant_type}",
            grant_type=grant_type,
            field_states=field_states,
            action="grant_field_tracking"
        )
    
    def log_service_call(self, service_name: str, method_name: str, params: Dict[str, Any]) -> None:
        """Log service method call."""
        # Sanitize sensitive parameters
        sanitized_params = self._sanitize_params(params)
        
        self.logger.info_context(
            f"Service call: {service_name}.{method_name}",
            service=service_name,
            method=method_name,
            params=sanitized_params,
            action="service_call"
        )
    
    def log_error_with_context(self, error: Exception, context: Dict[str, Any]) -> None:
        """Log error with full context."""
        self.logger.error_context(
            f"Error in enhanced analysis: {str(error)}",
            error_type=type(error).__name__,
            error_message=str(error),
            context=context,
            action="error"
        )
    
    def log_progress_update(self, progress: float, message: str, stage: str) -> None:
        """Log progress update."""
        self.logger.debug_context(
            f"Progress update: {progress}% - {message}",
            progress=progress,
            message=message,
            stage=stage,
            action="progress"
        )
    
    def log_analysis_completion(self, results_summary: Dict[str, Any]) -> None:
        """Log analysis completion with results summary."""
        self.logger.info_context(
            "Enhanced analysis completed successfully",
            results_summary=results_summary,
            action="completion"
        )
    
    def _sanitize_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize parameters to remove sensitive data."""
        sanitized = {}
        sensitive_keys = {'password', 'token', 'key', 'secret', 'auth'}
        
        for key, value in params.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                sanitized[key] = "***REDACTED***"
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_params(value)
            elif isinstance(value, (list, tuple)) and len(value) > 10:
                # Truncate large lists for logging
                sanitized[key] = f"[{len(value)} items]"
            else:
                sanitized[key] = value
        
        return sanitized


class LoggingConfiguration:
    """Configuration utilities for the logging system."""
    
    @staticmethod
    def setup_correlation_logging(log_level: str = "INFO",
                                  json_format: bool = False,
                                  log_file: Optional[str] = None) -> None:
        """Setup correlation ID logging for the entire application."""
        # Convert log level string to logging constant
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # Create formatter
        formatter = StructuredLogFormatter(
            include_correlation_id=True,
            json_format=json_format
        )
        
        # Setup handlers
        handlers = []
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(numeric_level)
        handlers.append(console_handler)
        
        # File handler if specified
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            file_handler.setLevel(numeric_level)
            handlers.append(file_handler)
        
        # Configure root logger
        logging.basicConfig(
            level=numeric_level,
            handlers=handlers,
            force=True  # Override existing configuration
        )
    
    @staticmethod
    def setup_enhanced_analysis_logging(output_dir: Optional[str] = None) -> str:
        """Setup specialized logging for enhanced analysis with correlation tracking."""
        if not output_dir:
            output_dir = "logs"
        
        log_dir = Path(output_dir)
        log_dir.mkdir(exist_ok=True)
        
        # Create enhanced analysis log file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"enhanced_analysis_{timestamp}.log"
        
        # Setup JSON logging for enhanced analysis
        LoggingConfiguration.setup_correlation_logging(
            log_level="DEBUG",
            json_format=True,
            log_file=str(log_file)
        )
        
        return str(log_file)


# Convenience functions for common logging patterns
def get_correlation_logger(name: str) -> CorrelationLoggerAdapter:
    """Get a correlation-aware logger for the given name."""
    return LoggerFactory.get_logger(name)


def with_correlation_id(correlation_id: str):
    """Decorator to set correlation ID for a function."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Save current correlation ID
            old_correlation_id = CorrelationIDManager.get_correlation_id()
            
            try:
                # Set new correlation ID
                CorrelationIDManager.set_correlation_id(correlation_id)
                return func(*args, **kwargs)
            finally:
                # Restore old correlation ID
                if old_correlation_id:
                    CorrelationIDManager.set_correlation_id(old_correlation_id)
                else:
                    CorrelationIDManager.clear_correlation_id()
        
        return wrapper
    return decorator


def log_function_call(logger: Optional[CorrelationLoggerAdapter] = None):
    """Decorator to log function calls with correlation ID."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_logger = logger or get_correlation_logger(func.__module__)
            
            func_logger.debug_context(
                f"Function call: {func.__name__}",
                function=func.__name__,
                module=func.__module__,
                args_count=len(args),
                kwargs_keys=list(kwargs.keys()),
                action="function_call"
            )
            
            try:
                result = func(*args, **kwargs)
                func_logger.debug_context(
                    f"Function completed: {func.__name__}",
                    function=func.__name__,
                    success=True,
                    action="function_complete"
                )
                return result
            except Exception as e:
                func_logger.error_context(
                    f"Function failed: {func.__name__}",
                    function=func.__name__,
                    error=str(e),
                    error_type=type(e).__name__,
                    action="function_error"
                )
                raise
        
        return wrapper
    return decorator


# Export main classes and functions
__all__ = [
    'CorrelationIDGenerator',
    'CorrelationIDManager', 
    'StructuredLogFormatter',
    'CorrelationLoggerAdapter',
    'LoggerFactory',
    'EnhancedAnalysisLogger',
    'LoggingConfiguration',
    'get_correlation_logger',
    'with_correlation_id',
    'log_function_call'
]