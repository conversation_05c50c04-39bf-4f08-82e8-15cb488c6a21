name: Continuous Integration

on:
  push:
    branches: [ main, v4.1, develop ]
  pull_request:
    branches: [ main, v4.1 ]
  schedule:
    - cron: '0 2 * * 1'  # Run every Monday at 2 AM

jobs:
  lint:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install linting tools
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy
    
    - name: Run Black formatter check
      run: black --check --diff .
    
    - name: Run isort import sorting check
      run: isort --check-only --diff .
    
    - name: Run flake8 linting
      run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
    
    - name: Run mypy type checking
      run: mypy . --ignore-missing-imports || true

  build-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flet pyinstaller
    
    - name: Test build process
      run: |
        python build_exe.py --platform linux --clean --pyinstaller
      continue-on-error: true

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run Bandit security scan
      run: |
        bandit -r . -f json -o bandit-report.json || true
    
    - name: Run Safety dependency check
      run: |
        safety check --json || true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v4
      with:
        name: security-reports
        path: bandit-report.json

  performance:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark memory-profiler
    
    - name: Run performance tests
      run: |
        python -m pytest tests/ -v --benchmark-only || true
    
    - name: Test ML model performance
      run: |
        python -c "
        from services.ml_prediction_service import MLPredictionService
        import time
        
        service = MLPredictionService()
        start = time.time()
        service.retrain_models()
        print(f'Model training time: {time.time() - start:.2f}s')
        " || true

  compatibility:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Test minimum dependencies
      run: |
        python -m pip install --upgrade pip
        # Install minimum required versions
        pip install flet==0.21.0 pandas==1.5.0 numpy==1.21.0
        python -c "import main; print('Minimum dependencies OK')" || true
    
    - name: Test without optional dependencies
      run: |
        pip uninstall -y scikit-learn
        python -c "
        from services.ml_prediction_service import MLPredictionService
        service = MLPredictionService()
        print('ML service gracefully handles missing sklearn')
        " || true

  documentation:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install docs dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pydoc-markdown
    
    - name: Generate API documentation
      run: |
        python -c "
        import os
        import pkgutil
        import importlib
        
        # Generate basic API docs
        modules = ['services', 'models', 'components', 'utils']
        for module_name in modules:
            try:
                module = importlib.import_module(module_name)
                print(f'=== {module_name.upper()} ===')
                for _, name, _ in pkgutil.iter_modules(module.__path__, module_name + '.'):
                    try:
                        submodule = importlib.import_module(name)
                        print(f'  - {name}: {getattr(submodule, \"__doc__\", \"No description\") or \"No description\"}')
                    except:
                        continue
            except:
                continue
        " > docs/API_REFERENCE.md || true
    
    - name: Upload documentation
      uses: actions/upload-artifact@v4
      with:
        name: documentation
        path: docs/