name: Dependency Update

on:
  schedule:
    - cron: '0 8 * * 1'  # Run every Monday at 8 AM
  workflow_dispatch:  # Allow manual trigger

jobs:
  update-dependencies:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install pip-tools
      run: |
        python -m pip install --upgrade pip
        pip install pip-tools
    
    - name: Update requirements
      run: |
        pip-compile --upgrade requirements.in > requirements.txt || true
    
    - name: Check for security vulnerabilities
      run: |
        pip install safety
        safety check --json > safety-report.json || true
    
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update dependencies'
        title: 'chore: Update dependencies'
        body: |
          ## 🔄 Automated Dependency Update
          
          This PR updates project dependencies to their latest versions.
          
          ### Changes
          - Updated `requirements.txt` with latest compatible versions
          - Ran security scan with `safety`
          
          ### Testing
          - [ ] All tests pass
          - [ ] Application builds successfully
          - [ ] No security vulnerabilities introduced
          
          ### Notes
          Please review the changes and test thoroughly before merging.
          
          ---
          *This PR was created automatically by GitHub Actions*
        branch: dependency-update
        delete-branch: true

  check-outdated:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Check for outdated packages
      run: |
        pip list --outdated --format=json > outdated-packages.json
        echo "### Outdated Packages Report" > outdated-report.md
        echo "Generated on: $(date)" >> outdated-report.md
        echo "" >> outdated-report.md
        python -c "
        import json
        import sys
        
        with open('outdated-packages.json', 'r') as f:
            outdated = json.load(f)
        
        if outdated:
            print('| Package | Current | Latest |')
            print('|---------|---------|--------|')
            for pkg in outdated:
                print(f'| {pkg[\"name\"]} | {pkg[\"version\"]} | {pkg[\"latest_version\"]} |')
        else:
            print('All packages are up to date! 🎉')
        " >> outdated-report.md
    
    - name: Upload outdated report
      uses: actions/upload-artifact@v4
      with:
        name: outdated-packages-report
        path: outdated-report.md

  security-audit:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install safety pip-audit
    
    - name: Run safety check
      run: |
        safety check --json --output safety-report.json || true
    
    - name: Run pip-audit
      run: |
        pip-audit --format=json --output=pip-audit-report.json || true
    
    - name: Generate security report
      run: |
        echo "# Security Audit Report" > security-report.md
        echo "Generated on: $(date)" >> security-report.md
        echo "" >> security-report.md
        
        echo "## Safety Check Results" >> security-report.md
        if [ -f safety-report.json ]; then
          python -c "
          import json
          import sys
          
          try:
              with open('safety-report.json', 'r') as f:
                  safety_data = json.load(f)
              
              if safety_data.get('vulnerabilities'):
                  print('⚠️ **Security vulnerabilities found:**')
                  for vuln in safety_data['vulnerabilities']:
                      print(f'- **{vuln.get(\"package_name\", \"Unknown\")}** {vuln.get(\"installed_version\", \"Unknown\")}: {vuln.get(\"advisory\", \"No description\")}')
              else:
                  print('✅ No security vulnerabilities found!')
          except:
              print('❌ Could not parse safety report')
          " >> security-report.md
        fi
        
        echo "" >> security-report.md
        echo "## Pip-Audit Results" >> security-report.md
        if [ -f pip-audit-report.json ]; then
          python -c "
          import json
          import sys
          
          try:
              with open('pip-audit-report.json', 'r') as f:
                  audit_data = json.load(f)
              
              if audit_data.get('vulnerabilities'):
                  print('⚠️ **Pip-audit vulnerabilities found:**')
                  for vuln in audit_data['vulnerabilities']:
                      print(f'- **{vuln.get(\"package\", \"Unknown\")}** {vuln.get(\"version\", \"Unknown\")}: {vuln.get(\"description\", \"No description\")}')
              else:
                  print('✅ No pip-audit vulnerabilities found!')
          except:
              print('❌ Could not parse pip-audit report')
          " >> security-report.md
        fi
    
    - name: Upload security report
      uses: actions/upload-artifact@v4
      with:
        name: security-audit-report
        path: security-report.md
    
    - name: Create issue if vulnerabilities found
      if: failure()
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          let body = '## 🚨 Security Vulnerabilities Detected\n\n';
          body += 'The automated security audit has detected potential vulnerabilities in project dependencies.\n\n';
          body += '### Action Required\n';
          body += '1. Review the security audit report\n';
          body += '2. Update vulnerable dependencies\n';
          body += '3. Test the application thoroughly\n';
          body += '4. Close this issue once resolved\n\n';
          body += '### Reports\n';
          body += 'Check the workflow artifacts for detailed security reports.\n\n';
          body += `**Workflow Run:** [${context.runId}](${context.payload.repository.html_url}/actions/runs/${context.runId})`;
          
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: '🚨 Security Vulnerabilities Detected',
            body: body,
            labels: ['security', 'bug', 'high-priority']
          });