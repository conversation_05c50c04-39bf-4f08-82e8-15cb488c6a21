name: Build and Release

on:
  push:
    branches: [ main, v4.1 ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:  # Allow manual trigger

jobs:
  build-windows:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flet pyinstaller
    
    - name: Build Windows executable
      run: |
        python build_exe.py --platform windows --clean
      
    - name: Upload Windows artifact
      uses: actions/upload-artifact@v4
      with:
        name: windows-executable
        path: dist/*.exe
        
    - name: Upload build info
      uses: actions/upload-artifact@v4
      with:
        name: build-info-windows
        path: dist/build_info.json

  build-linux:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libasound2-dev \
          libgtk-3-dev \
          libnotify-dev \
          libnss3-dev \
          libxss1 \
          libxtst6 \
          xauth \
          xvfb \
          libatspi2.0-0 \
          libdrm2 \
          libxcomposite1 \
          libxdamage1 \
          libxrandr2 \
          libgbm1 \
          libxkbcommon0 \
          wget \
          fuse
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flet pyinstaller
    
    - name: Build Linux executable
      run: |
        python build_exe.py --platform linux --clean
    
    - name: Install AppImage tools
      run: |
        wget -O appimagetool https://github.com/AppImage/AppImageTool/releases/download/continuous/appimagetool-x86_64.AppImage
        chmod +x appimagetool
        sudo mv appimagetool /usr/local/bin/
    
    - name: Build AppImage
      run: |
        python build_exe.py --platform linux --appimage --clean
    
    - name: Upload Linux executable
      uses: actions/upload-artifact@v4
      with:
        name: linux-executable
        path: dist/HielRnEModeler
        
    - name: Upload AppImage
      uses: actions/upload-artifact@v4
      with:
        name: linux-appimage
        path: dist/*.AppImage
        
    - name: Upload build info
      uses: actions/upload-artifact@v4
      with:
        name: build-info-linux
        path: dist/build_info.json

  build-macos:
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flet pyinstaller
    
    - name: Build macOS executable
      run: |
        python build_exe.py --platform linux --clean  # Flet uses linux target for macOS
    
    - name: Upload macOS artifact
      uses: actions/upload-artifact@v4
      with:
        name: macos-executable
        path: dist/HielRnEModeler
        
    - name: Upload build info
      uses: actions/upload-artifact@v4
      with:
        name: build-info-macos
        path: dist/build_info.json

  create-release:
    needs: [build-windows, build-linux, build-macos]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Download all artifacts
      uses: actions/download-artifact@v4
    
    - name: Rename artifacts for clarity
      run: |
        mkdir -p release-assets
        cp ./windows-executable/HielRnEModeler.exe ./release-assets/HielRnEModeler-Windows.exe
        cp ./linux-appimage/HielRnEModeler.AppImage ./release-assets/HielRnEModeler-Linux.AppImage
        cp ./macos-executable/HielRnEModeler ./release-assets/HielRnEModeler-macOS
    
    - name: Create Release
      uses: softprops/action-gh-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        name: Hiel RnE Modeler ${{ github.ref }}
        files: |
          ./release-assets/HielRnEModeler-Windows.exe
          ./release-assets/HielRnEModeler-Linux.AppImage
          ./release-assets/HielRnEModeler-macOS
        body: |
          ## 🚀 Hiel RnE Modeler v4.1 Release
          
          ### Enhanced ML Financial Modeling for Renewable Energy Projects
          
          **New Features:**
          - ✅ **Enhanced ML Predictions** with ensemble learning
          - ✅ **Cross-validation** for better model accuracy
          - ✅ **Historical data learning** from previous projects
          - ✅ **Weather & regulatory features** for location-based predictions
          - ✅ **Real-time learning** from actual project outcomes
          
          **Downloads:**
          - 🖥️ **Windows:** `HielRnEModeler-Windows.exe`
          - 🐧 **Linux:** `HielRnEModeler-Linux.AppImage` (portable)
          - 🍎 **macOS:** `HielRnEModeler-macOS` (binary)
          
          **What's New:**
          - Professional DCF modeling with 2025 standards
          - Advanced Monte Carlo simulations with correlation matrices
          - Industry benchmark comparisons
          - Comprehensive reporting (Excel, DOCX, HTML)
          - Enhanced security and validation
          
          **System Requirements:**
          - Python 3.8+ (for source)
          - 4GB RAM minimum
          - 1GB free disk space
          
          ---
          Built with ❤️ by Agevolami SRL
        draft: false
        prerelease: false

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Bandit Security Scan
      uses: securecodewarrior/github-action-bandit@v1
      with:
        path: .
        
    - name: Run Safety Check
      run: |
        pip install safety
        safety check --json || true