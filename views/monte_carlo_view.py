"""
Monte Carlo Simulation View
===========================

View component for Monte Carlo simulation results.
"""

import flet as ft
from typing import Dict, Any, Optional
import logging

from .base_view import BaseView


class MonteCarloView(BaseView):
    """View for Monte Carlo simulation results."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.monte_carlo_results: Optional[Dict[str, Any]] = None
        self.n_simulations = 1000
        self.logger = logging.getLogger(__name__)
    
    def build_content(self) -> ft.Control:
        """Build the Monte Carlo view content."""
        
        # Header
        header = self.create_section_header(
            "Monte Carlo Simulation",
            "Risk analysis through probabilistic modeling"
        )
        
        # Simulation settings
        simulation_settings = self._create_simulation_settings()
        
        # Results display
        if self.monte_carlo_results:
            results_content = self._create_results_display()
        else:
            results_content = self.create_empty_state(
                "No Monte Carlo Results",
                "Run Monte Carlo simulation to see risk analysis",
                "Run Simulation",
                self._on_run_simulation
            )
        
        return ft.Column([
            header,
            simulation_settings,
            results_content
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_simulation_settings(self) -> ft.Card:
        """Create simulation settings interface."""
        settings_content = ft.Column([
            ft.Text("Simulation Settings:", size=16, weight=ft.FontWeight.BOLD),
            ft.Row([
                ft.Text("Number of Simulations:", expand=1),
                ft.TextField(
                    value=str(self.n_simulations),
                    width=150,
                    on_change=self._on_simulations_changed
                )
            ]),
            ft.Divider(height=20),
            self.create_action_button(
                "Run Monte Carlo Simulation",
                ft.Icons.CASINO,
                self._on_run_simulation,
                ft.Colors.PURPLE_600
            )
        ])
        
        return self.create_card(
            "Simulation Configuration",
            settings_content,
            icon=ft.Icons.SETTINGS
        )
    
    def _create_results_display(self) -> ft.Card:
        """Create Monte Carlo results display with enhanced data handling."""
        if not self.monte_carlo_results:
            # Enhanced empty state with data status information
            placeholder = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.CASINO, size=48, color=ft.Colors.GREY_400),
                    ft.Text("Monte Carlo Analysis", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_600),
                    ft.Text("Run Monte Carlo simulation to see comprehensive risk analysis",
                          text_align=ft.TextAlign.CENTER, size=14, color=ft.Colors.GREY_500),
                    ft.Container(height=10),
                    ft.Text("📊 NPV Distribution Analysis", size=12, color=ft.Colors.GREY_500),
                    ft.Text("📈 IRR Risk Assessment", size=12, color=ft.Colors.GREY_500),
                    ft.Text("🎯 Probability Metrics", size=12, color=ft.Colors.GREY_500),
                    ft.Text("⚠️ Value at Risk (VaR)", size=12, color=ft.Colors.GREY_500),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                height=400,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.GREY_100,
                border_radius=8,
                padding=20
            )

            return self.create_card(
                "Monte Carlo Risk Analysis",
                placeholder,
                icon=ft.Icons.ASSESSMENT
            )

        # Validate Monte Carlo data structure
        data_status = self._validate_monte_carlo_data(self.monte_carlo_results)
        
        # Create actual Monte Carlo dashboard
        try:
            from components.charts.chart_factory import ChartFactory

            chart_factory = ChartFactory()
            
            # Log data structure for debugging
            self.logger.info(f"Monte Carlo data keys: {list(self.monte_carlo_results.keys())}")
            self.logger.info(f"Data validation status: {data_status}")

            # Create enhanced Monte Carlo dashboard with individual charts
            charts = chart_factory.create_enhanced_monte_carlo_dashboard(
                mc_results=self.monte_carlo_results,
                title="Comprehensive Monte Carlo Risk Analysis"
            )

            # Create summary statistics with data quality indicator
            statistics = self.monte_carlo_results.get('statistics', {})
            risk_metrics = self.monte_carlo_results.get('risk_metrics', {})

            summary_content = self._create_monte_carlo_summary(statistics, risk_metrics, data_status)

            # Create content with individual charts
            content_items = [summary_content, ft.Container(height=20)]

            if charts:
                # Add each individual chart
                for chart in charts:
                    if 'ui_component' in chart:
                        chart_container = ft.Container(
                            content=chart['ui_component'],
                            height=600,
                            margin=ft.margin.only(bottom=20)
                        )
                        content_items.append(chart_container)

                        # Add chart title
                        if 'title' in chart:
                            title_text = ft.Text(
                                chart['title'],
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.BLUE_GREY_800
                            )
                            content_items.insert(-1, ft.Container(content=title_text, margin=ft.margin.only(top=10, bottom=5)))
            else:
                # Fallback if no charts generated
                content_items.append(
                    ft.Container(
                        content=ft.Text(
                            "No Monte Carlo charts available. Please ensure simulation data is valid.",
                            size=14,
                            color=ft.Colors.GREY_600
                        ),
                        height=200,
                        alignment=ft.alignment.center
                    )
                )

            content = ft.Column(content_items, scroll=ft.ScrollMode.AUTO)

            return self.create_card(
                "Monte Carlo Risk Analysis",
                content,
                icon=ft.Icons.ASSESSMENT
            )

        except Exception as e:
            self.logger.error(f"Error creating Monte Carlo dashboard: {e}")
            # Enhanced error display with debugging info
            error_content = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.ERROR, size=32, color=ft.Colors.RED),
                    ft.Text("Chart Generation Error", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.RED),
                    ft.Text(f"Error: {str(e)}", text_align=ft.TextAlign.CENTER, size=12),
                    ft.Container(height=10),
                    ft.Text("Data Debug Info:", size=12, weight=ft.FontWeight.BOLD),
                    ft.Text(f"Available keys: {list(self.monte_carlo_results.keys()) if self.monte_carlo_results else 'None'}", 
                           size=10, color=ft.Colors.GREY_600),
                    ft.Text(f"Data status: {data_status}", size=10, color=ft.Colors.GREY_600),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                height=400,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.RED_50,
                border_radius=8,
                padding=20
            )

            return self.create_card(
                "Monte Carlo Results",
                error_content,
                icon=ft.Icons.ERROR
            )

    def _validate_monte_carlo_data(self, mc_results: Dict[str, Any]) -> Dict[str, str]:
        """Validate Monte Carlo data structure and return status info."""
        status = {
            'overall': 'unknown',
            'data_source': 'unknown',
            'simulation_count': 'unknown',
            'metrics_available': []
        }
        
        if not mc_results:
            status['overall'] = 'no_data'
            return status
        
        # ENHANCED: Check for different data structures from various services
        results = None
        
        # Standard formats from financial service
        if 'results' in mc_results:
            status['data_source'] = 'results_key'
            results = mc_results['results']
        elif 'simulation_results' in mc_results:
            status['data_source'] = 'simulation_results_key'
            results = mc_results['simulation_results']
        # Enhanced service formats
        elif 'monte_carlo' in mc_results:
            status['data_source'] = 'monte_carlo_key'
            results = mc_results['monte_carlo']
        elif 'npv_distribution' in mc_results:
            status['data_source'] = 'npv_distribution_key'
            results = mc_results['npv_distribution']
        # Check for direct array formats
        elif 'IRR_equity' in mc_results or 'NPV_project' in mc_results:
            status['data_source'] = 'direct_arrays'
            results = mc_results  # Use the whole dict as results
        else:
            # Try to find any numerical array or dict with simulation data
            for key, value in mc_results.items():
                if isinstance(value, (list, dict)) and len(value) > 10:
                    # Check if it contains numerical data
                    if isinstance(value, list) and all(isinstance(x, (int, float)) for x in value[:5]):
                        status['data_source'] = f'fallback_{key}'
                        results = {key: value}  # Wrap in dict
                        break
                    elif isinstance(value, dict) and any(isinstance(v, (int, float)) for v in list(value.values())[:5]):
                        status['data_source'] = f'fallback_{key}'
                        results = value
                        break
            
            if results is None:
                status['overall'] = 'no_valid_results'
                return status
        
        # IMPROVED: Better simulation count detection
        if isinstance(results, dict):
            # For dict results, check array length in values
            for key, value in results.items():
                if isinstance(value, list) and len(value) > 1:
                    status['simulation_count'] = str(len(value))
                    break
            else:
                status['simulation_count'] = str(len(results))
        elif isinstance(results, list):
            status['simulation_count'] = str(len(results))
        
        # ENHANCED: Check available metrics with better detection
        if 'statistics' in mc_results:
            status['metrics_available'].extend(list(mc_results['statistics'].keys()))
        
        if 'risk_metrics' in mc_results:
            status['metrics_available'].extend(['risk_analysis'])
        
        # Check for direct metric arrays in results
        if isinstance(results, dict):
            metric_keys = ['IRR_equity', 'IRR_project', 'NPV_project', 'NPV_equity', 'LCOE_eur_kwh', 'Min_DSCR']
            available_metrics = [key for key in metric_keys if key in results and isinstance(results[key], list)]
            status['metrics_available'].extend(available_metrics)
        
        # IMPROVED: Better overall status determination
        has_simulation_data = status['simulation_count'] != 'unknown' and int(status['simulation_count']) > 10
        has_metrics = len(status['metrics_available']) > 0
        
        if has_simulation_data and has_metrics:
            status['overall'] = 'valid'
        elif has_simulation_data or has_metrics:
            status['overall'] = 'limited'
        else:
            status['overall'] = 'invalid'
        
        return status

    def _create_monte_carlo_summary(self, statistics: Dict[str, Any], risk_metrics: Dict[str, Any], data_status: Dict[str, str] = None) -> ft.Container:
        """Create Monte Carlo summary statistics."""
        summary_cards = []

        # IRR Statistics
        if 'IRR_equity' in statistics:
            irr_stats = statistics['IRR_equity']
            summary_cards.append(
                self._create_stat_card(
                    "IRR Equity",
                    f"{irr_stats.get('mean', 0):.1%}",
                    f"σ: {irr_stats.get('std', 0):.1%}",
                    ft.Colors.GREEN_600
                )
            )

        # NPV Statistics
        if 'NPV_project' in statistics:
            npv_stats = statistics['NPV_project']
            npv_mean = npv_stats.get('mean', 0) / 1e6  # Convert to millions
            summary_cards.append(
                self._create_stat_card(
                    "NPV Project",
                    f"€{npv_mean:.1f}M",
                    f"P(NPV>0): {risk_metrics.get('probability_positive_npv', 0):.0%}",
                    ft.Colors.BLUE_600
                )
            )

        # Risk Metrics
        if risk_metrics:
            prob_positive_irr = risk_metrics.get('probability_positive_irr', 0)
            summary_cards.append(
                self._create_stat_card(
                    "Success Rate",
                    f"{prob_positive_irr:.0%}",
                    "P(IRR > 0%)",
                    ft.Colors.PURPLE_600
                )
            )

            var_95 = risk_metrics.get('var_95', 0)
            if var_95 > 0:
                summary_cards.append(
                    self._create_stat_card(
                        "VaR (95%)",
                        f"{var_95:.1%}",
                        "Worst 5% scenario",
                        ft.Colors.ORANGE_600
                    )
                )

        return ft.Container(
            content=ft.Row(summary_cards, alignment=ft.MainAxisAlignment.SPACE_AROUND),
            padding=10
        )

    def _create_stat_card(self, title: str, value: str, subtitle: str, color: str) -> ft.Container:
        """Create a statistics card."""
        return ft.Container(
            content=ft.Column([
                ft.Text(title, size=12, weight=ft.FontWeight.BOLD, color=color, text_align=ft.TextAlign.CENTER),
                ft.Text(value, size=16, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                ft.Text(subtitle, size=10, color=ft.Colors.GREY_600, text_align=ft.TextAlign.CENTER)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, color),
            width=150,
            height=80
        )
    
    def _on_simulations_changed(self, e):
        """Handle number of simulations change."""
        try:
            self.n_simulations = int(e.control.value)
        except ValueError:
            self.n_simulations = 1000
    
    def _on_run_simulation(self, e=None):
        """Run Monte Carlo simulation."""
        self.request_action("run_monte_carlo", {
            "n_simulations": self.n_simulations
        })
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with Monte Carlo results."""
        if "monte_carlo_results" in data:
            self.monte_carlo_results = data["monte_carlo_results"]
            self.refresh()
    
    def set_monte_carlo_results(self, results: Dict[str, Any]):
        """Set Monte Carlo results."""
        self.monte_carlo_results = results
        self.refresh()
