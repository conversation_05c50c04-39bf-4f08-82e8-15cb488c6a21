"""
Project Settings View
====================

View for managing project settings and configurations with JSON support.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable
from pathlib import Path
import json
import logging

from .base_view import BaseView
from config import AppConfig, UIConfig, ExportConfig
from utils.file_utils import FileUtils


class ProjectSettingsView(BaseView):
    """Project settings view with JSON configuration support."""
    
    def __init__(self, 
                 app_config: AppConfig,
                 ui_config: UIConfig,
                 export_config: ExportConfig,
                 on_config_changed: Optional[Callable] = None):
        super().__init__()
        self.app_config = app_config
        self.ui_config = ui_config
        self.export_config = export_config
        self.on_config_changed = on_config_changed
        self.file_utils = FileUtils()
        self.logger = logging.getLogger(__name__)
        
        # UI components
        self.category_tabs = None
        self.status_text = None
        
    def build_content(self) -> ft.Container:
        """Build the main settings content."""
        return ft.Container(
            content=ft.Column([
                self._create_header(),
                ft.Divider(),
                self._create_settings_tabs(),
                ft.Divider(),
                self._create_action_buttons(),
                self._create_status_bar()
            ], spacing=20),
            padding=20,
            expand=True
        )
    
    def _create_header(self) -> ft.Container:
        """Create settings header."""
        return ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.SETTINGS, size=32, color=ft.Colors.BLUE),
                ft.Column([
                    ft.Text(
                        "Project Settings",
                        style=ft.TextThemeStyle.HEADLINE_MEDIUM,
                        weight=ft.FontWeight.BOLD
                    ),
                    ft.Text(
                        "Configure application settings, export options, and templates",
                        style=ft.TextThemeStyle.BODY_MEDIUM,
                        color=ft.Colors.GREY_600
                    )
                ], spacing=5, expand=True),
                ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.FOLDER_OPEN,
                        tooltip="Load Configuration",
                        on_click=self._load_configuration
                    ),
                    ft.IconButton(
                        icon=ft.Icons.SAVE,
                        tooltip="Save Configuration",
                        on_click=self._save_configuration
                    )
                ])
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(horizontal=20, vertical=15),
            bgcolor=ft.Colors.GREY_50,
            border_radius=10
        )
    
    def _create_settings_tabs(self) -> ft.Container:
        """Create tabbed settings interface."""
        self.category_tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="Application",
                    icon=ft.Icons.APP_SETTINGS_ALT,
                    content=self._create_app_settings()
                ),
                ft.Tab(
                    text="Export",
                    icon=ft.Icons.FILE_DOWNLOAD,
                    content=self._create_export_settings()
                ),
                ft.Tab(
                    text="Charts",
                    icon=ft.Icons.ANALYTICS,
                    content=self._create_chart_settings()
                ),
                ft.Tab(
                    text="Templates",
                    icon=ft.Icons.LIBRARY_BOOKS,
                    content=self._create_template_settings()
                )
            ]
        )
        
        return ft.Container(
            content=self.category_tabs,
            expand=True
        )
    
    def _create_app_settings(self) -> ft.Container:
        """Create application settings panel."""
        return ft.Container(
            content=ft.Column([
                self._create_section_title("Application Information"),
                self._create_setting_row(
                    "Application Name",
                    ft.TextField(
                        value=self.app_config.app_name,
                        hint_text="Enter application name",
                        on_change=lambda e: self._update_config('app', 'app_name', e.control.value)
                    )
                ),
                self._create_setting_row(
                    "Company Name",
                    ft.TextField(
                        value=self.app_config.company_name,
                        hint_text="Enter company name",
                        on_change=lambda e: self._update_config('app', 'company_name', e.control.value)
                    )
                ),
                
                self._create_section_title("Default Settings"),
                self._create_setting_row(
                    "Default Currency",
                    ft.Dropdown(
                        value=self.app_config.default_currency,
                        options=[
                            ft.dropdown.Option("EUR", "Euro (€)"),
                            ft.dropdown.Option("USD", "US Dollar ($)"),
                            ft.dropdown.Option("GBP", "British Pound (£)"),
                            ft.dropdown.Option("MAD", "Moroccan Dirham (MAD)")
                        ],
                        on_change=lambda e: self._update_config('app', 'default_currency', e.control.value)
                    )
                ),
                self._create_setting_row(
                    "Auto Save",
                    ft.Switch(
                        value=self.app_config.auto_save,
                        on_change=lambda e: self._update_config('app', 'auto_save', e.control.value)
                    )
                )
            ], scroll=ft.ScrollMode.AUTO, spacing=15),
            padding=20
        )
    
    def _create_export_settings(self) -> ft.Container:
        """Create export settings panel."""
        return ft.Container(
            content=ft.Column([
                self._create_section_title("Export Formats"),
                self._create_export_format_checkboxes(),
                
                self._create_section_title("Quality Settings"),
                self._create_setting_row(
                    "Chart DPI",
                    ft.Dropdown(
                        value=str(self.export_config.chart_dpi),
                        options=[
                            ft.dropdown.Option("100", "100 DPI (Screen)"),
                            ft.dropdown.Option("300", "300 DPI (Print Quality)"),
                            ft.dropdown.Option("600", "600 DPI (High Quality)")
                        ],
                        on_change=lambda e: self._update_config('export', 'chart_dpi', int(e.control.value))
                    )
                )
            ], scroll=ft.ScrollMode.AUTO, spacing=15),
            padding=20
        )
    
    def _create_chart_settings(self) -> ft.Container:
        """Create chart settings panel."""
        return ft.Container(
            content=ft.Column([
                self._create_section_title("Chart Appearance"),
                self._create_setting_row(
                    "Color Scheme",
                    ft.Dropdown(
                        value=self.export_config.chart_color_scheme,
                        options=[
                            ft.dropdown.Option("professional", "Professional"),
                            ft.dropdown.Option("colorful", "Colorful"),
                            ft.dropdown.Option("monochrome", "Monochrome")
                        ],
                        on_change=lambda e: self._update_config('export', 'chart_color_scheme', e.control.value)
                    )
                ),
                
                self._create_section_title("Chart Types"),
                self._create_chart_type_checkboxes()
            ], scroll=ft.ScrollMode.AUTO, spacing=15),
            padding=20
        )
    
    def _create_template_settings(self) -> ft.Container:
        """Create template settings panel."""
        return ft.Container(
            content=ft.Column([
                self._create_section_title("Configuration Templates"),
                ft.Text("Save and load complete project configurations"),
                
                ft.Row([
                    ft.ElevatedButton(
                        "Load Template",
                        icon=ft.Icons.UPLOAD_FILE,
                        on_click=self._load_template
                    ),
                    ft.ElevatedButton(
                        "Save Template",
                        icon=ft.Icons.SAVE_AS,
                        on_click=self._save_template
                    )
                ], spacing=10),
                
                self._create_template_list()
            ], scroll=ft.ScrollMode.AUTO, spacing=15),
            padding=20
        )
    
    def _create_section_title(self, title: str) -> ft.Text:
        """Create section title."""
        return ft.Text(
            title,
            style=ft.TextThemeStyle.TITLE_MEDIUM,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE_700
        )
    
    def _create_setting_row(self, label: str, control: ft.Control) -> ft.Container:
        """Create a setting row with label and control."""
        return ft.Container(
            content=ft.Row([
                ft.Container(
                    content=ft.Text(label, weight=ft.FontWeight.W_500),
                    width=200
                ),
                ft.Container(content=control, expand=True)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(vertical=5, horizontal=10),
            bgcolor=ft.Colors.GREY_50,
            border_radius=5
        )
    
    def _create_export_format_checkboxes(self) -> ft.Column:
        """Create checkboxes for export formats."""
        formats = ["Excel", "DOCX", "HTML", "PDF", "JSON"]
        checkboxes = []
        
        for format_name in formats:
            checkbox = ft.Checkbox(
                label=format_name,
                value=format_name in self.export_config.default_formats,
                on_change=lambda e, fmt=format_name: self._toggle_export_format(fmt, e.control.value)
            )
            checkboxes.append(checkbox)
        
        return ft.Column(checkboxes, spacing=5)
    
    def _create_chart_type_checkboxes(self) -> ft.Column:
        """Create checkboxes for chart types."""
        chart_types = {
            'financial_kpis': 'Financial KPIs',
            'cash_flow_analysis': 'Cash Flow Analysis',
            'dcf_waterfall': 'DCF Waterfall',
            'sensitivity_tornado': 'Sensitivity Analysis',
            'monte_carlo_histogram': 'Monte Carlo',
            'scenario_comparison': 'Scenario Comparison'
        }
        
        checkboxes = []
        for chart_key, chart_label in chart_types.items():
            checkbox = ft.Checkbox(
                label=chart_label,
                value=self.export_config.chart_types_enabled.get(chart_key, True),
                on_change=lambda e, key=chart_key: self._toggle_chart_type(key, e.control.value)
            )
            checkboxes.append(checkbox)
        
        return ft.Column(checkboxes, spacing=5)
    
    def _create_template_list(self) -> ft.Container:
        """Create list of available templates."""
        return ft.Container(
            content=ft.Text("Template management coming soon", color=ft.Colors.GREY_600),
            padding=20,
            alignment=ft.alignment.center
        )
    
    def _create_action_buttons(self) -> ft.Container:
        """Create action buttons."""
        return ft.Container(
            content=ft.Row([
                ft.ElevatedButton(
                    "Load Configuration",
                    icon=ft.Icons.FOLDER_OPEN,
                    on_click=self._load_configuration
                ),
                ft.ElevatedButton(
                    "Save Configuration",
                    icon=ft.Icons.SAVE,
                    on_click=self._save_configuration
                ),
                ft.ElevatedButton(
                    "Reset Defaults",
                    icon=ft.Icons.REFRESH,
                    on_click=self._reset_configuration
                )
            ], spacing=10, wrap=True),
            padding=20,
            alignment=ft.alignment.center
        )
    
    def _create_status_bar(self) -> ft.Container:
        """Create status bar."""
        self.status_text = ft.Text(
            "Ready",
            color=ft.Colors.GREY_600,
            size=12
        )
        
        return ft.Container(
            content=self.status_text,
            padding=10,
            bgcolor=ft.Colors.GREY_100,
            border_radius=5
        )
    
    def _update_config(self, config_type: str, key: str, value: Any):
        """Update configuration value."""
        try:
            if config_type == 'app':
                setattr(self.app_config, key, value)
            elif config_type == 'export':
                setattr(self.export_config, key, value)
            
            self._update_status(f"Updated {key}")
            
        except Exception as e:
            self.logger.error(f"Error updating config: {str(e)}")
            self._update_status(f"Error updating {key}", is_error=True)
    
    def _toggle_export_format(self, format_name: str, enabled: bool):
        """Toggle export format."""
        formats = list(self.export_config.default_formats)
        
        if enabled and format_name not in formats:
            formats.append(format_name)
        elif not enabled and format_name in formats:
            formats.remove(format_name)
        
        self.export_config.default_formats = formats
        self._update_status(f"{'Enabled' if enabled else 'Disabled'} {format_name}")
    
    def _toggle_chart_type(self, chart_key: str, enabled: bool):
        """Toggle chart type."""
        self.export_config.chart_types_enabled[chart_key] = enabled
        self._update_status(f"{'Enabled' if enabled else 'Disabled'} {chart_key}")
    
    def _load_configuration(self, e):
        """Load configuration from JSON."""
        try:
            config_dir = Path("config")
            config_file = config_dir / "saved_config.json"
            
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Update configurations
                if 'app' in config_data:
                    for key, value in config_data['app'].items():
                        if hasattr(self.app_config, key):
                            setattr(self.app_config, key, value)
                
                if 'export' in config_data:
                    for key, value in config_data['export'].items():
                        if hasattr(self.export_config, key):
                            setattr(self.export_config, key, value)
                
                self._update_status("Configuration loaded successfully")
                self.refresh()
            else:
                self._update_status("No saved configuration found", is_error=True)
                
        except Exception as e:
            self.logger.error(f"Error loading configuration: {str(e)}")
            self._update_status("Error loading configuration", is_error=True)
    
    def _save_configuration(self, e):
        """Save current configuration to JSON."""
        try:
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)
            config_file = config_dir / "saved_config.json"
            
            config_data = {
                'app': self.app_config.to_dict(),
                'export': {
                    'default_formats': self.export_config.default_formats,
                    'chart_dpi': self.export_config.chart_dpi,
                    'chart_color_scheme': self.export_config.chart_color_scheme,
                    'chart_types_enabled': self.export_config.chart_types_enabled
                }
            }
            
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            self._update_status("Configuration saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving configuration: {str(e)}")
            self._update_status("Error saving configuration", is_error=True)
    
    def _reset_configuration(self, e):
        """Reset to default configuration."""
        try:
            self.app_config = AppConfig()
            self.export_config = ExportConfig()
            self._update_status("Configuration reset to defaults")
            self.refresh()
            
        except Exception as e:
            self.logger.error(f"Error resetting configuration: {str(e)}")
            self._update_status("Error resetting configuration", is_error=True)
    
    def _load_template(self, e):
        """Load configuration template."""
        self._update_status("Template loading coming soon")
    
    def _save_template(self, e):
        """Save configuration template."""
        self._update_status("Template saving coming soon")
    
    def _update_status(self, message: str, is_error: bool = False):
        """Update status message."""
        if self.status_text:
            self.status_text.value = message
            self.status_text.color = ft.Colors.RED if is_error else ft.Colors.GREEN
            if self.page:
                self.page.update() 