# Build and Deployment Guidelines

## Build System
- **Primary**: Use Flet's built-in build system (`flet build`) for cross-platform executables
- **Fallback**: PyInstaller for compatibility when Flet build fails
- **Scripts**: Automated build scripts (build.py, build.bat, build.ps1) for different platforms

## Build Configuration
- **Target Platforms**: Windows (.exe), Linux (binary + AppImage)
- **Bundle Type**: Single-file executable with embedded assets
- **Icon**: Use assets/logo.ico for Windows, assets/Hiel RnE Logo.png for Linux
- **Version Info**: Embedded version information and company details

## Dependencies Management
- **Requirements**: All dependencies listed in requirements.txt
- **Version Pinning**: Use compatible version ranges (>=) for stability
- **Hidden Imports**: Explicitly include all application modules in build scripts
- **Data Files**: Bundle config/, data/, and template directories

## Build Process
1. **Dependency Check**: Verify all required packages are installed
2. **Clean Build**: Remove previous build artifacts
3. **Asset Preparation**: Ensure icons and data files are available
4. **Compilation**: Use Flet build with fallback to PyInstaller
5. **Packaging**: Create distribution-ready executables
6. **Validation**: Test built executable functionality

## Distribution
- **Windows**: Single .exe file with embedded dependencies
- **Linux**: Binary executable + optional AppImage for portability
- **Output Location**: dist/ directory with build metadata
- **File Naming**: HielRnEModeler.exe / HielRnEModeler (Linux)

## Build Scripts Usage
```bash
# Windows
build.bat                    # Interactive build menu
python build_exe.py         # Standard build
python build_exe.py --clean # Clean build

# Linux
./build.ps1                 # PowerShell script
python build_exe.py --platform linux --appimage
```

## Quality Assurance
- **Pre-build Checks**: Validate all dependencies and assets
- **Post-build Testing**: Verify executable launches and core functions work
- **Size Optimization**: Monitor executable size and optimize if needed
- **Performance Testing**: Ensure build doesn't impact application performance

## Version Management
- **Version Info**: Embedded in executable metadata
- **Build Info**: JSON file with build details and timestamps
- **Changelog**: Track changes between builds
- **Release Notes**: Document new features and fixes

## Deployment Considerations
- **System Requirements**: Python not required on target systems
- **File Permissions**: Ensure executable permissions on Linux
- **Antivirus**: May require whitelisting for some antivirus software
- **User Data**: Application creates data folders in user Documents directory

## Troubleshooting
- **Missing Dependencies**: Check requirements.txt and hidden imports
- **Build Failures**: Try PyInstaller fallback if Flet build fails
- **Runtime Errors**: Verify all data files are properly bundled
- **Performance Issues**: Check for unnecessary dependencies in build