# Coding Standards

## Python Style Guide
- Follow **PEP 8** Python style guide
- Use **black** for automatic code formatting (line length: 88 characters)
- Use **flake8** for linting
- Use **mypy** for type checking

## Code Organization
- **One class per file** for models and services
- **Descriptive module names** that reflect their purpose
- **Clear import organization**: standard library, third-party, local imports
- **Docstrings** for all classes and public methods using Google style

## Naming Conventions
- **Classes**: PascalCase (e.g., `FinancialModelService`)
- **Functions/Methods**: snake_case (e.g., `run_financial_model`)
- **Variables**: snake_case (e.g., `project_assumptions`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `DEFAULT_DISCOUNT_RATE`)
- **Private methods**: prefix with underscore (e.g., `_validate_inputs`)

## Error Handling
- Use **custom exceptions** for domain-specific errors
- Implement **comprehensive error handling** with user-friendly messages
- Use **logging** for debugging and monitoring
- Provide **fallback mechanisms** for critical operations

## Data Validation
- Use **pydantic** models for data validation
- Validate inputs at **service layer boundaries**
- Return **structured validation errors** with field-specific messages
- Use **type hints** throughout the codebase

## Testing Standards
- Write **unit tests** for all service methods
- Use **pytest** as the testing framework
- Maintain **test coverage** above 80%
- Use **descriptive test names** that explain the scenario

## Documentation
- Include **module-level docstrings** explaining purpose
- Document **complex algorithms** with inline comments
- Maintain **README files** for each major component
- Use **type hints** for better code documentation

## File Structure Standards
```python
"""
Module Description
==================

Brief description of the module's purpose and functionality.
"""

# Standard library imports
import os
import sys
from typing import Dict, Any, Optional

# Third-party imports
import pandas as pd
import numpy as np

# Local imports
from models.client_profile import ClientProfile
from services.base_service import BaseService
```

## Configuration Management
- Use **dataclasses** for configuration objects
- Support **JSON configuration files**
- Provide **environment variable overrides**
- Include **validation** for configuration values