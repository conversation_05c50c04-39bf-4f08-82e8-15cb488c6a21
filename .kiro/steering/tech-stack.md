# Technology Stack

## Core Framework
- **Flet**: Modern Python framework for building native apps with web technologies
- **Python 3.8+**: Primary programming language

## Key Dependencies
- **pandas**: Data processing and analysis
- **numpy**: Numerical computations
- **matplotlib**: Chart generation and visualization
- **plotly**: Interactive visualizations and 3D charts
- **openpyxl**: Excel file generation
- **python-docx**: Word document generation
- **pydantic**: Data validation and settings management
- **scipy**: Scientific computing for Monte Carlo simulations

## Architecture Patterns
- **MVC Pattern**: Clear separation between Models, Views, and Controllers
- **Service Layer**: Business logic encapsulated in service classes
- **Repository Pattern**: Data access abstraction
- **Observer Pattern**: Event-driven UI updates

## Project Structure
```
├── app/                    # Application core (controllers, state)
├── models/                 # Data models and validation
├── services/               # Business logic services
├── views/                  # UI view components
├── components/             # Reusable UI components
├── config/                 # Configuration management
├── utils/                  # Utility functions
├── data/                   # Data files and templates
├── assets/                 # Static assets (icons, images)
└── main.py                 # Application entry point
```

## Build System
- **Flet Build**: Primary build system for cross-platform executables
- **PyInstaller**: Fallback build system
- **Custom build scripts**: build.py, build.bat, build.ps1

## Development Tools
- **pytest**: Testing framework
- **black**: Code formatting
- **flake8**: Code linting
- **mypy**: Type checking

## Enhanced Features
- **ML Integration**: scikit-learn for financial predictions
- **3D Visualization**: Advanced plotly charts
- **Caching**: Redis-based caching for performance
- **Persistence**: SQLite for data storage with versioning