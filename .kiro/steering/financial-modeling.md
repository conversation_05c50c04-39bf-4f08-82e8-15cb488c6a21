# Financial Modeling Standards

## DCF Model Requirements
- Use **Enhanced DCF Model** with 2025 industry standards
- Implement **comprehensive cash flow modeling** with monthly granularity
- Calculate **standard financial KPIs**: IRR (project & equity), NPV, LCOE, DSCR, Payback Period
- Support **terminal value calculations** using perpetuity growth method

## Key Financial Metrics
- **IRR (Internal Rate of Return)**: Both project and equity IRR
- **NPV (Net Present Value)**: Project and equity NPV calculations
- **LCOE (Levelized Cost of Energy)**: EUR/kWh with grant impact analysis
- **DSCR (Debt Service Coverage Ratio)**: Minimum, average, and timeline
- **Payback Period**: Simple and discounted payback calculations

## Risk Analysis Standards
- **Monte Carlo Simulation**: Support 1,000-10,000 iterations with correlation matrices
- **Sensitivity Analysis**: ±20% variations on key parameters
- **Scenario Analysis**: Base, Optimistic, Pessimistic scenarios
- **Risk Metrics**: VaR, CVaR, probability distributions

## Grant Integration
- **Italian Government Grants**: Integration with Italian incentive schemes
- **MASEN Strategic Grants**: Moroccan renewable energy grants
- **Grid Connection Grants**: Infrastructure support grants
- **SIMEST African Fund**: International development funding
- **Impact Analysis**: Detailed breakdown of grant impact on LCOE

## Input Parameters
- **Technical**: Capacity (MW), Production (MWh/year), Degradation rates
- **Financial**: CAPEX, OPEX, PPA prices, Debt terms, Tax rates
- **Economic**: Discount rates, Inflation, Currency assumptions
- **Location**: Country-specific parameters, Grid connection costs

## Validation Rules
- **Range Validation**: All inputs within realistic industry ranges
- **Consistency Checks**: Cross-parameter validation (e.g., debt ratio vs. DSCR)
- **Industry Benchmarks**: Compare results against industry standards
- **Sanity Checks**: Flag unrealistic results for review

## Output Standards
- **Comprehensive Cash Flow**: 25-year monthly cash flow projections
- **KPI Dashboard**: Summary of all key financial metrics
- **Charts and Graphs**: Professional visualizations for all metrics
- **Sensitivity Tables**: Tornado charts and data tables
- **Risk Distributions**: Probability distributions from Monte Carlo

## Calculation Precision
- Use **Decimal precision** for financial calculations
- Round **percentages to 2 decimal places**
- Round **monetary values to nearest thousand EUR**
- Maintain **calculation audit trails** for verification