"""
Application Controller
======================

Main application controller coordinating between views and services.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable, List
import logging
import asyncio
import time
from pathlib import Path
from datetime import datetime

from app.app_state import AppState
from models.ui_state import UIState, TabState
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions

from services.financial_service import FinancialModelService
from services.validation_service import ValidationService
from services.export_service import ExportService
from services.location_service import LocationComparisonService
from services.report_service import ReportGenerationService
from services.health_monitor import setup_health_monitoring, get_health_status
from services.error_handler import global_error_handler

# Enhanced Integration Service
from services.enhanced_integration_service import get_integration_service

from views.project_setup_view import ProjectSetupView
from views.dashboard_view import DashboardView
from views.location_comparison_view import LocationComparisonView
from views.financial_model_view import FinancialModelView
from views.validation_view import ValidationView
from views.sensitivity_view import SensitivityView
from views.monte_carlo_view import Monte<PERSON>ar<PERSON>View
from views.scenarios_view import Sc<PERSON><PERSON>sView
from views.export_view import ExportView

from utils.file_utils import FileUtils

# Enhanced Logging System
try:
    from utils.logging_utils import (
        CorrelationIDManager, 
        CorrelationIDGenerator,
        LoggerFactory,
        EnhancedAnalysisLogger,
        get_correlation_logger
    )
    ENHANCED_LOGGING_AVAILABLE = True
except ImportError:
    # Fallback logging if enhanced logging not available
    ENHANCED_LOGGING_AVAILABLE = False
    
    class CorrelationIDManager:
        @staticmethod
        def get_correlation_id(): return None
        @staticmethod
        def set_correlation_id(cid): pass
        @staticmethod
        def ensure_correlation_id(): return "fallback-id"
    
    class CorrelationIDGenerator:
        @staticmethod
        def generate(): return "fallback-id"
    
    def get_correlation_logger(name): return logging.getLogger(name)

from components.ui.loading_overlay import LoadingOverlay
from components.ui.notification_system import NotificationSystem
from components.ui.theme_manager import ThemeManager
from services.simple_progress_service import SimpleProgressService
from components.widgets.status_bar import StatusBar

# Modern UI System
from components.ui.modern_theme_system import get_theme, set_theme_mode
from components.ui.modern_navigation import ModernSidebar, ModernBreadcrumb, ModernTopBar, create_navigation_items, NavigationState
from components.ui.modern_components import ModernButton, ModernCard, ButtonVariant, ComponentSize


class AppController:
    """Main application controller."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        
        # Initialize enhanced logging system
        if ENHANCED_LOGGING_AVAILABLE:
            self.logger = get_correlation_logger(__name__)
            self.enhanced_analysis_logger = None  # Will be created per analysis session
        else:
            self.logger = logging.getLogger(__name__)
            self.enhanced_analysis_logger = None
        
        # Initialize state
        self.app_state = AppState()
        self.ui_state = UIState()
        
        # Initialize services with error handling and enhanced logging
        try:
            # Generate correlation ID for service initialization
            init_correlation_id = CorrelationIDGenerator.generate()
            CorrelationIDManager.set_correlation_id(init_correlation_id)
            
            self.logger.info("Starting service initialization with enhanced logging and diagnostics",
                           structured_data={
                               'enhanced_logging_available': ENHANCED_LOGGING_AVAILABLE,
                               'initialization_id': init_correlation_id,
                               'action': 'service_init_start'
                           })
            
            # Enhanced Integration Service (primary)
            self.enhanced_service = get_integration_service()
            
            # Log enhanced service availability and capabilities
            if self.enhanced_service:
                service_capabilities = {
                    'ml_predictions': hasattr(self.enhanced_service, 'ml_service') and self.enhanced_service.ml_service is not None,
                    'cache_service': hasattr(self.enhanced_service, 'cache_service') and self.enhanced_service.cache_service is not None,
                    'persistence_service': hasattr(self.enhanced_service, 'persistence_service') and self.enhanced_service.persistence_service is not None,
                    'undo_redo_service': hasattr(self.enhanced_service, 'undo_redo_service') and self.enhanced_service.undo_redo_service is not None,
                    'recovery_service': hasattr(self.enhanced_service, 'recovery_service') and self.enhanced_service.recovery_service is not None
                }
                
                self.logger.info("Enhanced integration service initialized successfully",
                               structured_data={
                                   'service_capabilities': service_capabilities,
                                   'action': 'enhanced_service_init'
                               })
            else:
                self.logger.warning("Enhanced integration service not available, using fallback services",
                                  structured_data={'action': 'enhanced_service_fallback'})
            
            # Standard services (for compatibility)
            self.financial_service = FinancialModelService()
            self.validation_service = ValidationService()
            self.export_service = ExportService()
            self.location_service = LocationComparisonService()
            self.report_service = ReportGenerationService()
            self.file_utils = FileUtils()

            # Setup health monitoring
            setup_health_monitoring()
            
            self.logger.info("All services initialized successfully with enhanced features and health monitoring",
                           structured_data={
                               'total_services': 7,
                               'enhanced_service_available': self.enhanced_service is not None,
                               'health_monitoring_enabled': True,
                               'action': 'service_init_complete'
                           })

        except Exception as e:
            # Log initialization error with correlation ID
            self.logger.error("Failed to initialize services",
                            structured_data={
                                'error': str(e),
                                'error_type': type(e).__name__,
                                'action': 'service_init_error'
                            })
            
            global_error_handler.handle_error(e,
                context={'function': 'service_initialization', 'correlation_id': init_correlation_id},
                show_user_message=True,
                page=self.page)
        finally:
            # Clear initialization correlation ID
            CorrelationIDManager.clear_correlation_id()
        
        # Initialize views
        self.views = {}
        self._initialize_views()
        
        # UI components
        self.status_bar: Optional[ft.Container] = None
        self.progress_bar: Optional[ft.ProgressBar] = None
        self.main_content: Optional[ft.Container] = None
        self.navigation_rail: Optional[ft.NavigationRail] = None
        self.loading_overlay = LoadingOverlay(self.page)
        self.simple_progress = SimpleProgressService(self.page)
        self.notification_system = NotificationSystem(self.page)
        self.theme_manager = ThemeManager()
        
        # Modern UI System
        self.modern_theme = get_theme()
        self.modern_sidebar: Optional[ModernSidebar] = None
        self.modern_breadcrumb: Optional[ModernBreadcrumb] = None
        self.modern_topbar: Optional[ModernTopBar] = None
        self.current_breadcrumb: List[Dict[str, str]] = []
        
        # Apply modern theme to page
        self.modern_theme.apply_to_page(self.page)
        
        # Setup page
        self._setup_page()
    
    def _initialize_views(self):
        """Initialize all view components."""
        self.views = {
            TabState.PROJECT_SETUP: ProjectSetupView(self.page),
            TabState.DASHBOARD: DashboardView(self.page),
            TabState.LOCATION_COMPARISON: LocationComparisonView(self.page),
            TabState.FINANCIAL_MODEL: FinancialModelView(self.page),
            TabState.VALIDATION: ValidationView(self.page),
            TabState.SENSITIVITY: SensitivityView(self.page),
            TabState.MONTE_CARLO: MonteCarloView(self.page),
            TabState.SCENARIOS: ScenariosView(self.page),
            TabState.EXPORT: ExportView(self.page)
        }
        
        # Setup view callbacks
        for view in self.views.values():
            view.on_navigate = self.navigate_to_tab
            view.on_data_changed = self.handle_data_change
            view.on_action_requested = self.handle_action_request
            view.on_status_update = self.update_status
    
    def _setup_page(self):
        """Setup the main page layout."""
        self.page.title = "Hiel RnE Modeler v3.0 - Agevolami SRL"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.padding = 0

        # Set window icon
        try:
            import os
            # Try .ico first (preferred for Windows), then .png as fallback
            if os.path.exists("assets/logo.ico"):
                self.page.window_icon = "assets/logo.ico"
            elif os.path.exists("assets/Hiel RnE Logo.png"):
                self.page.window_icon = "assets/Hiel RnE Logo.png"
        except Exception as e:
            print(f"Could not set window icon: {e}")
        
        # Setup keyboard shortcuts for enhanced features
        self.page.on_keyboard_event = self._on_keyboard_event
        
        # Create modern layout
        self._create_modern_sidebar()
        self._create_modern_topbar()
        self._create_modern_main_content()
        self._create_status_bar()
        
        # Setup modern page layout
        self.page.add(
            ft.Row([
                self.modern_sidebar.build(),
                ft.Column([
                    self.modern_topbar.build(),
                    self.modern_breadcrumb.build() if self.modern_breadcrumb else ft.Container(),
                    self.main_content,
                    self.status_bar
                ], expand=True)
            ], expand=True)
        )
        
        # NOTE: Don't add enhanced progress overlay to page.overlay during initialization
        # It will be added only when show() is called to avoid blocking interactions
        
        # Initial navigation
        self.navigate_to_tab(TabState.PROJECT_SETUP)
    
    def _create_modern_sidebar(self):
        """Create modern sidebar navigation with enhanced UX."""
        navigation_items = create_navigation_items()
        
        self.modern_sidebar = ModernSidebar(
            navigation_items=navigation_items,
            on_navigate=self._handle_modern_navigation,
            on_state_change=self._handle_sidebar_state_change,
            initial_state=NavigationState.EXPANDED,
            show_user_profile=True,
            user_name="Financial Analyst",
            user_avatar=None
        )
    
    def _create_modern_topbar(self):
        """Create modern top navigation bar."""
        # Quick action buttons
        quick_actions = [
            ModernButton(
                "New Project",
                variant=ButtonVariant.PRIMARY,
                size=ComponentSize.SM,
                icon=ft.Icons.ADD,
                on_click=self._handle_new_project
            ).build(),
            ModernButton(
                "Export",
                variant=ButtonVariant.SECONDARY,
                size=ComponentSize.SM,
                icon=ft.Icons.DOWNLOAD,
                on_click=self._handle_quick_export
            ).build()
        ]
        
        self.modern_topbar = ModernTopBar(
            title="Enhanced Financial Analysis",
            actions=quick_actions,
            show_search=True,
            on_search=self._handle_global_search
        )
    
    def _create_modern_main_content(self):
        """Create modern main content area with enhanced styling."""
        # Initialize with home breadcrumb
        self.current_breadcrumb = [
            {"label": "Home", "route": "/dashboard"},
            {"label": "Project Setup", "route": "/setup"}
        ]
        
        self.modern_breadcrumb = ModernBreadcrumb(
            items=self.current_breadcrumb,
            on_navigate=self._handle_breadcrumb_navigation
        )

        # Main content container with modern styling (single child)
        self.main_content = ft.Container(
            content=ft.Container(
                content=ft.Text("Loading enhanced interface...", 
                               text_align=ft.TextAlign.CENTER,
                               size=16,
                               color=self.modern_theme.get_text_colors()['secondary']),
                expand=True,
                padding=self.modern_theme.tokens.spacing['xxl'],
                bgcolor=self.modern_theme.get_background_colors()['primary']
            ),
            expand=True
        )
    
    def _create_status_bar(self):
        """Create enhanced status bar with health monitoring."""
        self.progress_bar = ft.ProgressBar(visible=False)

        # Health status indicator
        self.health_indicator = ft.Icon(
            ft.Icons.HEALTH_AND_SAFETY,
            color=ft.Colors.GREEN,
            size=16,
            tooltip="System Health: All services operational"
        )

        # Enhanced features indicators
        self.enhanced_indicators = ft.Row([
            ft.Icon(ft.Icons.MEMORY, size=14, color=ft.Colors.BLUE_600, tooltip="ML Predictions Active"),
            ft.Icon(ft.Icons.VIEW_IN_AR, size=14, color=ft.Colors.PURPLE_600, tooltip="3D Charts Available"),
            ft.Icon(ft.Icons.SAVE, size=14, color=ft.Colors.GREEN_600, tooltip="Auto-Save Enabled"),
            ft.Icon(ft.Icons.UNDO, size=14, color=ft.Colors.ORANGE_600, tooltip="Undo/Redo Available")
        ], spacing=4, visible=True)

        self.status_bar = ft.Container(
            content=ft.Column([
                self.progress_bar,
                ft.Row([
                    ft.Row([
                        ft.Text("Ready", size=12, color=ft.Colors.GREY_600),
                        self.health_indicator,
                        ft.VerticalDivider(width=1),
                        self.enhanced_indicators
                    ], spacing=8),
                    ft.Text("Hiel RnE Modeler v3.0 • Agevolami SRL • 🚀 All Advanced Features Active",
                           size=12, color=ft.Colors.GREY_600, expand=True,
                           text_align=ft.TextAlign.RIGHT)
                ])
            ]),
            padding=ft.padding.symmetric(horizontal=20, vertical=10),
            bgcolor=ft.Colors.GREY_100,
            height=60
        )

        # Start periodic health checks
        self.page.run_task(self._periodic_health_check)
    
    def _on_keyboard_event(self, e: ft.KeyboardEvent):
        """Handle keyboard shortcuts for enhanced features."""
        try:
            # Undo: Ctrl+Z
            if e.key == "Z" and e.ctrl and not e.shift:
                if self.enhanced_service.undo_redo_service:
                    result = self.enhanced_service.undo_redo_service.undo()
                    if result is not None:
                        self.show_success("Action undone")
                        self.refresh_current_view()
                    else:
                        self.show_error("Nothing to undo")
                e.prevent_default = True
            
            # Redo: Ctrl+Shift+Z or Ctrl+Y
            elif ((e.key == "Z" and e.ctrl and e.shift) or 
                  (e.key == "Y" and e.ctrl)):
                if self.enhanced_service.undo_redo_service:
                    result = self.enhanced_service.undo_redo_service.redo()
                    if result is not None:
                        self.show_success("Action redone")
                        self.refresh_current_view()
                    else:
                        self.show_error("Nothing to redo")
                e.prevent_default = True
            
            # Save: Ctrl+S
            elif e.key == "S" and e.ctrl:
                if self.enhanced_service.persistence_service:
                    project_id = self.app_state.client_profile.get_clean_company_name()
                    version_id = self.enhanced_service.save_project_with_versioning(
                        project_id=project_id,
                        project_data={
                            'client_profile': self.app_state.client_profile.to_dict(),
                            'assumptions': self.app_state.project_assumptions.to_dict(),
                            'results': self.app_state.financial_results
                        }
                    )
                    self.show_success(f"Project saved (version: {version_id})")
                e.prevent_default = True
                
        except Exception as ex:
            self.logger.error(f"Keyboard shortcut error: {ex}")
    
    def refresh_current_view(self):
        """Refresh the currently active view."""
        current_tab = self.ui_state.current_tab
        if current_tab in self.views:
            view = self.views[current_tab]
            if hasattr(view, 'refresh'):
                view.refresh()
    
    def _handle_modern_navigation(self, route: str):
        """Handle modern sidebar navigation."""
        # Map routes to tab states
        route_mapping = {
            "/dashboard": TabState.DASHBOARD,
            "/setup": TabState.PROJECT_SETUP,
            "/locations": TabState.LOCATION_COMPARISON,
            "/analysis": TabState.FINANCIAL_MODEL,  # Default analysis view
            "/analysis/financial": TabState.FINANCIAL_MODEL,
            "/analysis/sensitivity": TabState.SENSITIVITY,
            "/analysis/monte_carlo": TabState.MONTE_CARLO,
            "/analysis/scenarios": TabState.SCENARIOS,
            "/validation": TabState.VALIDATION,
            "/export": TabState.EXPORT
        }
        
        selected_tab = route_mapping.get(route, TabState.PROJECT_SETUP)
        self.navigate_to_tab(selected_tab)
        
        # Update breadcrumbs
        self._update_modern_breadcrumbs(route)
    
    def _handle_sidebar_state_change(self, state: NavigationState):
        """Handle sidebar state changes."""
        self.logger.info(f"Sidebar state changed to: {state.value}")
        # Could trigger page layout adjustments here
    
    def _handle_breadcrumb_navigation(self, route: str):
        """Handle breadcrumb navigation clicks."""
        self._handle_modern_navigation(route)
    
    def _handle_new_project(self, _):
        """Handle new project button click."""
        # Clear current project data
        self.app_state.reset()
        self.navigate_to_tab(TabState.PROJECT_SETUP)
        self.notification_system.show_success("Started new project")
    
    def _handle_quick_export(self, _):
        """Handle quick export button click."""
        self.navigate_to_tab(TabState.EXPORT)
    
    def _handle_global_search(self, query: str):
        """Handle global search functionality."""
        if query.strip():
            self.logger.info(f"Global search: {query}")
            # TODO: Implement search functionality
            self.notification_system.show_info(f"Searching for: {query}")
    
    def _update_modern_breadcrumbs(self, route: str):
        """Update breadcrumb navigation with improved context and reduced redundancy."""
        breadcrumb_map = {
            "/dashboard": [
                {"label": "Dashboard", "route": "/dashboard"}
            ],
            "/setup": [
                {"label": "Project Setup", "route": "/setup"}
            ],
            "/locations": [
                {"label": "Setup", "route": "/setup"},
                {"label": "Location Analysis", "route": "/locations"}
            ],
            "/analysis": [
                {"label": "Analysis", "route": "/analysis"}
            ],
            "/analysis/financial": [
                {"label": "Analysis", "route": "/analysis"},
                {"label": "Financial Model", "route": "/analysis/financial"}
            ],
            "/analysis/sensitivity": [
                {"label": "Analysis", "route": "/analysis"},
                {"label": "Sensitivity", "route": "/analysis/sensitivity"}
            ],
            "/analysis/monte_carlo": [
                {"label": "Analysis", "route": "/analysis"},
                {"label": "Monte Carlo", "route": "/analysis/monte_carlo"}
            ],
            "/analysis/scenarios": [
                {"label": "Analysis", "route": "/analysis"},
                {"label": "Scenarios", "route": "/analysis/scenarios"}
            ],
            "/validation": [
                {"label": "Validation", "route": "/validation"}
            ],
            "/export": [
                {"label": "Export & Reports", "route": "/export"}
            ]
        }
        
        self.current_breadcrumb = breadcrumb_map.get(route, [{"label": "Home", "route": "/dashboard"}])
        
        if self.modern_breadcrumb:
            self.modern_breadcrumb.items = self.current_breadcrumb
    
    def navigate_to_tab(self, tab: TabState):
        """Navigate to a specific tab with modern visual feedback."""
        if not self.ui_state.can_navigate_to_tab(tab):
            self.show_error("Please complete the required steps before accessing this tab")
            return
        self.set_loading(True, "Loading...")
        self.ui_state.navigate_to_tab(tab)
        tab_to_route = {
            TabState.DASHBOARD: "/dashboard",
            TabState.PROJECT_SETUP: "/setup", 
            TabState.LOCATION_COMPARISON: "/locations",
            TabState.FINANCIAL_MODEL: "/analysis/financial",
            TabState.SENSITIVITY: "/analysis/sensitivity",
            TabState.MONTE_CARLO: "/analysis/monte_carlo",
            TabState.SCENARIOS: "/analysis/scenarios",
            TabState.VALIDATION: "/validation",
            TabState.EXPORT: "/export"
        }
        route = tab_to_route.get(tab, "/setup")
        if self.modern_sidebar:
            route_to_nav_id = {
                "/dashboard": "dashboard",
                "/setup": "setup",
                "/locations": "locations",
                "/analysis": "analysis", 
                "/analysis/financial": "financial",
                "/analysis/sensitivity": "sensitivity",
                "/analysis/monte_carlo": "monte_carlo",
                "/analysis/scenarios": "scenarios",
                "/validation": "validation",
                "/export": "export"
            }
            nav_id = route_to_nav_id.get(route, "setup")
            self.modern_sidebar.select_item(nav_id)
        self._update_modern_breadcrumbs(route)
        # Update main content with real view
        if tab in self.views:
            view = self.views[tab]
            content = view.get_content()
            self.main_content.content = ft.Container(
                content=content,
                expand=True,
                bgcolor=self.modern_theme.get_background_colors()['primary']
            )
        else:
            self.main_content.content = ft.Container(
                content=ft.Text(
                    f"View for {tab.value} not implemented yet",
                    text_align=ft.TextAlign.CENTER,
                    size=16,
                    color=ft.Colors.GREY_600
                ),
                expand=True,
                bgcolor=self.modern_theme.get_background_colors()['primary']
            )
        self.page.update()
        self.logger.info(f"Navigated to tab: {tab.value}")


    
    def handle_data_change(self, data_type: str, data: Any):
        """Handle data changes from views."""
        if data_type == "client_profile":
            self.app_state.client_profile = data
        elif data_type == "project_assumptions":
            self.app_state.project_assumptions = data
        
        self.logger.info(f"Data changed: {data_type}")
    
    def handle_action_request(self, action: str, params: Dict[str, Any]):
        """Handle action requests from views with enhanced logging and correlation ID tracking."""
        
        # Extract or generate correlation ID from parameters
        correlation_id = params.get('correlation_id')
        if not correlation_id:
            correlation_id = CorrelationIDGenerator.generate()
            params['correlation_id'] = correlation_id
        
        # Set correlation ID in context for this request
        CorrelationIDManager.set_correlation_id(correlation_id)
        
        # Sanitize parameters for logging (remove sensitive data)
        sanitized_params = self._sanitize_action_params(params.copy())
        
        # Log action request with correlation ID and structured data
        self.logger.info(f"Action requested: {action}",
                        structured_data={
                            'action': action,
                            'correlation_id': correlation_id,
                            'params': sanitized_params,
                            'user_context': {
                                'current_tab': self.ui_state.current_tab.value if self.ui_state.current_tab else None,
                                'model_executed': self.ui_state.model_executed,
                                'has_client_profile': self.app_state.client_profile is not None,
                                'has_project_assumptions': self.app_state.project_assumptions is not None
                            },
                            'action_type': 'user_request'
                        })
        
        # Log Piano Mattei/SIMEST grant states if project assumptions exist
        if self.app_state.project_assumptions:
            self._log_grant_field_states(correlation_id)
        
        try:
            if action == "run_financial_model":
                self.page.run_task(self._run_financial_model)
            elif action == "run_comprehensive_analysis":
                self.page.run_task(self._run_comprehensive_analysis, params)
            elif action == "run_comprehensive_analysis_with_locations":
                self.page.run_task(self._run_comprehensive_analysis_with_locations, params)
            elif action == "run_location_comparison":
                self.page.run_task(self._run_location_comparison, params)
            elif action == "run_sensitivity_analysis":
                self.page.run_task(self._run_sensitivity_analysis, params)
            elif action == "run_monte_carlo":
                self.page.run_task(self._run_monte_carlo, params)
            elif action == "run_scenario_analysis":
                self.page.run_task(self._run_scenario_analysis, params)
            elif action == "quick_export":
                self.page.run_task(self._quick_export, params)
            elif action == "comprehensive_export":
                self.page.run_task(self._comprehensive_export, params)
            elif action == "save_configuration":
                self._save_configuration(params)
            elif action == "load_preset":
                self._load_preset()
            else:
                self.logger.warning(f"Unknown action requested: {action}",
                                  structured_data={
                                      'action': action,
                                      'available_actions': [
                                          'run_financial_model', 'run_comprehensive_analysis',
                                          'run_comprehensive_analysis_with_locations', 'run_location_comparison',
                                          'run_sensitivity_analysis', 'run_monte_carlo', 'run_scenario_analysis',
                                          'quick_export', 'comprehensive_export', 'save_configuration', 'load_preset'
                                      ],
                                      'action_type': 'unknown_action'
                                  })
                self.show_error(f"Unknown action: {action}")
        
        except Exception as e:
            # Enhanced error handling with correlation ID
            error_context = {
                'action': action,
                'correlation_id': correlation_id,
                'error': str(e),
                'error_type': type(e).__name__,
                'params': sanitized_params,
                'action_type': 'action_error'
            }
            
            self.logger.error(f"Error handling action {action}: {str(e)}",
                            structured_data=error_context)
            
            # Show user-friendly error with correlation ID for support
            error_message = f"Error: {str(e)}\n\nCorrelation ID: {correlation_id[:8]}... (for support)"
            self.show_error(error_message)
        
        finally:
            # Clear correlation ID from context after request handling
            CorrelationIDManager.clear_correlation_id()
    
    def _sanitize_action_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize action parameters for logging by removing sensitive data."""
        sanitized = {}
        sensitive_keys = {'password', 'token', 'key', 'secret', 'auth', 'credential'}
        
        for key, value in params.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                sanitized[key] = "***REDACTED***"
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_action_params(value)
            elif isinstance(value, (list, tuple)) and len(value) > 10:
                # Truncate large lists for logging
                sanitized[key] = f"[{len(value)} items - truncated for logging]"
            elif isinstance(value, str) and len(value) > 500:
                # Truncate very long strings
                sanitized[key] = f"{value[:100]}... [truncated - {len(value)} chars total]"
            else:
                sanitized[key] = value
        
        return sanitized
    
    def _log_grant_field_states(self, correlation_id: str):
        """Log Piano Mattei/SIMEST grant field states for debugging."""
        if not self.app_state.project_assumptions:
            return
        
        assumptions = self.app_state.project_assumptions
        
        # Extract grant field states
        grant_states = {
            'piano_mattei_grant': getattr(assumptions, 'piano_mattei_grant', None),
            'simest_facility_amount': getattr(assumptions, 'simest_facility_amount', None),
            'simest_grant_percentage': getattr(assumptions, 'simest_grant_percentage', None),
            'simest_max_grant_amount': getattr(assumptions, 'simest_max_grant_amount', None),
            'has_piano_mattei': hasattr(assumptions, 'piano_mattei_grant'),
            'has_simest_facility': hasattr(assumptions, 'simest_facility_amount'),
            'has_simest_percentage': hasattr(assumptions, 'simest_grant_percentage'),
            'has_simest_max': hasattr(assumptions, 'simest_max_grant_amount')
        }
        
        # Check for unified grant field (new system)
        if hasattr(assumptions, 'grant_amount'):
            grant_states['unified_grant_amount'] = getattr(assumptions, 'grant_amount', None)
            grant_states['has_unified_grant'] = True
        else:
            grant_states['has_unified_grant'] = False
        
        # Log grant field states
        self.logger.info("Piano Mattei/SIMEST grant field states logged for debugging",
                        structured_data={
                            'correlation_id': correlation_id,
                            'grant_states': grant_states,
                            'action_type': 'grant_field_diagnostic'
                        })
    
    async def _run_financial_model(self):
        """Run the financial model."""
        try:
            self.set_loading(True, "Running financial model...")

            # Generate correlation ID for this operation
            correlation_id = CorrelationIDGenerator.generate()
            CorrelationIDManager.set_correlation_id(correlation_id)

            async def progress_callback(progress: float, message: str):
                # Use simplified progress update
                await self.simple_progress.update_progress(progress, message)

            # Run financial model with correlation ID
            results = self.financial_service.run_financial_model(
                self.app_state.project_assumptions,
                progress_callback,
                correlation_id=correlation_id
            )
            
            # Update state
            self.app_state.financial_results = results
            self.ui_state.set_model_executed(True)
            
            # Update views with results
            self._update_views_with_results()
            
            self.set_loading(False)
            self.show_success("Financial model completed successfully!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error running financial model: {str(e)}")
    
    async def _run_comprehensive_analysis(self, params: Dict[str, Any]):
        """Run enhanced comprehensive analysis with all advanced features."""
        try:
            # Start progress operation
            await self.simple_progress.start_operation("Enhanced Financial Analysis")

            async def progress_callback(progress: float, message: str):
                """Simplified progress callback with async UI updates."""
                self.logger.info(f"Progress updated: {progress}% - {message}")
                
                # Use simplified progress update
                await self.simple_progress.update_progress(progress, message)

            # Step 1: Run enhanced financial model with ML predictions and Monte Carlo
            await progress_callback(10, "Running enhanced financial model with ML predictions...")

            project_data = {
                'client_profile': self.app_state.client_profile.to_dict(),
                'assumptions': self.app_state.project_assumptions.to_dict()
            }

            # Ensure enhanced service is available
            if not hasattr(self, 'enhanced_service') or not self.enhanced_service:
                self.logger.error("Enhanced service not available, using fallback analysis")
                # Run fallback analysis
                await progress_callback(50, "Running standard financial analysis...")
                
                standard_results = self.report_service.generate_comprehensive_report(
                    client_profile=self.app_state.client_profile,
                    assumptions=self.app_state.project_assumptions,
                    selected_locations=None,  # Fallback mode - use defaults
                    progress_callback=lambda p, m: asyncio.create_task(progress_callback(50 + p*0.5, m))
                )
                
                enhanced_results = standard_results['analysis_results'].get('financial', {})
                charts_3d = {}
            else:
                # Normal enhanced analysis
                enhanced_results = self.enhanced_service.run_enhanced_financial_model(
                    project_data=project_data,
                    include_ml_predictions=True,
                    include_monte_carlo=True
                )

                # CRITICAL FIX: Update app state with enhanced financial results immediately
                # This ensures the dashboard gets the financial data
                self.app_state.financial_results = enhanced_results
                
                # Step 2: Generate 3D charts
                await progress_callback(40, "Generating interactive 3D visualizations...")
                
                # Use actual project location for chart title
                project_location = self.app_state.project_assumptions.project_location
                base_project_name = self.app_state.client_profile.project_name or "Solar Project"
                project_name = f"{base_project_name} ({project_location})"

                charts_3d = self.enhanced_service.generate_advanced_charts(
                    financial_results=enhanced_results,
                    project_name=project_name
                )
                
                # Step 3: Auto-save with versioning
                await progress_callback(60, "Saving project with versioning...")
                
                if self.enhanced_service.persistence_service:
                    project_id = self.app_state.client_profile.get_clean_company_name()
                    version_id = self.enhanced_service.save_project_with_versioning(
                        project_id=project_id,
                        project_data={
                            'client_profile': self.app_state.client_profile.to_dict(),
                            'assumptions': self.app_state.project_assumptions.to_dict(),
                            'enhanced_results': enhanced_results,
                            'charts_3d': charts_3d
                        }
                    )
                    self.logger.info(f"Project saved with version: {version_id}")
                
                # Step 4: Run standard comprehensive report for exports
                await progress_callback(70, "Generating comprehensive reports...")
                
                try:
                    standard_results = self.report_service.generate_comprehensive_report(
                        client_profile=self.app_state.client_profile,
                        assumptions=self.app_state.project_assumptions,
                        selected_locations=None,  # Use defaults for comprehensive analysis
                        progress_callback=lambda p, m: asyncio.create_task(progress_callback(70 + p*0.25, m))
                    )
                except Exception as e:
                    self.logger.error(f"Error generating comprehensive report: {e}")
                    # Create fallback standard results structure
                    standard_results = {
                        'analysis_results': {
                            'validation': {},
                            'location_comparison': {},
                            'sensitivity': {},
                            'scenarios': {}
                        },
                        'generated_files': []
                    }
            
            # Step 5: Merge enhanced and standard results
            await progress_callback(95, "Finalizing enhanced results...")

            # Update state with enhanced results (financial_results already updated above)
            self.app_state.validation_results = standard_results['analysis_results'].get('validation')
            self.app_state.location_comparison_results = standard_results['analysis_results'].get('location_comparison')
            self.app_state.sensitivity_results = standard_results['analysis_results'].get('sensitivity')
            self.app_state.monte_carlo_results = enhanced_results.get('monte_carlo')
            self.app_state.scenario_results = standard_results['analysis_results'].get('scenarios')

            # Store enhanced features
            self.app_state.ml_predictions = enhanced_results.get('ml_predictions')
            self.app_state.charts_3d = charts_3d

            # Update UI state
            self.ui_state.set_model_executed(True)

            # CRITICAL FIX: Force refresh dashboard with all data at once
            # This ensures the dashboard shows the KPIs and financial data immediately
            if TabState.DASHBOARD in self.views and self.app_state.financial_results:
                dashboard_view = self.views[TabState.DASHBOARD]
                if hasattr(dashboard_view, 'force_refresh_with_data'):
                    dashboard_view.force_refresh_with_data(
                        financial_results=self.app_state.financial_results,
                        ml_predictions=enhanced_results.get('ml_predictions'),
                        charts_3d=charts_3d
                    )
                else:
                    dashboard_view.set_financial_results(self.app_state.financial_results)
                self.logger.info("Dashboard force refreshed with comprehensive analysis results")

            # CRITICAL FIX: Ensure Monte Carlo view gets updated with simulation results
            if TabState.MONTE_CARLO in self.views and self.app_state.monte_carlo_results:
                monte_carlo_view = self.views[TabState.MONTE_CARLO]
                if hasattr(monte_carlo_view, 'set_monte_carlo_results'):
                    monte_carlo_view.set_monte_carlo_results(self.app_state.monte_carlo_results)
                    self.logger.info("Monte Carlo view updated with simulation results")

            # Update views with enhanced data
            self._update_views_with_enhanced_results(enhanced_results, charts_3d)
            
            # CRITICAL FIX: Generate enhanced interactive dashboard with proper data structure
            await progress_callback(96, "Generating enhanced interactive dashboard...")
            try:
                # Create properly structured analysis results for interactive dashboard
                enhanced_analysis_results = {
                    'financial': enhanced_results,  # This is the key fix!
                    'validation': standard_results['analysis_results'].get('validation'),
                    'location_comparison': standard_results['analysis_results'].get('location_comparison'),
                    'sensitivity': standard_results['analysis_results'].get('sensitivity'),
                    'monte_carlo': enhanced_results.get('monte_carlo'),
                    'scenarios': standard_results['analysis_results'].get('scenarios'),
                    'enhanced_features': {
                        'ml_predictions': enhanced_results.get('ml_predictions'),
                        'charts_3d': charts_3d
                    }
                }

                # Generate enhanced interactive dashboard
                dashboard_file = self.export_service.export_interactive_dashboard(
                    client_profile=self.app_state.client_profile,
                    assumptions=self.app_state.project_assumptions,
                    analysis_results=enhanced_analysis_results
                )

                # Add to generated files
                standard_results['generated_files'].append(('Enhanced Interactive Dashboard', dashboard_file))
                self.logger.info("Enhanced interactive dashboard generated successfully")

            except Exception as e:
                self.logger.error(f"Failed to generate enhanced interactive dashboard: {e}")

            # Update export view with generated files
            if TabState.EXPORT in self.views:
                export_view = self.views[TabState.EXPORT]
                for file_type, file_path in standard_results['generated_files']:
                    export_view.add_generated_file(file_type, file_path)
            
            await progress_callback(100, "Enhanced analysis completed!")
            
            # Complete the operation
            await self.simple_progress.complete_operation("Analysis completed successfully!")
            
            # Enhanced success message
            ml_status = "✓" if enhanced_results.get('ml_predictions') else "✗"
            charts_3d_count = len([k for k in charts_3d.keys() if '3d' in k.lower()])
            cache_status = "✓" if self.enhanced_service.cache_service else "✗"
            
            success_msg = (f"🚀 Enhanced Analysis Completed!\n\n"
                          f"📊 Features Used:\n"
                          f"  {ml_status} ML Predictions: {len(enhanced_results.get('ml_predictions', {}).get('predictions', {}))}/3 models\n"
                          f"  ✓ 3D Charts: {charts_3d_count} interactive visualizations\n"
                          f"  {cache_status} Performance Cache: {'Enabled' if self.enhanced_service.cache_service else 'Disabled'}\n"
                          f"  ✓ Auto-Save: Project versioned and backed up\n"
                          f"  ✓ Error Recovery: Comprehensive fallback systems\n\n"
                          f"📁 Generated: {len(standard_results['generated_files'])} files")
            
            self.show_success(success_msg)
            
        except Exception as e:
            # Ensure progress dialog is hidden on error
            try:
                await self.simple_progress.dialog.hide()
            except:
                pass
            self.logger.error(f"Enhanced analysis failed: {e}")
            
            # Enhanced error recovery
            if self.enhanced_service.recovery_service:
                try:
                    fallback_results = self.enhanced_service._get_fallback_results()
                    self.app_state.financial_results = fallback_results
                    self.show_error(f"Analysis failed, using fallback data: {str(e)}")
                except:
                    self.show_error(f"Error in enhanced comprehensive analysis: {str(e)}")
            else:
                self.show_error(f"Error in enhanced comprehensive analysis: {str(e)}")
    
    async def _run_comprehensive_analysis_with_locations(self, params: Dict[str, Any]):
        """Run comprehensive analysis with location intelligence integration and enhanced logging."""
        
        # Extract correlation ID from parameters
        correlation_id = params.get('correlation_id', CorrelationIDGenerator.generate())
        CorrelationIDManager.set_correlation_id(correlation_id)
        
        # Create enhanced analysis logger for this session
        if ENHANCED_LOGGING_AVAILABLE:
            self.enhanced_analysis_logger = EnhancedAnalysisLogger(correlation_id)
        
        try:
            # Extract location parameters
            selected_locations = params.get('selected_locations', [])
            include_comparison = params.get('include_location_comparison', False)
            analysis_context = params.get('analysis_context', {})
            
            # Log analysis start with comprehensive context
            analysis_start_context = {
                'selected_locations': selected_locations,
                'location_count': len(selected_locations),
                'include_comparison': include_comparison,
                'analysis_context': analysis_context,
                'correlation_id': correlation_id,
                'action_type': 'location_analysis_start'
            }
            
            self.logger.info(f"Starting location-enhanced analysis for {len(selected_locations)} locations",
                           structured_data=analysis_start_context)
            
            # Log Piano Mattei/SIMEST grant states at analysis start
            if self.enhanced_analysis_logger:
                self.enhanced_analysis_logger.log_grant_field_state(
                    "analysis_start", 
                    self._get_current_grant_states()
                )
            
            # Start location-enhanced progress operation
            await self.simple_progress.start_operation("Location-Enhanced Financial Analysis")

            async def progress_callback(progress: float, message: str):
                """Enhanced progress callback with correlation ID and location context."""
                progress_context = {
                    'correlation_id': correlation_id,
                    'progress': progress,
                    'message': message,
                    'stage': 'location_analysis',
                    'action_type': 'progress_update'
                }
                
                self.logger.info(f"Location Analysis Progress: {progress}% - {message}",
                               structured_data=progress_context)
                
                # Log to enhanced analysis logger if available
                if self.enhanced_analysis_logger:
                    self.enhanced_analysis_logger.log_progress_update(progress, message, "location_analysis")
                
                await self.simple_progress.update_progress(progress, message)

            # Step 1: If multiple locations selected, run location comparison first
            if include_comparison and len(selected_locations) > 1:
                await progress_callback(10, f"Comparing {len(selected_locations)} locations...")
                
                # Log location comparison start
                self.logger.info("Starting multi-location comparison",
                               structured_data={
                                   'correlation_id': correlation_id,
                                   'locations': selected_locations,
                                   'comparison_type': 'multi_location',
                                   'action_type': 'location_comparison_start'
                               })
                
                try:
                    comparison_results = self.location_service.compare_locations(
                        self.app_state.project_assumptions,
                        selected_locations
                    )
                    
                    # Store comparison results
                    self.app_state.location_comparison_results = comparison_results
                    
                    # Log comparison results summary
                    self.logger.info("Location comparison completed",
                                   structured_data={
                                       'correlation_id': correlation_id,
                                       'comparison_results_available': comparison_results is not None,
                                       'results_keys': list(comparison_results.keys()) if comparison_results else [],
                                       'action_type': 'location_comparison_complete'
                                   })
                    
                    # Update location comparison view
                    if TabState.LOCATION_COMPARISON in self.views:
                        self.views[TabState.LOCATION_COMPARISON].set_comparison_results(comparison_results)
                    
                    # Get optimal location from comparison
                    optimal_location = comparison_results.get('analysis', {}).get('recommendations', {}).get('best_overall', {}).get('location')
                    if optimal_location:
                        await progress_callback(20, f"Identified optimal location: {optimal_location}")
                        # Focus analysis on optimal location
                        selected_locations = [optimal_location]
                        
                        self.logger.info("Optimal location identified from comparison",
                                       structured_data={
                                           'correlation_id': correlation_id,
                                           'optimal_location': optimal_location,
                                           'original_locations': params.get('selected_locations', []),
                                           'action_type': 'optimal_location_selected'
                                       })
                    else:
                        # Fallback to first selected location
                        selected_locations = [selected_locations[0]]
                        await progress_callback(20, f"Using primary location: {selected_locations[0]}")
                        
                        self.logger.warning("No optimal location identified, using first location",
                                          structured_data={
                                              'correlation_id': correlation_id,
                                              'fallback_location': selected_locations[0],
                                              'action_type': 'location_fallback'
                                          })
                    
                except Exception as e:
                    self.logger.error("Location comparison failed, proceeding with single location",
                                    structured_data={
                                        'correlation_id': correlation_id,
                                        'error': str(e),
                                        'error_type': type(e).__name__,
                                        'fallback_location': selected_locations[0] if selected_locations else 'None',
                                        'action_type': 'location_comparison_error'
                                    })
                    selected_locations = [selected_locations[0]]
                    await progress_callback(20, f"Proceeding with single location analysis")
            else:
                location_name = selected_locations[0] if selected_locations else 'Default'
                await progress_callback(15, f"Single location analysis: {location_name}")
                
                self.logger.info("Single location analysis mode",
                               structured_data={
                                   'correlation_id': correlation_id,
                                   'location': location_name,
                                   'analysis_mode': 'single_location',
                                   'action_type': 'single_location_analysis'
                               })

            # Step 2: Update project assumptions with optimal location data
            if selected_locations:
                primary_location = selected_locations[0]
                await progress_callback(25, f"Configuring project for {primary_location}...")

                # Log location configuration
                self.logger.info("Configuring project for primary location",
                               structured_data={
                                   'correlation_id': correlation_id,
                                   'primary_location': primary_location,
                                   'selected_locations': selected_locations,
                                   'action_type': 'location_configuration'
                               })

                # Log grant states before location-specific adjustments
                if self.enhanced_analysis_logger:
                    self.enhanced_analysis_logger.log_grant_field_state(
                        "pre_location_config",
                        self._get_current_grant_states()
                    )

                # CRITICAL FIX: Update project_location field for SIMEST validation
                old_project_location = self.app_state.project_assumptions.project_location
                self.app_state.project_assumptions.project_location = primary_location

                # Log the location update for debugging
                self.logger.info("Updated project location for SIMEST validation",
                               structured_data={
                                   'correlation_id': correlation_id,
                                   'old_location': old_project_location,
                                   'new_location': primary_location,
                                   'selected_locations': selected_locations,
                                   'action_type': 'project_location_update'
                               })

                # Re-validate project assumptions after location update
                self.app_state.project_assumptions.validate_all()

                self.logger.info(f"Primary analysis location set to: {primary_location}")
            
            # Step 3: Run enhanced comprehensive analysis
            await progress_callback(30, "Running enhanced financial model with location optimization...")
            
            # Prepare enhanced project data with location context
            project_data = {
                'client_profile': self.app_state.client_profile.to_dict(),
                'assumptions': self.app_state.project_assumptions.to_dict(),
                'location_context': {
                    'primary_location': selected_locations[0] if selected_locations else None,
                    'selected_locations': selected_locations,
                    'comparison_enabled': include_comparison,
                    'analysis_context': analysis_context
                },
                'correlation_id': correlation_id  # Pass correlation ID to services
            }

            # Log enhanced service call
            if self.enhanced_analysis_logger:
                self.enhanced_analysis_logger.log_service_call(
                    "enhanced_integration_service",
                    "run_enhanced_financial_model",
                    {
                        'include_ml_predictions': True,
                        'include_monte_carlo': True,
                        'location_context': project_data['location_context']
                    }
                )

            # Run enhanced financial model with location intelligence
            if hasattr(self, 'enhanced_service') and self.enhanced_service:
                self.logger.info("Running enhanced financial model with location intelligence",
                               structured_data={
                                   'correlation_id': correlation_id,
                                   'service_available': True,
                                   'ml_predictions_enabled': True,
                                   'monte_carlo_enabled': True,
                                   'action_type': 'enhanced_model_start'
                               })
                
                enhanced_results = self.enhanced_service.run_enhanced_financial_model(
                    project_data=project_data,
                    include_ml_predictions=True,
                    include_monte_carlo=True
                )
                
                # Update app state
                self.app_state.financial_results = enhanced_results
                
                # Log enhanced results summary
                self.logger.info("Enhanced financial model completed",
                               structured_data={
                                   'correlation_id': correlation_id,
                                   'results_available': enhanced_results is not None,
                                   'ml_predictions_included': 'ml_predictions' in enhanced_results if enhanced_results else False,
                                   'monte_carlo_included': 'monte_carlo' in enhanced_results if enhanced_results else False,
                                   'action_type': 'enhanced_model_complete'
                               })
                
                # Step 4: Generate 3D charts with location context
                await progress_callback(60, "Generating location-aware 3D visualizations...")
                
                # Use actual project location for chart title, not comparison locations
                project_location = self.app_state.project_assumptions.project_location
                base_project_name = self.app_state.client_profile.project_name or 'Solar Project'

                if len(selected_locations) > 1:
                    project_name = f"{base_project_name} ({project_location}) - Multi-Location Analysis"
                else:
                    project_name = f"{base_project_name} ({project_location})"

                self.logger.info("Generating 3D charts with location context",
                               structured_data={
                                   'correlation_id': correlation_id,
                                   'project_name': project_name,
                                   'project_location': project_location,
                                   'multi_location': len(selected_locations) > 1,
                                   'action_type': '3d_charts_generation'
                               })

                charts_3d = self.enhanced_service.generate_advanced_charts(
                    financial_results=enhanced_results,
                    project_name=project_name
                )
                
                # Step 5: Auto-save with location data
                await progress_callback(75, "Saving project with location intelligence...")
                
                if self.enhanced_service.persistence_service:
                    project_id = self.app_state.client_profile.get_clean_company_name()
                    
                    save_data = {
                        'client_profile': self.app_state.client_profile.to_dict(),
                        'assumptions': self.app_state.project_assumptions.to_dict(),
                        'enhanced_results': enhanced_results,
                        'location_data': {
                            'selected_locations': selected_locations,
                            'comparison_results': self.app_state.location_comparison_results,
                            'primary_location': selected_locations[0] if selected_locations else None
                        },
                        'charts_3d': charts_3d,
                        'correlation_id': correlation_id
                    }
                    
                    version_id = self.enhanced_service.save_project_with_versioning(
                        project_id=project_id,
                        project_data=save_data
                    )
                    
                    self.logger.info("Location-enhanced project saved with versioning",
                                   structured_data={
                                       'correlation_id': correlation_id,
                                       'project_id': project_id,
                                       'version_id': version_id,
                                       'action_type': 'project_save_complete'
                                   })
            else:
                # Fallback to standard analysis
                await progress_callback(50, "Running standard analysis (enhanced features unavailable)...")
                
                self.logger.warning("Enhanced service not available, falling back to standard analysis",
                                  structured_data={
                                      'correlation_id': correlation_id,
                                      'fallback_reason': 'enhanced_service_unavailable',
                                      'action_type': 'analysis_fallback'
                                  })
                
                # Log grant states before fallback analysis
                if self.enhanced_analysis_logger:
                    self.enhanced_analysis_logger.log_grant_field_state(
                        "fallback_analysis", 
                        self._get_current_grant_states()
                    )
                
                async def fallback_progress_callback(p, m):
                    await progress_callback(50 + p*0.3, m)
                
                # Get correlation ID from context or generate new one
                correlation_id = CorrelationIDManager.get_correlation_id() or CorrelationIDGenerator.generate()

                results = self.financial_service.run_financial_model(
                    self.app_state.project_assumptions,
                    fallback_progress_callback,
                    correlation_id=correlation_id
                )
                
                enhanced_results = results
                charts_3d = {}
                self.app_state.financial_results = results

            # Step 6: Generate comprehensive reports
            await progress_callback(85, "Generating location-enhanced reports...")
            
            try:
                standard_results = self.report_service.generate_comprehensive_report(
                    client_profile=self.app_state.client_profile,
                    assumptions=self.app_state.project_assumptions,
                    selected_locations=selected_locations,
                    progress_callback=lambda p, m: asyncio.create_task(progress_callback(85 + p*0.1, m))
                )
            except Exception as e:
                self.logger.error(f"Error generating location-enhanced report: {e}")
                # Create fallback standard results structure
                standard_results = {
                    'analysis_results': {
                        'validation': {},
                        'location_comparison': {},
                        'sensitivity': {},
                        'scenarios': {}
                    },
                    'generated_files': []
                }
            
            # Update app state with all results
            self.app_state.validation_results = standard_results['analysis_results'].get('validation')
            self.app_state.sensitivity_results = standard_results['analysis_results'].get('sensitivity')
            self.app_state.monte_carlo_results = enhanced_results.get('monte_carlo')
            self.app_state.scenario_results = standard_results['analysis_results'].get('scenarios')
            
            # Store enhanced features
            self.app_state.ml_predictions = enhanced_results.get('ml_predictions')
            self.app_state.charts_3d = charts_3d
            
            # Update UI state
            self.ui_state.set_model_executed(True)
            
            # Step 7: Update all views with location-enhanced results
            await progress_callback(95, "Updating dashboard with location intelligence...")
            
            # Force refresh dashboard with comprehensive data
            if TabState.DASHBOARD in self.views and self.app_state.financial_results:
                dashboard_view = self.views[TabState.DASHBOARD]
                if hasattr(dashboard_view, 'force_refresh_with_data'):
                    dashboard_view.force_refresh_with_data(
                        financial_results=self.app_state.financial_results,
                        ml_predictions=enhanced_results.get('ml_predictions'),
                        charts_3d=charts_3d
                    )
                else:
                    dashboard_view.set_financial_results(self.app_state.financial_results)
                self.logger.info("Dashboard updated with location-enhanced analysis results")

            # Update views with enhanced data
            self._update_views_with_enhanced_results(enhanced_results, charts_3d)
            
            # Update export view
            if TabState.EXPORT in self.views:
                export_view = self.views[TabState.EXPORT]
                for file_type, file_path in standard_results['generated_files']:
                    export_view.add_generated_file(file_type, file_path)
            
            await progress_callback(100, "Location-enhanced analysis completed!")
            
            # Add delay to ensure progress is visible
            await asyncio.sleep(1)
            
            # Complete the location analysis operation
            await self.simple_progress.complete_operation("Location analysis completed successfully!")
            
            # Log analysis completion with comprehensive summary
            if self.enhanced_analysis_logger:
                results_summary = {
                    'location_count': len(selected_locations),
                    'primary_location': selected_locations[0] if selected_locations else None,
                    'comparison_enabled': include_comparison,
                    'enhanced_features_used': {
                        'ml_predictions': enhanced_results.get('ml_predictions') is not None,
                        '3d_charts': len(charts_3d) > 0 if charts_3d else False,
                        'monte_carlo': enhanced_results.get('monte_carlo') is not None
                    },
                    'files_generated': len(standard_results.get('generated_files', [])),
                    'correlation_id': correlation_id
                }
                self.enhanced_analysis_logger.log_analysis_completion(results_summary)
            
            # Enhanced success message with location context
            location_info = f"{len(selected_locations)} location(s)" if selected_locations else "default configuration"
            comparison_info = " with comparison analysis" if include_comparison else ""
            primary_location_info = f" (Optimal: {selected_locations[0]})" if selected_locations else ""
            
            ml_status = "✓" if enhanced_results.get('ml_predictions') else "✗"
            charts_3d_count = len([k for k in charts_3d.keys() if '3d' in k.lower()]) if charts_3d else 0
            
            success_msg = (f"🌍 Location-Enhanced Analysis Completed!\n\n"
                          f"📍 Location Intelligence:\n"
                          f"  ✓ Analyzed: {location_info}{comparison_info}{primary_location_info}\n"
                          f"  ✓ Location Data: Integrated into financial model\n"
                          f"  {'✓' if include_comparison else '○'} Comparison: {'Multi-location optimization' if include_comparison else 'Single location focus'}\n\n"
                          f"🚀 Enhanced Features:\n"
                          f"  {ml_status} ML Predictions: {len(enhanced_results.get('ml_predictions', {}).get('predictions', {})) if enhanced_results.get('ml_predictions') else 0}/3 models\n"
                          f"  ✓ 3D Charts: {charts_3d_count} location-aware visualizations\n"
                          f"  ✓ Auto-Save: Project with location data saved\n\n"
                          f"📁 Generated: {len(standard_results.get('generated_files', []))}\n"
                          f"🔍 Correlation ID: {correlation_id[:8]}... (for support)")
            
            self.show_success(success_msg)
            
        except Exception as e:
            # Ensure progress dialog is hidden on error
            try:
                await self.simple_progress.dialog.hide()
            except:
                pass
            
            # Enhanced error logging with correlation ID
            error_context = {
                'correlation_id': correlation_id,
                'error': str(e),
                'error_type': type(e).__name__,
                'selected_locations': selected_locations,
                'include_comparison': include_comparison,
                'analysis_context': analysis_context,
                'action_type': 'location_analysis_error'
            }
            
            self.logger.error("Location-enhanced analysis failed",
                            structured_data=error_context)
            
            # Log error with enhanced analysis logger
            if self.enhanced_analysis_logger:
                self.enhanced_analysis_logger.log_error_with_context(e, error_context)
            
            # Show user-friendly error with correlation ID
            error_message = f"Error in location-enhanced analysis: {str(e)}\n\nCorrelation ID: {correlation_id[:8]}... (for support)"
            self.show_error(error_message)
        
        finally:
            # Clear correlation ID from context
            CorrelationIDManager.clear_correlation_id()
    
    async def _run_location_comparison(self, params: Dict[str, Any]):
        """Run location comparison analysis."""
        try:
            self.set_loading(True, "Running location comparison...")
            
            locations = params.get('locations', [])
            results = self.location_service.compare_locations(
                self.app_state.project_assumptions,
                locations
            )
            
            self.app_state.location_comparison_results = results
            
            # Update location comparison view
            if TabState.LOCATION_COMPARISON in self.views:
                self.views[TabState.LOCATION_COMPARISON].set_comparison_results(results)
            
            self.set_loading(False)
            self.show_success("Location comparison completed!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in location comparison: {str(e)}")
    
    async def _run_sensitivity_analysis(self, params: Dict[str, Any]):
        """Run sensitivity analysis."""
        try:
            self.set_loading(True, "Running sensitivity analysis...")
            
            variables = params.get('variables', [])
            results = self.financial_service.run_sensitivity_analysis(
                self.app_state.project_assumptions,
                variables
            )
            
            self.app_state.sensitivity_results = results
            
            # Update sensitivity view
            if TabState.SENSITIVITY in self.views:
                self.views[TabState.SENSITIVITY].set_sensitivity_results(results)
            
            self.set_loading(False)
            self.show_success("Sensitivity analysis completed!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in sensitivity analysis: {str(e)}")
    
    async def _run_monte_carlo(self, params: Dict[str, Any]):
        """Run Monte Carlo simulation."""
        try:
            self.set_loading(True, "Running Monte Carlo simulation...")
            
            n_simulations = params.get('n_simulations', 1000)
            
            async def progress_callback(progress: float, message: str):
                # Use simplified progress update
                await self.simple_progress.update_progress(progress, message)
                
                # Update Monte Carlo view progress
                try:
                    if TabState.MONTE_CARLO in self.views:
                        await self.views[TabState.MONTE_CARLO].set_monte_carlo_results({
                            'progress': progress,
                            'in_progress': True
                        })
                except Exception as e:
                    self.logger.error(f"Error updating Monte Carlo view: {e}")
            
            results = self.financial_service.run_monte_carlo_simulation(
                self.app_state.project_assumptions,
                n_simulations,
                progress_callback
            )
            
            self.app_state.monte_carlo_results = results
            
            # Update Monte Carlo view
            if TabState.MONTE_CARLO in self.views:
                self.views[TabState.MONTE_CARLO].set_monte_carlo_results(results)
            
            self.set_loading(False)
            self.show_success(f"Monte Carlo simulation completed with {n_simulations} simulations!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in Monte Carlo simulation: {str(e)}")
    
    async def _run_scenario_analysis(self, params: Dict[str, Any]):
        """Run scenario analysis."""
        try:
            self.set_loading(True, "Running scenario analysis...")

            scenarios = params.get('scenarios', ["Base", "Optimistic", "Pessimistic"])

            async def progress_callback(progress: float, message: str):
                # Use simplified progress update
                await self.simple_progress.update_progress(progress, message)

            results = self.financial_service.run_scenario_analysis(
                self.app_state.project_assumptions,
                scenarios,
                progress_callback
            )

            self.app_state.scenario_results = results

            # Update scenarios view
            if TabState.SCENARIOS in self.views:
                self.views[TabState.SCENARIOS].set_scenario_results(results)

            self.set_loading(False)
            self.show_success(f"Scenario analysis completed for {len(scenarios)} scenarios!")

        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in scenario analysis: {str(e)}")
    
    async def _quick_export(self, params: Dict[str, Any]):
        """Perform quick export with selected formats."""
        try:
            self.set_loading(True, "Exporting data...")

            formats = params.get('formats', ['Excel'])
            generated_files = []

            # Ensure we have financial results
            if not self.app_state.financial_results:
                self.show_error("No financial results available. Please run analysis first.")
                self.set_loading(False)
                return

            # Create output directory
            output_dir = self.export_service.file_utils.create_timestamped_output_directory(
                self.app_state.client_profile
            )

            total_formats = len(formats)

            for i, format_name in enumerate(formats):
                progress = 20 + (i / total_formats) * 60  # 20-80% for exports

                try:
                    if format_name == 'Excel':
                        self.set_loading(True, f"Exporting Excel report... ({i+1}/{total_formats})")
                        excel_file = self.export_service.export_excel_report(
                            self.app_state.client_profile,
                            self.app_state.project_assumptions,
                            self.app_state.financial_results,
                            output_dir=output_dir
                        )
                        generated_files.append(('Excel Report', excel_file))

                    elif format_name == 'DOCX':
                        self.set_loading(True, f"Exporting DOCX report... ({i+1}/{total_formats})")
                        docx_file = self.export_service.export_docx_report(
                            self.app_state.client_profile,
                            self.app_state.project_assumptions,
                            self.app_state.financial_results,
                            output_dir=output_dir
                        )
                        generated_files.append(('DOCX Report', docx_file))

                    elif format_name == 'HTML':
                        self.set_loading(True, f"Exporting HTML report... ({i+1}/{total_formats})")
                        html_file = self.export_service.export_html_report(
                            self.app_state.client_profile,
                            self.app_state.project_assumptions,
                            self.app_state.financial_results,
                            output_dir=output_dir
                        )
                        generated_files.append(('HTML Report', html_file))

                    elif format_name == 'JSON':
                        self.set_loading(True, f"Exporting JSON data... ({i+1}/{total_formats})")
                        json_file = self.export_service.export_json_data(
                            self.app_state.client_profile,
                            self.app_state.project_assumptions,
                            self.app_state.financial_results,
                            output_dir=output_dir
                        )
                        generated_files.append(('Raw Data (JSON)', json_file))

                    elif format_name == 'PDF':
                        self.set_loading(True, f"Exporting PDF report... ({i+1}/{total_formats})")
                        try:
                            pdf_file = self.export_service.export_pdf_report(
                                self.app_state.client_profile,
                                self.app_state.project_assumptions,
                                self.app_state.financial_results,
                                output_dir=output_dir
                            )
                            generated_files.append(('PDF Report', pdf_file))
                        except ImportError as e:
                            self.logger.warning(f"PDF export not available: {e}")
                            self.show_warning("PDF export requires reportlab. Install with: pip install reportlab")

                except Exception as e:
                    self.logger.error(f"Error exporting {format_name}: {e}")
                    self.show_warning(f"Failed to export {format_name}: {str(e)}")

            # Update export view
            if TabState.EXPORT in self.views:
                export_view = self.views[TabState.EXPORT]
                for file_type, file_path in generated_files:
                    export_view.add_generated_file(file_type, file_path)

            self.set_loading(False)

            if generated_files:
                self.show_success(f"Export completed! Generated {len(generated_files)} files.")
                self.logger.info(f"Quick export completed: {[f[0] for f in generated_files]}")
            else:
                self.show_error("No files were generated. Please check the logs for errors.")

        except Exception as e:
            self.set_loading(False)
            self.logger.error(f"Error during quick export: {e}")
            self.show_error(f"Error during export: {str(e)}")
    
    async def _comprehensive_export(self, params: Dict[str, Any]):
        """Perform comprehensive export with full analysis."""
        try:
            self.set_loading(True, "Starting comprehensive analysis and export...")

            formats = params.get('formats', ['Excel', 'DOCX', 'HTML'])
            include_all_analysis = params.get('include_all_analysis', True)

            # Ensure we have the necessary data
            if not self.app_state.client_profile or not self.app_state.project_assumptions:
                self.show_error("Please complete project setup before exporting.")
                self.set_loading(False)
                return

            # Convert format names to lowercase for report service
            export_formats = []
            for fmt in formats:
                if fmt == 'Excel':
                    export_formats.append('excel')
                elif fmt == 'DOCX':
                    export_formats.append('docx')
                elif fmt == 'HTML':
                    export_formats.append('html')
                elif fmt == 'JSON':
                    export_formats.append('json')
                elif fmt == 'PDF':
                    export_formats.append('pdf')

            # Add dashboard and PowerPoint if comprehensive
            if include_all_analysis:
                export_formats.extend(['dashboard', 'pptx'])

            self.logger.info(f"Starting comprehensive export with formats: {export_formats}")

            # Use report service for comprehensive analysis and export
            def progress_callback(progress: float, message: str):
                self.set_loading(True, f"{message} ({progress:.0f}%)")

            try:
                results = self.report_service.generate_comprehensive_report(
                    client_profile=self.app_state.client_profile,
                    assumptions=self.app_state.project_assumptions,
                    include_location_comparison=include_all_analysis,
                    include_sensitivity=include_all_analysis,
                    include_monte_carlo=include_all_analysis,
                    include_scenarios=include_all_analysis,
                    export_formats=export_formats,
                    selected_locations=None,  # Use defaults for export
                    progress_callback=progress_callback
                )

                # Update export view with generated files
                if TabState.EXPORT in self.views and results.get('generated_files'):
                    export_view = self.views[TabState.EXPORT]
                    for file_type, file_path in results['generated_files']:
                        export_view.add_generated_file(file_type, file_path)

                # Store results in app state
                if 'analysis_results' in results:
                    if 'financial' in results['analysis_results']:
                        self.app_state.financial_results = results['analysis_results']['financial']
                    if 'monte_carlo' in results['analysis_results']:
                        self.app_state.monte_carlo_results = results['analysis_results']['monte_carlo']
                    if 'sensitivity' in results['analysis_results']:
                        self.app_state.sensitivity_results = results['analysis_results']['sensitivity']
                    if 'location_comparison' in results['analysis_results']:
                        self.app_state.location_comparison_results = results['analysis_results']['location_comparison']

                self.set_loading(False)

                generated_count = len(results.get('generated_files', []))
                if generated_count > 0:
                    self.show_success(f"Comprehensive export completed! Generated {generated_count} files.")
                    self.logger.info(f"Comprehensive export completed: {[f[0] for f in results.get('generated_files', [])]}")

                    # Show output directory
                    output_dir = results.get('output_directory', {})
                    if output_dir and 'base_dir' in output_dir:
                        self.logger.info(f"Files saved to: {output_dir['base_dir']}")
                else:
                    self.show_warning("Comprehensive export completed but no files were generated.")

            except Exception as e:
                self.logger.error(f"Error in comprehensive report generation: {e}")
                self.show_error(f"Error generating comprehensive report: {str(e)}")
                # Fallback to quick export
                self.logger.info("Falling back to quick export...")
                await self._quick_export(params)

        except Exception as e:
            self.set_loading(False)
            self.logger.error(f"Error during comprehensive export: {e}")
            self.show_error(f"Error during comprehensive export: {str(e)}")
    
    def _save_configuration(self, params: Dict[str, Any]):
        """Save current configuration."""
        try:
            # Save configuration logic here
            self.show_success("Configuration saved successfully!")
        except Exception as e:
            self.show_error(f"Error saving configuration: {str(e)}")
    
    def _load_preset(self):
        """Load preset configuration."""
        try:
            # Load preset logic here
            self.show_success("Preset loaded successfully!")
        except Exception as e:
            self.show_error(f"Error loading preset: {str(e)}")
    
    def _update_views_with_results(self):
        """Update all views with current results."""
        # Update dashboard
        if TabState.DASHBOARD in self.views and self.app_state.financial_results:
            self.views[TabState.DASHBOARD].set_financial_results(self.app_state.financial_results)
        
        # Update financial model view
        if TabState.FINANCIAL_MODEL in self.views and self.app_state.financial_results:
            self.views[TabState.FINANCIAL_MODEL].set_financial_results(self.app_state.financial_results)
        
        # Update validation view
        if TabState.VALIDATION in self.views and self.app_state.validation_results:
            self.views[TabState.VALIDATION].set_validation_results(self.app_state.validation_results)
    
    def _update_views_with_enhanced_results(self, enhanced_results: Dict[str, Any], charts_3d: Dict[str, str]):
        """Update all views with enhanced results including ML predictions and 3D charts."""

        # Update all standard views first
        self._update_views_with_results()

        # Update dashboard with ML predictions and 3D charts
        if TabState.DASHBOARD in self.views:
            dashboard_view = self.views[TabState.DASHBOARD]

            # Ensure financial results are set first
            if enhanced_results and not dashboard_view.has_data():
                dashboard_view.set_financial_results(enhanced_results)
                self.logger.info("Dashboard financial results set in enhanced update")

            # Add ML predictions
            if hasattr(dashboard_view, 'set_ml_predictions') and enhanced_results.get('ml_predictions'):
                dashboard_view.set_ml_predictions(enhanced_results['ml_predictions'])
                self.logger.info("Dashboard ML predictions updated")

            # Add 3D charts
            if hasattr(dashboard_view, 'set_3d_charts') and charts_3d:
                dashboard_view.set_3d_charts(charts_3d)
                self.logger.info("Dashboard 3D charts updated")
        
        # Update financial model view with enhanced data
        if TabState.FINANCIAL_MODEL in self.views:
            financial_view = self.views[TabState.FINANCIAL_MODEL]
            if hasattr(financial_view, 'set_enhanced_results'):
                financial_view.set_enhanced_results(enhanced_results)
        
        # Update Monte Carlo view with enhanced simulation
        if TabState.MONTE_CARLO in self.views and enhanced_results.get('monte_carlo'):
            mc_view = self.views[TabState.MONTE_CARLO]
            if hasattr(mc_view, 'set_monte_carlo_results'):
                mc_view.set_monte_carlo_results(enhanced_results['monte_carlo'])
        
        # Update location comparison view
        if TabState.LOCATION_COMPARISON in self.views and self.app_state.location_comparison_results:
            location_view = self.views[TabState.LOCATION_COMPARISON]
            if hasattr(location_view, 'set_comparison_results'):
                location_view.set_comparison_results(self.app_state.location_comparison_results)
        
        self.logger.info("Views updated with enhanced results including ML predictions and 3D charts")
    
    async def _safe_progress_update(self, progress: float, message: str):
        """Simplified async progress update using the new progress service."""
        try:
            await self.simple_progress.update_progress(progress, message)
            self.logger.debug(f"Progress updated: {progress}% - {message}")
        except Exception as e:
            self.logger.error(f"Error in progress update: {e}")
            # Fallback to logging
            print(f"PROGRESS LOG: {progress}% - {message}")
    
    def update_status(self, status: str, message: str):
        """Update status bar."""
        if self.status_bar:
            status_text = self.status_bar.content.controls[1].controls[0]
            status_text.value = message
            self.page.update()
    
    def set_loading(self, loading: bool, message: str = ""):
        """Show or hide loading message in main content area."""
        if loading:
            self.main_content.content = ft.Container(
                content=ft.Text(
                    message or "Loading...",
                    text_align=ft.TextAlign.CENTER,
                    size=16,
                    color=self.modern_theme.get_text_colors()['secondary']
                ),
                expand=True,
                padding=self.modern_theme.tokens.spacing['xxl'],
                bgcolor=self.modern_theme.get_background_colors()['primary']
            )
        else:
            # Actual content will be set by navigate_to_tab
            pass
        self.page.update()
    
    async def update_progress(self, progress: float, message: str = ""):
        """Update progress using async UI updates."""
        self.ui_state.update_progress(progress, message)
        
        try:
            if self.progress_bar:
                self.progress_bar.value = progress / 100.0
                self.page.update()  # Use synchronous update instead of update_async

            if message:
                self.update_status("loading", f"{message} ({progress:.0f}%)")

        except Exception as e:
            self.logger.error(f"Error in progress UI update: {e}")
    
    def _get_current_grant_states(self) -> Dict[str, Any]:
        """Get current Piano Mattei/SIMEST grant field states for logging."""
        if not self.app_state.project_assumptions:
            return {'no_assumptions': True}
        
        assumptions = self.app_state.project_assumptions
        
        grant_states = {
            'piano_mattei_grant': getattr(assumptions, 'piano_mattei_grant', None),
            'simest_facility_amount': getattr(assumptions, 'simest_facility_amount', None),
            'simest_grant_percentage': getattr(assumptions, 'simest_grant_percentage', None),
            'simest_max_grant_amount': getattr(assumptions, 'simest_max_grant_amount', None),
            'unified_grant_amount': getattr(assumptions, 'grant_amount', None),
            'has_piano_mattei': hasattr(assumptions, 'piano_mattei_grant'),
            'has_simest_facility': hasattr(assumptions, 'simest_facility_amount'),
            'has_simest_percentage': hasattr(assumptions, 'simest_grant_percentage'),
            'has_simest_max': hasattr(assumptions, 'simest_max_grant_amount'),
            'has_unified_grant': hasattr(assumptions, 'grant_amount')
        }
        
        return grant_states
    
    def show_error(self, error: str):
        """Show error message with enhanced logging."""
        correlation_id = CorrelationIDManager.get_correlation_id()
        
        # Log error with correlation ID
        self.logger.error("User error message displayed",
                        structured_data={
                            'correlation_id': correlation_id,
                            'error_message': error,
                            'action_type': 'user_error_display'
                        })
        
        self.ui_state.set_error(error)
        self.update_status("error", f"Error: {error}")
        
        # Show snack bar
        snack_bar = ft.SnackBar(
            content=ft.Text(error),
            bgcolor=ft.Colors.RED_400,
            open=True
        )
        self.page.snack_bar = snack_bar
        self.page.update()
    
    def show_success(self, message: str):
        """Show success message with enhanced logging."""
        correlation_id = CorrelationIDManager.get_correlation_id()
        
        # Log success with correlation ID
        self.logger.info("User success message displayed",
                       structured_data={
                           'correlation_id': correlation_id,
                           'success_message': message,
                           'action_type': 'user_success_display'
                       })
        
        self.ui_state.clear_error()
        self.update_status("success", message)
        
        # Show snack bar
        snack_bar = ft.SnackBar(
            content=ft.Text(message),
            bgcolor=ft.Colors.GREEN_400,
            open=True
        )
        self.page.snack_bar = snack_bar
        self.page.update()
    
    def show_warning(self, message: str):
        """Show warning message with enhanced logging."""
        correlation_id = CorrelationIDManager.get_correlation_id()
        
        # Log warning with correlation ID
        self.logger.warning("User warning message displayed",
                          structured_data={
                              'correlation_id': correlation_id,
                              'warning_message': message,
                              'action_type': 'user_warning_display'
                          })
        
        # Show snack bar
        snack_bar = ft.SnackBar(
            content=ft.Text(message),
            bgcolor=ft.Colors.ORANGE_400,
            open=True
        )
        self.page.snack_bar = snack_bar
        self.page.update()

    async def _periodic_health_check(self):
        """Perform periodic health checks and update status indicator."""
        import asyncio

        while True:
            try:
                # Wait 30 seconds between checks
                await asyncio.sleep(30)

                # Get health status
                health_status = get_health_status()

                # Update health indicator based on system status
                system_status = health_status.get('system_status')

                if system_status and hasattr(system_status, 'value'):
                    status_value = system_status.value
                else:
                    status_value = str(system_status).lower() if system_status else 'unknown'

                if status_value == 'healthy':
                    self.health_indicator.color = ft.Colors.GREEN
                    self.health_indicator.name = ft.Icons.HEALTH_AND_SAFETY
                    tooltip = f"System Health: All {health_status.get('total_services', 0)} services operational"
                elif status_value == 'warning':
                    self.health_indicator.color = ft.Colors.ORANGE
                    self.health_indicator.name = ft.Icons.WARNING
                    tooltip = f"System Health: {health_status.get('warning_services', 0)} services have warnings"
                elif status_value == 'critical':
                    self.health_indicator.color = ft.Colors.RED
                    self.health_indicator.name = ft.Icons.ERROR
                    tooltip = f"System Health: {health_status.get('critical_services', 0)} services critical"
                else:
                    self.health_indicator.color = ft.Colors.GREY
                    self.health_indicator.name = ft.Icons.HELP
                    tooltip = "System Health: Status unknown"

                self.health_indicator.tooltip = tooltip

                # Update UI if page is still active
                if hasattr(self.page, 'update'):
                    self.health_indicator.update()

                self.logger.debug(f"Health check completed: {status_value}")

            except Exception as e:
                self.logger.error(f"Health check failed: {str(e)}")
                # Set error state
                if hasattr(self, 'health_indicator'):
                    self.health_indicator.color = ft.Colors.RED
                    self.health_indicator.name = ft.Icons.ERROR
                    self.health_indicator.tooltip = f"Health check failed: {str(e)}"
                    try:
                        self.health_indicator.update()
                    except:
                        pass
