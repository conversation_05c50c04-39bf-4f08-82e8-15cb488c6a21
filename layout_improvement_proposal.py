"""
Layout Improvement Proposal for Hiel RnE Modeler
===============================================

Based on the screenshot analysis, here are the recommended improvements:

1. Simplified Breadcrumb Navigation
2. More Compact Sidebar
3. Better Header Space Utilization
4. Improved Content Organization
"""

import flet as ft
from typing import List, Dict, Any, Optional

def create_improved_header_layout() -> ft.Container:
    """Create improved header layout with better space utilization."""
    return ft.Container(
        content=ft.Row([
            # Left: App branding (more compact)
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.ANALYTICS_ROUNDED, size=24, color=ft.Colors.BLUE_600),
                    ft.Text("Hiel RnE Modeler", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800)
                ], spacing=8),
                padding=ft.padding.only(left=20)
            ),
            
            # Center: Contextual navigation (replaces redundant breadcrumb)
            ft.Container(
                content=ft.Row([
                    ft.Container(
                        content=ft.Text("Project Setup", size=16, weight=ft.FontWeight.W_600, color=ft.Colors.GREY_700),
                        padding=ft.padding.symmetric(horizontal=16, vertical=8),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),
                    ft.Icon(ft.Icons.ARROW_FORWARD_IOS, size=14, color=ft.Colors.GREY_400),
                    ft.Text("Location Selection", size=14, color=ft.Colors.GREY_600)
                ], spacing=12),
                expand=True,
                alignment=ft.alignment.center
            ),
            
            # Right: Action buttons (more compact)
            ft.Container(
                content=ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.SEARCH,
                        icon_size=20,
                        tooltip="Search projects",
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.GREY_100,
                            shape=ft.CircleBorder()
                        )
                    ),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ADD, size=16),
                            ft.Text("New Project", size=14)
                        ], spacing=4, tight=True),
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE,
                            shape=ft.RoundedRectangleBorder(radius=8),
                            padding=ft.padding.symmetric(horizontal=16, vertical=8)
                        ),
                        height=36
                    ),
                    ft.IconButton(
                        icon=ft.Icons.DOWNLOAD,
                        icon_size=20,
                        tooltip="Export",
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.GREEN_100,
                            color=ft.Colors.GREEN_600,
                            shape=ft.CircleBorder()
                        )
                    )
                ], spacing=8),
                padding=ft.padding.only(right=20)
            )
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
        height=60,
        bgcolor=ft.Colors.WHITE,
        border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_200)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=4,
            color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
            offset=ft.Offset(0, 1)
        )
    )

def create_compact_sidebar() -> ft.Container:
    """Create more compact and efficient sidebar."""
    
    # Navigation items with better organization
    nav_items = [
        {"icon": ft.Icons.DASHBOARD_OUTLINED, "label": "Dashboard", "active": False},
        {"icon": ft.Icons.SETTINGS_OUTLINED, "label": "Setup", "active": True},
        {"icon": ft.Icons.LOCATION_ON_OUTLINED, "label": "Locations", "active": False},
        {"icon": ft.Icons.ANALYTICS_OUTLINED, "label": "Analysis", "active": False, "badge": "4"},
        {"icon": ft.Icons.VERIFIED_OUTLINED, "label": "Validation", "active": False, "badge": "New"},
        {"icon": ft.Icons.DOWNLOAD_OUTLINED, "label": "Export", "active": False}
    ]
    
    nav_controls = []
    for item in nav_items:
        # Create navigation item
        nav_item = ft.Container(
            content=ft.Row([
                ft.Icon(
                    item["icon"], 
                    size=20, 
                    color=ft.Colors.WHITE if item["active"] else ft.Colors.GREY_600
                ),
                ft.Text(
                    item["label"], 
                    size=14, 
                    weight=ft.FontWeight.W_500 if item["active"] else ft.FontWeight.W_400,
                    color=ft.Colors.WHITE if item["active"] else ft.Colors.GREY_700
                ),
                # Badge if present
                ft.Container(
                    content=ft.Text(
                        item.get("badge", ""), 
                        size=10, 
                        color=ft.Colors.WHITE,
                        weight=ft.FontWeight.BOLD
                    ),
                    bgcolor=ft.Colors.RED_500 if item.get("badge") == "New" else ft.Colors.BLUE_500,
                    border_radius=8,
                    padding=ft.padding.symmetric(horizontal=6, vertical=2),
                    visible=bool(item.get("badge"))
                )
            ], spacing=12, alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(horizontal=16, vertical=12),
            bgcolor=ft.Colors.BLUE_600 if item["active"] else ft.Colors.TRANSPARENT,
            border_radius=8,
            margin=ft.margin.symmetric(horizontal=8, vertical=2),
            animate=ft.Animation(200, ft.AnimationCurve.EASE_OUT)
        )
        nav_controls.append(nav_item)
    
    return ft.Container(
        content=ft.Column([
            # Compact header
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.ANALYTICS_ROUNDED, size=28, color=ft.Colors.BLUE_600),
                    ft.Text("RnE", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800)
                ], spacing=8),
                padding=ft.padding.all(16),
                border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_200))
            ),
            
            # Navigation items
            ft.Container(
                content=ft.Column(nav_controls, spacing=0),
                padding=ft.padding.symmetric(vertical=16),
                expand=True
            ),
            
            # Compact user section
            ft.Container(
                content=ft.Row([
                    ft.CircleAvatar(
                        content=ft.Text("FA", size=12, color=ft.Colors.WHITE),
                        bgcolor=ft.Colors.BLUE_600,
                        radius=16
                    ),
                    ft.Column([
                        ft.Text("Financial Analyst", size=12, weight=ft.FontWeight.W_500),
                        ft.Text("Online", size=10, color=ft.Colors.GREEN_600)
                    ], spacing=0)
                ], spacing=8),
                padding=ft.padding.all(16),
                border=ft.border.only(top=ft.BorderSide(1, ft.Colors.GREY_200))
            )
        ], spacing=0),
        width=220,  # Reduced from typical 280px
        bgcolor=ft.Colors.WHITE,
        border=ft.border.only(right=ft.BorderSide(1, ft.Colors.GREY_200))
    )

def create_improved_content_area() -> ft.Container:
    """Create improved content area with better organization."""
    return ft.Container(
        content=ft.Column([
            # Section header with context
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.LOCATION_ON, size=24, color=ft.Colors.ORANGE_600),
                    ft.Column([
                        ft.Text("Location Selection", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                        ft.Text("Choose locations for comparative analysis", size=14, color=ft.Colors.GREY_600)
                    ], spacing=2),
                    ft.Container(expand=True),
                    ft.Container(
                        content=ft.Text("No locations selected", size=12, color=ft.Colors.ORANGE_700),
                        padding=ft.padding.symmetric(horizontal=12, vertical=6),
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=12,
                        border=ft.border.all(1, ft.Colors.ORANGE_200)
                    )
                ], alignment=ft.MainAxisAlignment.START),
                padding=ft.padding.all(24),
                bgcolor=ft.Colors.WHITE,
                border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_200))
            ),
            
            # Main content area
            ft.Container(
                content=ft.Text("Location selection content goes here..."),
                padding=ft.padding.all(24),
                expand=True,
                bgcolor=ft.Colors.GREY_50
            )
        ], spacing=0),
        expand=True
    )

def create_improved_layout_demo() -> ft.Container:
    """Create complete improved layout demonstration."""
    return ft.Container(
        content=ft.Column([
            # Improved header
            create_improved_header_layout(),
            
            # Main content area
            ft.Container(
                content=ft.Row([
                    # Compact sidebar
                    create_compact_sidebar(),
                    
                    # Improved content area
                    create_improved_content_area()
                ], spacing=0),
                expand=True
            )
        ], spacing=0),
        expand=True
    )

# Key improvements summary:
"""
🎯 LAYOUT IMPROVEMENTS IMPLEMENTED:

1. HEADER OPTIMIZATION:
   ✅ More compact app branding
   ✅ Contextual navigation instead of redundant breadcrumbs
   ✅ Efficient action button placement
   ✅ Better space utilization

2. SIDEBAR IMPROVEMENTS:
   ✅ Reduced width (220px vs 280px)
   ✅ Better visual hierarchy
   ✅ Active state indicators
   ✅ Badges for notifications
   ✅ Compact user section

3. BREADCRUMB SIMPLIFICATION:
   ✅ Removed redundant "Home /" 
   ✅ Contextual navigation shows current section
   ✅ Clear progress indication

4. CONTENT ORGANIZATION:
   ✅ Clear section headers with context
   ✅ Status indicators
   ✅ Better visual hierarchy
   ✅ Improved spacing and layout

RESULT: More efficient use of screen space, clearer navigation, 
and better user experience overall.
"""
