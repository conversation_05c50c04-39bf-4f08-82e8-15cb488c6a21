# Piano Mattei-SIMEST Grant Unification Implementation

## Background

The Piano Mattei initiative is an Italian government program for cross-border development in Africa. Through research and analysis, we discovered that **Piano Mattei budget is managed through SIMEST** (Società Italiana per le Imprese all'Estero), Italy's export credit agency. This means that Piano Mattei and SIMEST grants represent the same funding source and should not be counted separately to avoid double-counting in financial models.

## Problem Statement

The previous implementation treated Piano Mattei (`grant_meur_italy`) and SIMEST (`grant_meur_simest_africa`) as separate grant sources throughout the application:

- **Data Model**: Separate fields in `EnhancedProjectAssumptions`
- **Validation**: Incorrect warning when SIMEST was used without Italian grants
- **Charts**: Separate slices/bars in all visualizations
- **Financial Calculations**: Independent summation leading to potential double-counting
- **Exports**: Separate line items in reports

## Solution: Soft Merge (Alias) Approach

We implemented a **Soft Merge** approach that maintains backward compatibility while preventing double-counting:

### Core Implementation

1. **Unified Internal Field**: Added `_grant_meur_piano_mattei_unified` as the canonical storage
2. **Property Aliases**: Both `grant_meur_italy` and `grant_meur_simest_africa` map to the unified field
3. **Validation**: Prevents simultaneous use of both legacy fields
4. **Migration**: Automatic consolidation during data loading

### Technical Details

```python
# Internal canonical field
_grant_meur_piano_mattei_unified: float = 0.0

# Property aliases for backward compatibility
@property
def grant_meur_italy(self) -> float:
    return self._grant_meur_piano_mattei_unified

@grant_meur_italy.setter
def grant_meur_italy(self, value: float):
    self._grant_meur_piano_mattei_unified = value
```

## Implementation Changes

### 1. Data Model (`models/project_assumptions.py`)
- Added unified internal field `_grant_meur_piano_mattei_unified`
- Replaced field definitions with property aliases
- Updated validation to prevent double-counting
- Modified `calculate_total_grants()` to use unified field
- Enhanced serialization to handle migration scenarios

### 2. Chart Factory (`components/charts/chart_factory.py`)
- Unified grant representation in financial structure charts
- Single slice for "Piano Mattei (incl. SIMEST)" in pie charts
- Updated LCOE waterfall charts with unified incentive names
- Consolidated funding scenarios in scenario analysis

### 3. Export Service (`services/export_service.py`)
- Single line item for "Piano Mattei (incl. SIMEST)" in grant analysis
- Unified LCOE/IRR impact calculations
- Updated scenario descriptions and funding breakdowns

### 4. UI Forms (`components/forms/project_params_form.py`)
- Grouped Italian Grant and SIMEST fields with explanatory text
- Implemented mutual exclusion logic with user feedback
- Added contextual help about Piano Mattei-SIMEST relationship

### 5. Migration Script (`migrations/piano_mattei_simest_unification.py`)
- Handles consolidation of existing data with both fields populated
- Creates backups and audit logs
- Provides rollback functionality
- Validates migration results

## Migration Guide

### For Existing Users

1. **Automatic Migration**: The application automatically consolidates grants when loading existing projects
2. **Data Preservation**: Total grant amounts are preserved by summing both legacy values
3. **Audit Trail**: Migration actions are logged for transparency

### For Developers

1. **Use Property Aliases**: Continue using `grant_meur_italy` or `grant_meur_simest_africa` - they now map to the same unified field
2. **Update Tests**: Ensure tests don't set both fields to non-zero values simultaneously
3. **Migration Script**: Use `migrations/piano_mattei_simest_unification.py` for batch processing

### Example Migration Scenarios

```python
# Scenario 1: Both fields populated (consolidation)
original_data = {
    'grant_meur_italy': 6.0,
    'grant_meur_simest_africa': 4.0
}
# Result: unified_value = 10.0

# Scenario 2: Only Italian grant (direct mapping)
original_data = {
    'grant_meur_italy': 8.0,
    'grant_meur_simest_africa': 0.0
}
# Result: unified_value = 8.0

# Scenario 3: Only SIMEST grant (direct mapping)
original_data = {
    'grant_meur_italy': 0.0,
    'grant_meur_simest_africa': 5.0
}
# Result: unified_value = 5.0
```

## Breaking Changes and Compatibility

### Breaking Changes
- Validation now prevents both `grant_meur_italy` and `grant_meur_simest_africa` from being non-zero simultaneously
- Chart visualizations show unified representation instead of separate slices/bars
- Export reports show single line item for Piano Mattei grants

### Backward Compatibility Measures
- Property aliases maintain API compatibility
- Serialization includes legacy fields for compatibility
- Migration handles existing data gracefully
- Documentation provides clear upgrade path

## Testing Strategy

### Test Coverage
1. **Property Alias Functionality**: Getters and setters work correctly
2. **Validation Rules**: Double-counting prevention
3. **Migration Scenarios**: All consolidation cases
4. **Chart Generation**: Unified representation
5. **Export Functionality**: Consolidated reports
6. **UI Behavior**: Mutual exclusion logic
7. **Financial Calculations**: Accuracy with unified grants
8. **Backward Compatibility**: Existing code continues to work

### Test Files
- `tests/test_piano_mattei_unification.py`: Comprehensive test suite
- Updated fixtures in `tests/conftest.py`
- Integration tests for end-to-end functionality

## User Communication

### UI Changes
- Grouped grant fields with explanatory text
- Warning messages about Piano Mattei-SIMEST relationship
- Automatic field clearing with user notification

### Documentation Updates
- Updated README.md with unified grant descriptions
- Enhanced USER_GUIDE.md with best practices and troubleshooting
- Updated API_REFERENCE.md with migration notes
- Added troubleshooting section for common issues

## Future Roadmap

### Phase 1 (Current): Soft Merge Implementation
- ✅ Property aliases for backward compatibility
- ✅ Unified internal representation
- ✅ Migration support for existing data

### Phase 2 (Future): Complete Field Consolidation
- Replace property aliases with single unified field
- Update all external APIs to use unified field names
- Remove legacy field references from templates and documentation

### Phase 3 (Future): Enhanced Grant Management
- Support for multiple Piano Mattei funding streams
- Integration with real-time SIMEST API (if available)
- Advanced grant optimization algorithms

## Troubleshooting

### Common Issues

1. **Unexpected Grant Totals**
   - Check that only one of Italian/SIMEST fields is populated
   - Run migration script to consolidate existing data

2. **Validation Errors**
   - Ensure both grant fields are not simultaneously non-zero
   - Use either Italian Grant OR SIMEST African Fund, not both

3. **Chart Display Issues**
   - Clear browser cache if charts show old separate representation
   - Regenerate charts after migration

4. **Migration Problems**
   - Check migration logs in `migrations/backups/`
   - Use rollback functionality if needed
   - Contact support for complex migration scenarios

### Support Resources
- Migration script: `migrations/piano_mattei_simest_unification.py`
- Test suite: `tests/test_piano_mattei_unification.py`
- User guide: `docs/guides/USER_GUIDE.md`
- API reference: `docs/references/API_REFERENCE.md`

## Conclusion

The Piano Mattei-SIMEST grant unification successfully addresses the double-counting issue while maintaining backward compatibility. The implementation provides a smooth migration path for existing users and a solid foundation for future enhancements to grant management functionality.

---

**Implementation Date**: January 26, 2025  
**Version**: 4.2  
**Author**: Renewable Energy Modeling Team  
**Status**: Complete
