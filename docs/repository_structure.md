# Repository Structure

This document describes the organization and structure of the Hiel RnE Model v4 project for current and future contributors.

## Project Layout Philosophy

The project follows a clean, modular architecture with clear separation of concerns:

- **Root level**: Contains only essential project files
- **Application code**: Organized into logical modules
- **Tests**: Structured by testing approach and component
- **Documentation**: Categorized by purpose and audience

## Directory Structure

```
Hiel-RnE-Model-v4/
├── main.py                    # Application entry point
├── requirements.txt           # Python dependencies
├── README.md                 # Project overview and quick start
├── pyproject.toml            # Project configuration and build settings
├── 
├── app/                      # Main application module
│   ├── __init__.py
│   └── ...                   # Application-specific code
├── 
├── components/               # Reusable UI components
│   ├── __init__.py
│   └── ...                   # Component modules
├── 
├── models/                   # Data models and business entities
│   ├── __init__.py
│   └── ...                   # Model definitions
├── 
├── services/                 # Business logic and external integrations
│   ├── __init__.py
│   └── ...                   # Service modules
├── 
├── utils/                    # Utility functions and helpers
│   ├── __init__.py
│   └── ...                   # Utility modules
├── 
├── views/                    # User interface views and layouts
│   ├── __init__.py
│   └── ...                   # View modules
├── 
├── config/                   # Configuration files and settings
│   ├── __init__.py
│   └── ...                   # Configuration modules
├── 
├── tests/                    # Test suite root
│   ├── __init__.py
│   ├── conftest.py          # Pytest configuration and fixtures
│   ├── run_tests.py         # Test runner script
│   ├── 
│   ├── unit/                # Unit tests (isolated component testing)
│   │   ├── __init__.py
│   │   └── ...              # Unit test modules
│   ├── 
│   ├── integration/         # Integration tests (cross-component testing)
│   │   ├── __init__.py
│   │   └── ...              # Integration test modules
│   ├── 
│   ├── ui/                  # UI tests (user interface testing)
│   │   ├── __init__.py
│   │   └── ...              # UI test modules
│   ├── 
│   ├── models/              # Model-specific tests
│   │   ├── __init__.py
│   │   └── ...              # Model test modules
│   ├── 
│   ├── services/            # Service-specific tests
│   │   ├── __init__.py
│   │   └── ...              # Service test modules
│   ├── 
│   ├── components/          # Component-specific tests
│   │   ├── __init__.py
│   │   └── ...              # Component test modules
│   └── 
│   └── ...                  # Additional test files
├── 
└── docs/                     # Documentation hub
    ├── repository_structure.md  # This file
    ├── 
    ├── guides/              # User and developer guides
    │   ├── USER_GUIDE.md    # End-user documentation
    │   ├── DEVELOPER_GUIDE.md  # Developer setup and contribution guide
    │   └── DEPLOYMENT_GUIDE.md  # Deployment instructions
    ├── 
    ├── changelogs/          # Release notes and change tracking
    │   ├── CHANGELOG.md     # Main changelog
    │   └── ...              # Version-specific release notes
    ├── 
    ├── references/          # Technical references
    │   ├── API_REFERENCE.md # API documentation
    │   ├── 2025_FINANCIAL_MODELING_STANDARDS.md  # Standards reference
    │   └── ...              # Architecture diagrams and technical docs
    └── 
    └── grants/              # Grant-specific documentation
        └── cri_grant.md     # CRI grant documentation
```

## Module Organization

### Core Application Modules

- **`app/`**: Main application logic and orchestration
- **`components/`**: Reusable UI components (buttons, forms, charts, etc.)
- **`models/`**: Data models, business entities, and domain logic
- **`services/`**: Business services, external API integrations, and data processing
- **`utils/`**: Utility functions, helpers, and common tools
- **`views/`**: UI views, layouts, and presentation logic
- **`config/`**: Configuration management and settings

### Testing Strategy

The test suite is organized by both testing approach and component:

- **`tests/unit/`**: Isolated unit tests for individual functions and classes
- **`tests/integration/`**: Cross-component integration tests
- **`tests/ui/`**: User interface and interaction tests
- **`tests/models/`**: Data model validation and business logic tests
- **`tests/services/`**: Service layer and business logic tests
- **`tests/components/`**: UI component behavior tests

### Documentation Categories

- **`docs/guides/`**: User and developer documentation
  - User guides for end-users
  - Developer guides for contributors
  - Deployment and setup guides

- **`docs/changelogs/`**: Release management
  - Main changelog following semantic versioning
  - Version-specific release notes

- **`docs/references/`**: Technical documentation
  - API reference documentation
  - Architecture diagrams
  - Technical standards and specifications

## File Organization Guidelines

### Root Directory (Keep Clean)

Only essential files should be in the root directory:

- `main.py` - Application entry point
- `requirements.txt` - Python dependencies
- `README.md` - Project overview
- `pyproject.toml` - Project configuration
- Core application directories (`app/`, `components/`, etc.)

### Naming Conventions

- **Files**: Use lowercase with underscores (`snake_case`)
- **Directories**: Use lowercase with underscores for multi-word names
- **Modules**: Follow Python naming conventions
- **Classes**: Use PascalCase
- **Functions**: Use snake_case

### Import Organization

- Use absolute imports from the project root
- Organize imports: standard library, third-party, local
- Use `__init__.py` files to control module exports

## Development Workflow

### Adding New Features

1. Create feature branch from `main`
2. Implement feature in appropriate module
3. Add tests in corresponding test directory
4. Update documentation if needed
5. Update changelog
6. Submit pull request

### Testing

Run tests using:
```bash
# All tests
pytest

# Specific test category
pytest tests/unit/
pytest tests/integration/
pytest tests/ui/

# With coverage
pytest --cov=app --cov=components --cov=models --cov=services --cov=utils --cov=views --cov=config
```

### Documentation

- Update relevant documentation when adding features
- Keep `docs/repository_structure.md` updated for structural changes
- Update `docs/changelogs/CHANGELOG.md` for all changes
- Add technical documentation to `docs/references/` as needed

## Contributing Guidelines

### Code Organization

- Place code in the most appropriate module
- Keep modules focused and cohesive
- Use clear, descriptive names
- Follow the existing code style and patterns

### Testing Requirements

- Write unit tests for new functions and classes
- Add integration tests for cross-component features
- Update or add UI tests for interface changes
- Ensure all tests pass before submitting

### Documentation Requirements

- Update user guides for user-facing changes
- Update developer guides for API changes
- Add technical documentation for new architectures
- Update changelog with all changes

## Maintenance

### Regular Tasks

- Review and update dependencies in `requirements.txt`
- Update documentation for accuracy
- Maintain changelog with all changes
- Review and organize test coverage

### Structural Changes

- Discuss major structural changes with the team
- Update this document for any directory changes
- Maintain backward compatibility where possible
- Update import statements if modules are moved

## Questions and Support

For questions about the project structure or contribution guidelines, please:

1. Check existing documentation in `docs/`
2. Review this structure document
3. Consult the developer guide in `docs/guides/DEVELOPER_GUIDE.md`
4. Contact the development team

---

*This document is maintained by the development team and should be updated whenever the project structure changes.*
