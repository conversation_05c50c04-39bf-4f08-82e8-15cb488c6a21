@echo off
REM Hiel Renewable Energy Financial Modeler - Build Script
REM ======================================================

echo.
echo 🏗️  Hiel RnE Financial Modeler Build Tool
echo ==========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python first.
    pause
    exit /b 1
)

echo ✓ Python found
echo.

REM Show build options
echo Available build options:
echo 1. Standard build (recommended)
echo 2. Debug build 
echo 3. Clean + Standard build
echo 4. PyInstaller build (fallback)
echo 5. Exit
echo.

set /p choice="Choose option (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🔨 Starting standard build...
    python build_exe.py
) else if "%choice%"=="2" (
    echo.
    echo 🔨 Starting debug build...
    python build_exe.py --debug
) else if "%choice%"=="3" (
    echo.
    echo 🔨 Starting clean build...
    python build_exe.py --clean
) else if "%choice%"=="4" (
    echo.
    echo 🔨 Starting PyInstaller build...
    python build_exe.py --pyinstaller
) else if "%choice%"=="5" (
    echo Goodbye!
    exit /b 0
) else (
    echo Invalid choice. Please run again.
    pause
    exit /b 1
)

echo.
if errorlevel 1 (
    echo ❌ Build failed!
    echo Check the error messages above.
) else (
    echo ✅ Build completed!
    echo Check the 'dist' folder for your executable.
    echo.
    echo 📦 Opening dist folder...
    explorer dist
)

echo.
pause 