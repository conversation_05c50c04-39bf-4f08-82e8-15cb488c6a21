"""
Test Configuration
==================

Pytest configuration and fixtures for the test suite.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import MagicMock

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions


@pytest.fixture
def temp_directory():
    """Create a temporary directory for testing."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def sample_client_profile():
    """Create a sample client profile for testing."""
    profile = ClientProfile()
    profile.company_name = "Test Solar Company"
    profile.client_name = "<PERSON>"
    profile.project_name = "Test Solar Project"
    profile.contact_email = "<EMAIL>"
    profile.phone = "******-123-4567"
    profile.project_location = "Ouarzazate, Morocco"
    profile.project_capacity_mw = 10.0
    profile.preferred_currency = "EUR"
    return profile


@pytest.fixture
def sample_project_assumptions():
    """Create sample project assumptions for testing."""
    assumptions = EnhancedProjectAssumptions()
    
    # Technical parameters
    assumptions.capacity_mw = 10.0
    assumptions.production_mwh_year1 = 18000.0
    assumptions.degradation_rate = 0.005
    assumptions.project_life_years = 25
    
    # Financial parameters
    assumptions.capex_meur = 8.5
    assumptions.opex_keuros_year1 = 180.0
    assumptions.ppa_price_eur_kwh = 0.045
    assumptions.ppa_escalation = 0.0
    assumptions.debt_ratio = 0.75
    assumptions.interest_rate = 0.06
    assumptions.debt_years = 15
    assumptions.discount_rate = 0.08
    assumptions.tax_rate = 0.30
    assumptions.land_lease_eur_mw_year = 2000.0
    
    # Grant parameters - using unified SIMEST approach
    assumptions._grant_meur_piano_mattei_unified = 5.0  # SIMEST total facility
    assumptions.grant_meur_masen = 3.0
    assumptions.grant_meur_connection = 1.0
    assumptions.grant_meur_cri = 0.5
    
    # Location for SIMEST eligibility
    assumptions.project_location = "Morocco"
    
    return assumptions


@pytest.fixture
def sample_project_assumptions_with_piano_mattei():
    """Create sample project assumptions with Piano Mattei grants for testing unified approach."""
    assumptions = EnhancedProjectAssumptions()

    # Basic project parameters
    assumptions.capacity_mw = 50.0
    assumptions.capex_meur = 75.0

    # Grant parameters - SIMEST unified approach
    assumptions._grant_meur_piano_mattei_unified = 8.0  # SIMEST total facility
    assumptions.grant_meur_masen = 2.0
    assumptions.grant_meur_connection = 1.0
    assumptions.grant_meur_cri = 0.5

    # Location for SIMEST eligibility
    assumptions.project_location = "Kenya"

    return assumptions


@pytest.fixture
def sample_project_assumptions_migration_scenario():
    """Create sample data for testing migration scenarios where both fields might be populated."""
    return {
        'capacity_mw': 100.0,
        'capex_meur': 150.0,
        'grant_meur_italy': 6.0,  # Legacy Piano Mattei
        'grant_meur_simest_africa': 4.0,  # Legacy SIMEST
        'grant_meur_masen': 5.0,
        'grant_meur_connection': 2.0,
        'grant_meur_cri': 1.0,
        'project_location': 'Nigeria'
    }


@pytest.fixture
def sample_simest_configuration():
    """Create sample SIMEST configuration for testing."""
    return {
        "program_name": "SIMEST - Strengthening African Markets",
        "fund_size_meur": 200,
        "grant_percentage_standard": 0.10,
        "grant_percentage_southern_italy": 0.20,
        "soft_loan_interest_rate": 0.00511,
        "soft_loan_tenor_years": 6,
        "soft_loan_grace_years": 2,
        "eligible_countries": [
            "morocco", "algeria", "tunisia", "egypt", "libya", "sudan", "south_sudan",
            "ethiopia", "eritrea", "djibouti", "somalia", "kenya", "uganda", "tanzania",
            "rwanda", "burundi", "democratic_republic_congo", "central_african_republic",
            "chad", "cameroon", "equatorial_guinea", "gabon", "sao_tome_principe",
            "nigeria", "niger", "burkina_faso", "mali", "mauritania", "senegal",
            "gambia", "guinea_bissau", "guinea", "sierra_leone", "liberia",
            "ivory_coast", "ghana", "togo", "benin", "cape_verde", "madagascar",
            "mauritius", "seychelles", "comoros", "zambia", "malawi", "mozambique",
            "zimbabwe", "botswana", "namibia", "south_africa", "lesotho",
            "swaziland", "angola"
        ],
        "minimum_facility_meur": 0.1,
        "maximum_facility_meur": 50.0,
        "eligible_activities": [
            "productive_investments", "digitalization", "sustainability",
            "training", "innovation", "commercial_investments"
        ],
        "company_eligibility": {
            "italian_exporter": True,
            "supply_chain_member": True,
            "minimum_turnover_eur": 500000,
            "strategic_africa_interest": True
        }
    }


@pytest.fixture
def sample_simest_split_scenario():
    """Create sample SIMEST split scenario for testing grant/soft loan structure."""
    assumptions = EnhancedProjectAssumptions()
    
    # Technical parameters
    assumptions.capacity_mw = 25.0
    assumptions.production_mwh_year1 = 45000.0
    assumptions.degradation_rate = 0.005
    assumptions.project_life_years = 25
    
    # Financial parameters
    assumptions.capex_meur = 20.0
    assumptions.opex_keuros_year1 = 400.0
    assumptions.ppa_price_eur_kwh = 0.048
    assumptions.debt_ratio = 0.70
    assumptions.interest_rate = 0.065
    assumptions.debt_years = 15
    assumptions.discount_rate = 0.09
    assumptions.tax_rate = 0.25
    
    # SIMEST facility - 10 MEUR total (1 MEUR grant + 9 MEUR soft loan for standard company)
    assumptions._grant_meur_piano_mattei_unified = 10.0
    assumptions.grant_meur_masen = 1.5
    assumptions.grant_meur_connection = 0.5
    
    # African project location for SIMEST eligibility
    assumptions.project_location = "Ghana"
    
    return assumptions


@pytest.fixture
def sample_southern_italy_scenario():
    """Create sample scenario for Southern Italy company testing 20% grant percentage."""
    assumptions = EnhancedProjectAssumptions()
    
    # Technical parameters
    assumptions.capacity_mw = 15.0
    assumptions.production_mwh_year1 = 27000.0
    assumptions.project_life_years = 25
    
    # Financial parameters
    assumptions.capex_meur = 12.0
    assumptions.opex_keuros_year1 = 240.0
    assumptions.ppa_price_eur_kwh = 0.052
    assumptions.debt_ratio = 0.75
    assumptions.interest_rate = 0.06
    assumptions.debt_years = 15
    assumptions.discount_rate = 0.08
    
    # SIMEST facility - 6 MEUR total (1.2 MEUR grant + 4.8 MEUR soft loan for Southern Italy)
    assumptions._grant_meur_piano_mattei_unified = 6.0
    assumptions.grant_meur_masen = 0.8
    
    # African project location for SIMEST eligibility
    assumptions.project_location = "Tunisia"
    
    return assumptions


@pytest.fixture
def sample_african_project_locations():
    """Create sample African project locations for SIMEST eligibility testing."""
    return [
        {"location": "Morocco", "country_code": "morocco", "eligible": True},
        {"location": "Ouarzazate", "country_code": "morocco", "eligible": True},
        {"location": "Lagos, Nigeria", "country_code": "nigeria", "eligible": True},
        {"location": "Cairo, Egypt", "country_code": "egypt", "eligible": True},
        {"location": "Nairobi, Kenya", "country_code": "kenya", "eligible": True},
        {"location": "Cape Town, South Africa", "country_code": "south_africa", "eligible": True},
        {"location": "Accra, Ghana", "country_code": "ghana", "eligible": True},
        {"location": "Tunis, Tunisia", "country_code": "tunisia", "eligible": True},
        {"location": "Addis Ababa, Ethiopia", "country_code": "ethiopia", "eligible": True},
        {"location": "Kigali, Rwanda", "country_code": "rwanda", "eligible": True},
        # Non-eligible locations for testing
        {"location": "Rome, Italy", "country_code": "italy", "eligible": False},
        {"location": "Madrid, Spain", "country_code": "spain", "eligible": False},
        {"location": "Paris, France", "country_code": "france", "eligible": False}
    ]


@pytest.fixture
def sample_simest_validation_scenarios():
    """Create sample scenarios for SIMEST program validation testing."""
    return {
        "valid_standard_company": {
            "simest_facility_meur": 5.0,
            "capex_meur": 50.0,
            "project_location": "morocco",
            "company_southern_italy": False,
            "expected_grant_meur": 0.5,  # 10% of facility
            "expected_soft_loan_meur": 4.5,  # 90% of facility
            "expected_valid": True
        },
        "valid_southern_italy_company": {
            "simest_facility_meur": 10.0,
            "capex_meur": 80.0,
            "project_location": "kenya",
            "company_southern_italy": True,
            "expected_grant_meur": 2.0,  # 20% of facility
            "expected_soft_loan_meur": 8.0,  # 80% of facility
            "expected_valid": True
        },
        "invalid_non_african_location": {
            "simest_facility_meur": 3.0,
            "capex_meur": 30.0,
            "project_location": "spain",
            "company_southern_italy": False,
            "expected_valid": False,
            "expected_error": "SIMEST funding is designed for African projects"
        },
        "invalid_facility_too_small": {
            "simest_facility_meur": 0.05,
            "capex_meur": 10.0,
            "project_location": "nigeria",
            "company_southern_italy": False,
            "expected_valid": False,
            "expected_error": "SIMEST facility below minimum"
        },
        "invalid_facility_too_large": {
            "simest_facility_meur": 60.0,
            "capex_meur": 200.0,
            "project_location": "egypt",
            "company_southern_italy": False,
            "expected_valid": False,
            "expected_error": "SIMEST facility exceeds maximum"
        }
    }


@pytest.fixture
def sample_simest_financial_model_scenarios():
    """Create sample scenarios for SIMEST financial model testing."""
    return {
        "no_simest": {
            "assumptions": {
                "capacity_mw": 20.0,
                "capex_meur": 16.0,
                "_grant_meur_piano_mattei_unified": 0.0,
                "grant_meur_masen": 2.0,
                "debt_ratio": 0.75,
                "interest_rate": 0.065,
                "project_location": "morocco"
            },
            "expected_total_grants": 2.0,
            "expected_debt_facilities": 1  # Only regular debt
        },
        "simest_standard_company": {
            "assumptions": {
                "capacity_mw": 20.0,
                "capex_meur": 16.0,
                "_grant_meur_piano_mattei_unified": 8.0,
                "grant_meur_masen": 2.0,
                "debt_ratio": 0.75,
                "interest_rate": 0.065,
                "project_location": "ghana"
            },
            "expected_total_grants": 2.8,  # 2.0 MASEN + 0.8 SIMEST grant (10%)
            "expected_simest_grant": 0.8,
            "expected_simest_soft_loan": 7.2,
            "expected_debt_facilities": 2  # Regular debt + SIMEST soft loan
        },
        "simest_southern_italy": {
            "assumptions": {
                "capacity_mw": 20.0,
                "capex_meur": 16.0,
                "_grant_meur_piano_mattei_unified": 8.0,
                "grant_meur_masen": 2.0,
                "debt_ratio": 0.75,
                "interest_rate": 0.065,
                "project_location": "tunisia"
            },
            "expected_total_grants": 3.6,  # 2.0 MASEN + 1.6 SIMEST grant (20%)
            "expected_simest_grant": 1.6,
            "expected_simest_soft_loan": 6.4,
            "expected_debt_facilities": 2  # Regular debt + SIMEST soft loan
        }
    }


@pytest.fixture
def sample_form_data():
    """Create sample form data for round-trip testing."""
    return {
        'grant_meur_cri': 1.0,
        '_grant_meur_piano_mattei_unified': 5.0,
        'project_location': 'Morocco'
    }


@pytest.fixture
def sample_simest_form_data():
    """Create sample SIMEST form data for testing UI interactions."""
    return {
        'simest_total_facility_meur': 10.0,
        'company_southern_italy': False,
        'project_location': 'Kenya',
        'capex_meur': 50.0
    }


@pytest.fixture
def sample_legacy_migration_data():
    """Create sample legacy data for testing migration scenarios."""
    return [
        {
            # Case 1: Only Piano Mattei field populated
            'input': {
                'capacity_mw': 30.0,
                'capex_meur': 25.0,
                'grant_meur_italy': 5.0,
                'grant_meur_simest_africa': 0.0,
                'project_location': 'Morocco'
            },
            'expected_unified': 5.0
        },
        {
            # Case 2: Only SIMEST field populated
            'input': {
                'capacity_mw': 40.0,
                'capex_meur': 35.0,
                'grant_meur_italy': 0.0,
                'grant_meur_simest_africa': 7.0,
                'project_location': 'Nigeria'
            },
            'expected_unified': 7.0
        },
        {
            # Case 3: Both fields populated (migration scenario)
            'input': {
                'capacity_mw': 50.0,
                'capex_meur': 45.0,
                'grant_meur_italy': 3.0,
                'grant_meur_simest_africa': 4.0,
                'project_location': 'Egypt'
            },
            'expected_unified': 7.0  # Sum of both
        },
        {
            # Case 4: Neither field populated
            'input': {
                'capacity_mw': 20.0,
                'capex_meur': 18.0,
                'grant_meur_italy': 0.0,
                'grant_meur_simest_africa': 0.0,
                'project_location': 'Ghana'
            },
            'expected_unified': 0.0
        }
    ]


@pytest.fixture
def sample_financial_results():
    """Create sample financial results for testing."""
    return {
        'kpis': {
            'IRR_project': 0.12,
            'IRR_equity': 0.15,
            'NPV_project': 5000000,
            'NPV_equity': 2000000,
            'LCOE_eur_kwh': 0.042,
            'Min_DSCR': 1.35,
            'Avg_DSCR': 1.45,
            'Payback_years': 8.5
        },
        'cashflow': {
            'Year': list(range(1, 26)),
            'Revenue': [810000] * 25,
            'OPEX': [-180000] * 25,
            'Capex': [-8500000] + [0] * 24,
            'Grants': [0] * 25,
            'Debt_Service': [0] + [-400000] * 15 + [0] * 9,
            'Equity_CF': [-2125000] + [500000] * 24,
            'Project_CF': [-8500000] + [630000] * 24,
            'DSCR': [0] + [1.4] * 15 + [0] * 9
        },
        'assumptions': {}  # Would contain the assumptions dict
    }


@pytest.fixture
def mock_page():
    """Create a mock Flet page for testing UI components."""
    page = MagicMock()
    page.overlay = []
    page.update = MagicMock()
    page.add = MagicMock()
    page.clean = MagicMock()
    page.show_snack_bar = MagicMock()
    return page


@pytest.fixture
def mock_file_system(temp_directory):
    """Create a mock file system structure for testing."""
    # Create directory structure
    data_dir = temp_directory / "data"
    charts_dir = temp_directory / "charts"
    reports_dir = temp_directory / "reports"
    
    data_dir.mkdir()
    charts_dir.mkdir()
    reports_dir.mkdir()
    
    # Create some test files
    test_data_file = data_dir / "test_data.json"
    test_data_file.write_text('{"test": "data"}')
    
    test_report_file = reports_dir / "test_report.xlsx"
    test_report_file.write_text("mock excel content")
    
    return {
        'base_dir': temp_directory,
        'data_dir': data_dir,
        'charts_dir': charts_dir,
        'reports_dir': reports_dir,
        'test_files': {
            'data': test_data_file,
            'report': test_report_file
        }
    }


@pytest.fixture
def mock_validation_results():
    """Create mock validation results for testing."""
    validation_result = MagicMock()
    validation_result.is_valid = True
    validation_result.warnings = []
    validation_result.errors = []
    validation_result.recommendations = []
    return validation_result


@pytest.fixture
def mock_benchmark_results():
    """Create mock benchmark results for testing."""
    return {
        'custom_analysis': {
            'irr_project': {
                'current': 0.12,
                'benchmark_min': 0.10,
                'benchmark_target': 0.12,
                'status': 'good'
            },
            'irr_equity': {
                'current': 0.15,
                'benchmark_min': 0.12,
                'benchmark_target': 0.15,
                'status': 'excellent'
            },
            'lcoe': {
                'current': 0.042,
                'benchmark_max': 0.050,
                'benchmark_target': 0.045,
                'status': 'excellent'
            },
            'dscr': {
                'current': 1.35,
                'benchmark_min': 1.20,
                'benchmark_target': 1.30,
                'status': 'good'
            }
        },
        'industry_benchmarks': {
            'solar_pv_morocco': {
                'irr_range': [0.08, 0.15],
                'lcoe_range': [0.035, 0.055],
                'capacity_factor_range': [0.18, 0.25]
            }
        }
    }


# Test configuration
def pytest_configure(config):
    """Configure pytest."""
    # Add custom markers
    config.addinivalue_line("markers", "round_trip: mark test for form round-trip value retention")
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "ui: mark test as UI test"
    )


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection."""
    # Add markers based on test file names
    for item in items:
        if "test_integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        elif "test_ui" in item.nodeid:
            item.add_marker(pytest.mark.ui)
        else:
            item.add_marker(pytest.mark.unit)
        
        # Mark slow tests
        if "monte_carlo" in item.name.lower() or "comprehensive" in item.name.lower():
            item.add_marker(pytest.mark.slow)
