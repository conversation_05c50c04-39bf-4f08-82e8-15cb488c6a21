#!/usr/bin/env python3
"""
Migration Script: Add CRI Grant Field
====================================

This script adds the 'grant_meur_cri' field to existing saved projects
in the SQLite database. All existing projects will have this field set
to 0.0 by default.

Usage:
    python migrations/add_cri_grant_field.py [--dry-run] [--db-path PATH]

Options:
    --dry-run    Show what would be updated without making changes
    --db-path    Path to the database file (default: data/projects.db)
"""

import sqlite3
import json
import logging
import argparse
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Tuple

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CRIGrantMigration:
    """Migration to add grant_meur_cri field to existing projects."""
    
    def __init__(self, db_path: str = "data/projects.db"):
        self.db_path = Path(db_path)
        self.backup_path = None
        
        if not self.db_path.exists():
            raise FileNotFoundError(f"Database not found: {self.db_path}")
    
    def create_backup(self) -> bool:
        """Create a backup of the database before migration."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.backup_path = self.db_path.with_suffix(f".backup_{timestamp}.db")
            
            import shutil
            shutil.copy2(self.db_path, self.backup_path)
            
            logger.info(f"✅ Database backup created: {self.backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create backup: {e}")
            return False
    
    def scan_projects(self) -> Tuple[List[Dict], List[Dict]]:
        """Scan projects to identify which need the CRI grant field."""
        projects_to_update = []
        projects_already_have_cri = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get all active projects
                cursor.execute("""
                    SELECT id, name, project_assumptions, version, modified_at
                    FROM projects 
                    WHERE is_deleted = FALSE
                """)
                
                for row in cursor.fetchall():
                    project_id, name, assumptions_json, version, modified_at = row
                    
                    try:
                        assumptions = json.loads(assumptions_json)
                        
                        if 'grant_meur_cri' not in assumptions:
                            projects_to_update.append({
                                'id': project_id,
                                'name': name,
                                'version': version,
                                'modified_at': modified_at,
                                'assumptions': assumptions
                            })
                        else:
                            projects_already_have_cri.append({
                                'id': project_id,
                                'name': name,
                                'version': version,
                                'cri_value': assumptions.get('grant_meur_cri', 0)
                            })
                            
                    except json.JSONDecodeError as e:
                        logger.warning(f"⚠️  Skipping project {project_id} due to JSON decode error: {e}")
                        continue
                
        except Exception as e:
            logger.error(f"❌ Error scanning projects: {e}")
            raise
        
        return projects_to_update, projects_already_have_cri
    
    def scan_project_versions(self) -> List[Dict]:
        """Scan project versions that need the CRI grant field."""
        versions_to_update = []
        
        try:
            import pickle
            import gzip
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get all project versions
                cursor.execute("""
                    SELECT id, project_id, version, data, created_at, comment
                    FROM project_versions
                """)
                
                for row in cursor.fetchall():
                    version_id, project_id, version, data_blob, created_at, comment = row
                    
                    try:
                        # Decompress and deserialize version data
                        decompressed_data = gzip.decompress(data_blob)
                        version_data = pickle.loads(decompressed_data)
                        
                        # Check if project assumptions exist and lack CRI grant
                        project_dict = version_data.get('project', {})
                        assumptions = project_dict.get('project_assumptions', {})
                        
                        if isinstance(assumptions, dict) and 'grant_meur_cri' not in assumptions:
                            versions_to_update.append({
                                'id': version_id,
                                'project_id': project_id,
                                'version': version,
                                'created_at': created_at,
                                'comment': comment,
                                'data': version_data
                            })
                            
                    except Exception as e:
                        logger.warning(f"⚠️  Skipping version {version_id} due to error: {e}")
                        continue
                
        except Exception as e:
            logger.error(f"❌ Error scanning project versions: {e}")
            raise
        
        return versions_to_update
    
    def update_projects(self, projects_to_update: List[Dict], dry_run: bool = False) -> int:
        """Update main projects table with CRI grant field."""
        updated_count = 0
        
        if not projects_to_update:
            logger.info("✅ No projects need updating in main table")
            return 0
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for project in projects_to_update:
                    project_id = project['id']
                    assumptions = project['assumptions']
                    
                    # Add the CRI grant field with default value 0.0
                    assumptions['grant_meur_cri'] = 0.0
                    
                    # Update the project assumptions JSON
                    updated_json = json.dumps(assumptions)
                    
                    if dry_run:
                        logger.info(f"  [DRY RUN] Would update project '{project['name']}' (ID: {project_id})")
                    else:
                        cursor.execute("""
                            UPDATE projects 
                            SET project_assumptions = ?, modified_at = ?
                            WHERE id = ?
                        """, (updated_json, datetime.now().isoformat(), project_id))
                        
                        logger.info(f"  ✅ Updated project '{project['name']}' (ID: {project_id})")
                    
                    updated_count += 1
                
                if not dry_run:
                    conn.commit()
                    logger.info(f"✅ Successfully updated {updated_count} projects in main table")
                else:
                    logger.info(f"[DRY RUN] Would update {updated_count} projects in main table")
                
        except Exception as e:
            logger.error(f"❌ Error updating projects: {e}")
            raise
        
        return updated_count
    
    def update_project_versions(self, versions_to_update: List[Dict], dry_run: bool = False) -> int:
        """Update project versions with CRI grant field."""
        updated_count = 0
        
        if not versions_to_update:
            logger.info("✅ No project versions need updating")
            return 0
        
        try:
            import pickle
            import gzip
            import hashlib
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for version_info in versions_to_update:
                    version_id = version_info['id']
                    version_data = version_info['data']
                    
                    # Update the project assumptions in the version data
                    project_dict = version_data.get('project', {})
                    assumptions = project_dict.get('project_assumptions', {})
                    
                    if isinstance(assumptions, dict):
                        assumptions['grant_meur_cri'] = 0.0
                        project_dict['project_assumptions'] = assumptions
                        version_data['project'] = project_dict
                        
                        # Recompress the updated data
                        compressed_data = gzip.compress(pickle.dumps(version_data))
                        new_checksum = hashlib.sha256(compressed_data).hexdigest()
                        
                        if dry_run:
                            logger.info(f"  [DRY RUN] Would update version {version_info['version']} "
                                      f"of project {version_info['project_id']}")
                        else:
                            cursor.execute("""
                                UPDATE project_versions 
                                SET data = ?, checksum = ?, file_size = ?
                                WHERE id = ?
                            """, (compressed_data, new_checksum, len(compressed_data), version_id))
                            
                            logger.info(f"  ✅ Updated version {version_info['version']} "
                                      f"of project {version_info['project_id']}")
                        
                        updated_count += 1
                
                if not dry_run:
                    conn.commit()
                    logger.info(f"✅ Successfully updated {updated_count} project versions")
                else:
                    logger.info(f"[DRY RUN] Would update {updated_count} project versions")
                
        except Exception as e:
            logger.error(f"❌ Error updating project versions: {e}")
            raise
        
        return updated_count
    
    def run_migration(self, dry_run: bool = False) -> Dict[str, Any]:
        """Run the complete migration process."""
        logger.info("🚀 Starting CRI Grant Field Migration")
        logger.info(f"Database: {self.db_path}")
        logger.info(f"Mode: {'DRY RUN' if dry_run else 'LIVE MIGRATION'}")
        logger.info("-" * 60)
        
        results = {
            'success': False,
            'projects_updated': 0,
            'versions_updated': 0,
            'projects_already_have_cri': 0,
            'backup_created': False,
            'error': None
        }
        
        try:
            # Step 1: Create backup (not in dry-run mode)
            if not dry_run:
                if self.create_backup():
                    results['backup_created'] = True
                else:
                    raise Exception("Failed to create backup")
            
            # Step 2: Scan existing projects
            logger.info("📊 Scanning existing projects...")
            projects_to_update, projects_already_have_cri = self.scan_projects()
            
            logger.info(f"  • Projects needing CRI field: {len(projects_to_update)}")
            logger.info(f"  • Projects already have CRI field: {len(projects_already_have_cri)}")
            results['projects_already_have_cri'] = len(projects_already_have_cri)
            
            # Step 3: Show projects that already have CRI field
            if projects_already_have_cri:
                logger.info("\n📋 Projects already have CRI grant field:")
                for project in projects_already_have_cri:
                    cri_value = project['cri_value']
                    logger.info(f"  • {project['name']} (ID: {project['id']}) = €{cri_value:.1f}M")
            
            # Step 4: Update main projects table
            logger.info("\n🔧 Updating main projects table...")
            projects_updated = self.update_projects(projects_to_update, dry_run)
            results['projects_updated'] = projects_updated
            
            # Step 5: Scan and update project versions
            logger.info("\n📚 Scanning project versions...")
            versions_to_update = self.scan_project_versions()
            logger.info(f"  • Project versions needing CRI field: {len(versions_to_update)}")
            
            logger.info("\n🔧 Updating project versions...")
            versions_updated = self.update_project_versions(versions_to_update, dry_run)
            results['versions_updated'] = versions_updated
            
            # Step 6: Migration summary
            logger.info("\n" + "=" * 60)
            logger.info("📝 MIGRATION SUMMARY")
            logger.info("=" * 60)
            
            if dry_run:
                logger.info("🔍 DRY RUN RESULTS:")
                logger.info(f"  • Would update {projects_updated} projects")
                logger.info(f"  • Would update {versions_updated} project versions")
                logger.info(f"  • {len(projects_already_have_cri)} projects already have CRI field")
                logger.info("\n✅ Run without --dry-run to apply changes")
            else:
                logger.info("✅ MIGRATION COMPLETED SUCCESSFULLY:")
                logger.info(f"  • Updated {projects_updated} projects")
                logger.info(f"  • Updated {versions_updated} project versions")
                logger.info(f"  • {len(projects_already_have_cri)} projects already had CRI field")
                logger.info(f"  • Backup created: {self.backup_path}")
                
                logger.info("\n🎯 All existing projects now have 'grant_meur_cri' field set to 0.0")
                logger.info("🔄 Future saves will include this field automatically")
            
            results['success'] = True
            
        except Exception as e:
            error_msg = f"Migration failed: {str(e)}"
            logger.error(f"❌ {error_msg}")
            results['error'] = error_msg
            
            # Offer to restore backup if migration failed
            if not dry_run and self.backup_path and self.backup_path.exists():
                logger.error(f"💾 Backup available for restore: {self.backup_path}")
        
        return results


def main():
    """Main migration entry point."""
    parser = argparse.ArgumentParser(
        description="Add grant_meur_cri field to existing projects",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python migrations/add_cri_grant_field.py --dry-run
  python migrations/add_cri_grant_field.py
  python migrations/add_cri_grant_field.py --db-path custom/path/projects.db
        """
    )
    
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='Show what would be updated without making changes'
    )
    
    parser.add_argument(
        '--db-path',
        type=str,
        default='data/projects.db',
        help='Path to the database file (default: data/projects.db)'
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize and run migration
        migration = CRIGrantMigration(db_path=args.db_path)
        results = migration.run_migration(dry_run=args.dry_run)
        
        # Exit with appropriate code
        if results['success']:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except FileNotFoundError as e:
        logger.error(f"❌ {e}")
        logger.error("💡 Make sure the database path is correct and the file exists")
        sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("\n⏹️  Migration cancelled by user")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
