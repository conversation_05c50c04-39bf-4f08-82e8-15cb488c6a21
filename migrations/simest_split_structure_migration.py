#!/usr/bin/env python3
"""
SIMEST Split Structure Migration Script
======================================

This script migrates the existing unified SIMEST grant structure to the new split structure
that properly models the SIMEST "Strengthening African Markets" program with separate
grant and soft loan components.

Migration Overview:
- Converts unified SIMEST amounts to split structure (10-20% grant + 80-90% soft loan)
- Handles legacy Piano Mattei and SIMEST field consolidation
- Updates financial models to reflect dual debt facility structure
- Provides comprehensive backup, validation, and rollback capabilities

Author: Traycer.AI
Date: 2024
Version: 1.0
"""

import os
import sys
import json
import shutil
import logging
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import argparse


@dataclass
class MigrationConfig:
    """Configuration for SIMEST migration."""
    backup_dir: str
    log_file: str
    dry_run: bool = False
    validate_only: bool = False
    force_rollback: bool = False
    rollback_timestamp: Optional[str] = None
    default_grant_percentage: float = 0.10  # 10% standard grant
    southern_italy_grant_percentage: float = 0.20  # 20% for Southern Italy
    soft_loan_interest_rate: float = 0.00511  # 0.511% preferential rate
    soft_loan_tenor_years: int = 6
    soft_loan_grace_years: int = 2


@dataclass
class MigrationStats:
    """Statistics for migration tracking."""
    projects_scanned: int = 0
    projects_with_simest: int = 0
    projects_migrated: int = 0
    projects_failed: int = 0
    backup_size_mb: float = 0.0
    migration_duration_seconds: float = 0.0
    validation_errors: List[str] = None
    
    def __post_init__(self):
        if self.validation_errors is None:
            self.validation_errors = []


class SimestMigrationError(Exception):
    """Custom exception for SIMEST migration errors."""
    pass


class SimestSplitStructureMigration:
    """
    Comprehensive migration handler for SIMEST split structure implementation.
    
    This class handles the migration from the legacy unified SIMEST grant structure
    to the new split structure that properly models the official SIMEST program
    with separate grant and soft loan components.
    """
    
    def __init__(self, config: MigrationConfig):
        """Initialize migration with configuration."""
        self.config = config
        self.stats = MigrationStats()
        self.logger = self._setup_logging()
        self.simest_config = None
        self.backup_manifest = {}
        
        # Project data storage paths
        self.project_data_dir = Path("data/projects")
        self.templates_dir = Path("config/templates")
        self.config_dir = Path("config")
        
        # Migration tracking
        self.migration_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_path = Path(self.config.backup_dir) / f"simest_migration_{self.migration_id}"
        
    def _setup_logging(self) -> logging.Logger:
        """Set up comprehensive logging for migration tracking."""
        logger = logging.getLogger('simest_migration')
        logger.setLevel(logging.DEBUG)
        
        # Create log directory if it doesn't exist
        log_path = Path(self.config.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # File handler for detailed logs
        file_handler = logging.FileHandler(self.config.log_file)
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler for user feedback
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def load_simest_config(self) -> Dict[str, Any]:
        """Load and validate SIMEST configuration."""
        try:
            config_path = self.config_dir / "simest_config.json"
            
            if not config_path.exists():
                self.logger.warning(f"SIMEST config not found at {config_path}, using defaults")
                return self._get_default_simest_config()
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Validate required fields
            required_fields = [
                'grant_percentage_standard',
                'grant_percentage_southern_italy',
                'soft_loan_interest_rate',
                'soft_loan_tenor_years',
                'soft_loan_grace_years'
            ]
            
            for field in required_fields:
                if field not in config:
                    raise SimestMigrationError(f"Missing required SIMEST config field: {field}")
            
            self.logger.info("SIMEST configuration loaded successfully")
            return config
            
        except Exception as e:
            self.logger.error(f"Failed to load SIMEST configuration: {e}")
            raise SimestMigrationError(f"SIMEST configuration error: {e}")
    
    def _get_default_simest_config(self) -> Dict[str, Any]:
        """Get default SIMEST configuration if config file is missing."""
        return {
            "program_name": "SIMEST - Strengthening African Markets",
            "fund_size_meur": 200,
            "grant_percentage_standard": 0.10,
            "grant_percentage_southern_italy": 0.20,
            "soft_loan_interest_rate": 0.00511,
            "soft_loan_tenor_years": 6,
            "soft_loan_grace_years": 2,
            "minimum_facility_meur": 0.1,
            "maximum_facility_meur": 50.0,
            "eligible_countries": [
                "morocco", "algeria", "tunisia", "egypt", "libya", "sudan",
                "south_sudan", "ethiopia", "eritrea", "djibouti", "somalia",
                "kenya", "uganda", "tanzania", "rwanda", "burundi",
                "democratic_republic_congo", "central_african_republic",
                "chad", "cameroon", "equatorial_guinea", "gabon",
                "sao_tome_principe", "nigeria", "niger", "burkina_faso",
                "mali", "mauritania", "senegal", "gambia", "guinea_bissau",
                "guinea", "sierra_leone", "liberia", "ivory_coast", "ghana",
                "togo", "benin", "cape_verde", "madagascar", "mauritius",
                "seychelles", "comoros", "zambia", "malawi", "mozambique",
                "zimbabwe", "botswana", "namibia", "south_africa",
                "lesotho", "swaziland", "angola"
            ]
        }
    
    def create_backup(self) -> bool:
        """Create comprehensive backup of all data before migration."""
        try:
            self.logger.info(f"Creating backup at {self.backup_path}")
            
            # Create backup directory
            self.backup_path.mkdir(parents=True, exist_ok=True)
            
            backup_items = []
            total_size = 0
            
            # Backup project data
            if self.project_data_dir.exists():
                project_backup = self.backup_path / "projects"
                shutil.copytree(self.project_data_dir, project_backup)
                backup_items.append("projects")
                total_size += self._get_directory_size(project_backup)
            
            # Backup templates
            if self.templates_dir.exists():
                templates_backup = self.backup_path / "templates"
                shutil.copytree(self.templates_dir, templates_backup)
                backup_items.append("templates")
                total_size += self._get_directory_size(templates_backup)
            
            # Backup configuration
            if self.config_dir.exists():
                config_backup = self.backup_path / "config"
                shutil.copytree(self.config_dir, config_backup)
                backup_items.append("config")
                total_size += self._get_directory_size(config_backup)
            
            # Create backup manifest
            self.backup_manifest = {
                "migration_id": self.migration_id,
                "timestamp": datetime.now().isoformat(),
                "backup_items": backup_items,
                "total_size_mb": total_size / (1024 * 1024),
                "migration_config": asdict(self.config)
            }
            
            manifest_path = self.backup_path / "backup_manifest.json"
            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(self.backup_manifest, f, indent=2)
            
            self.stats.backup_size_mb = total_size / (1024 * 1024)
            self.logger.info(f"Backup created successfully ({self.stats.backup_size_mb:.2f} MB)")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Backup creation failed: {e}")
            return False
    
    def _get_directory_size(self, path: Path) -> int:
        """Calculate total size of directory in bytes."""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        return total_size
    
    def scan_projects(self) -> List[Dict[str, Any]]:
        """Scan existing projects for SIMEST usage and migration needs."""
        projects_to_migrate = []
        
        if not self.project_data_dir.exists():
            self.logger.warning("Project data directory not found")
            return projects_to_migrate
        
        try:
            for project_file in self.project_data_dir.glob("*.json"):
                self.stats.projects_scanned += 1
                
                try:
                    with open(project_file, 'r', encoding='utf-8') as f:
                        project_data = json.load(f)
                    
                    # Check for SIMEST usage
                    needs_migration = self._check_project_needs_migration(project_data)
                    
                    if needs_migration:
                        self.stats.projects_with_simest += 1
                        projects_to_migrate.append({
                            'file_path': str(project_file),
                            'project_data': project_data,
                            'migration_type': needs_migration
                        })
                        
                        self.logger.debug(f"Project {project_file.name} needs migration: {needs_migration}")
                
                except Exception as e:
                    self.logger.error(f"Error scanning project {project_file}: {e}")
                    self.stats.validation_errors.append(f"Scan error in {project_file}: {e}")
            
            self.logger.info(f"Scanned {self.stats.projects_scanned} projects, "
                           f"{self.stats.projects_with_simest} need migration")
            
            return projects_to_migrate
            
        except Exception as e:
            self.logger.error(f"Project scanning failed: {e}")
            raise SimestMigrationError(f"Project scanning error: {e}")
    
    def _check_project_needs_migration(self, project_data: Dict[str, Any]) -> Optional[str]:
        """Check if a project needs migration and determine migration type."""
        # Check for legacy fields
        has_italy_grant = project_data.get('grant_meur_italy', 0) > 0
        has_simest_grant = project_data.get('grant_meur_simest_africa', 0) > 0
        has_unified_grant = project_data.get('_grant_meur_piano_mattei_unified', 0) > 0
        
        # Check for new split structure fields
        has_split_structure = (
            'simest_total_facility_meur' in project_data or
            'simest_grant_meur' in project_data or
            'simest_soft_loan_meur' in project_data
        )
        
        if has_split_structure:
            return None  # Already migrated
        
        if has_italy_grant and has_simest_grant:
            return "consolidate_dual_fields"
        elif has_italy_grant:
            return "migrate_italy_grant"
        elif has_simest_grant:
            return "migrate_simest_grant"
        elif has_unified_grant:
            return "split_unified_grant"
        
        return None
    
    def migrate_project(self, project_info: Dict[str, Any]) -> bool:
        """Migrate a single project to SIMEST split structure."""
        try:
            project_data = project_info['project_data']
            migration_type = project_info['migration_type']
            file_path = project_info['file_path']
            
            self.logger.debug(f"Migrating project {file_path} (type: {migration_type})")
            
            # Create migrated project data
            migrated_data = project_data.copy()
            
            # Determine total SIMEST facility amount
            total_facility = self._calculate_total_simest_facility(project_data, migration_type)
            
            if total_facility <= 0:
                self.logger.warning(f"No SIMEST facility found in {file_path}")
                return True  # Not an error, just no SIMEST to migrate
            
            # Determine grant percentage (default to standard 10%)
            is_southern_italy = project_data.get('company_southern_italy', False)
            grant_percentage = (
                self.simest_config['grant_percentage_southern_italy'] 
                if is_southern_italy 
                else self.simest_config['grant_percentage_standard']
            )
            
            # Calculate split amounts
            grant_amount = total_facility * grant_percentage
            soft_loan_amount = total_facility * (1 - grant_percentage)
            
            # Update project data with split structure
            migrated_data.update({
                'simest_total_facility_meur': total_facility,
                'simest_grant_meur': grant_amount,
                'simest_soft_loan_meur': soft_loan_amount,
                'simest_grant_percentage': grant_percentage,
                'simest_soft_loan_interest_rate': self.simest_config['soft_loan_interest_rate'],
                'simest_soft_loan_tenor_years': self.simest_config['soft_loan_tenor_years'],
                'simest_soft_loan_grace_years': self.simest_config['soft_loan_grace_years'],
                '_grant_meur_piano_mattei_unified': grant_amount,  # Update unified field to grant only
                'migration_timestamp': datetime.now().isoformat(),
                'migration_id': self.migration_id,
                'migration_type': migration_type
            })
            
            # Remove legacy fields to prevent confusion
            legacy_fields = ['grant_meur_italy', 'grant_meur_simest_africa']
            for field in legacy_fields:
                migrated_data.pop(field, None)
            
            # Validate migrated data
            validation_errors = self._validate_migrated_project(migrated_data)
            if validation_errors:
                self.logger.error(f"Validation failed for {file_path}: {validation_errors}")
                self.stats.validation_errors.extend(validation_errors)
                return False
            
            # Write migrated data (if not dry run)
            if not self.config.dry_run:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(migrated_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Successfully migrated {file_path}: "
                           f"{total_facility:.2f} MEUR → "
                           f"{grant_amount:.2f} MEUR grant + "
                           f"{soft_loan_amount:.2f} MEUR soft loan")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Migration failed for {project_info['file_path']}: {e}")
            self.logger.debug(traceback.format_exc())
            return False
    
    def _calculate_total_simest_facility(self, project_data: Dict[str, Any], migration_type: str) -> float:
        """Calculate total SIMEST facility amount from legacy fields."""
        if migration_type == "consolidate_dual_fields":
            italy_grant = project_data.get('grant_meur_italy', 0)
            simest_grant = project_data.get('grant_meur_simest_africa', 0)
            return italy_grant + simest_grant
        
        elif migration_type == "migrate_italy_grant":
            return project_data.get('grant_meur_italy', 0)
        
        elif migration_type == "migrate_simest_grant":
            return project_data.get('grant_meur_simest_africa', 0)
        
        elif migration_type == "split_unified_grant":
            return project_data.get('_grant_meur_piano_mattei_unified', 0)
        
        return 0.0
    
    def _validate_migrated_project(self, project_data: Dict[str, Any]) -> List[str]:
        """Validate migrated project data for consistency and compliance."""
        errors = []
        
        try:
            # Check split structure consistency
            total_facility = project_data.get('simest_total_facility_meur', 0)
            grant_amount = project_data.get('simest_grant_meur', 0)
            soft_loan_amount = project_data.get('simest_soft_loan_meur', 0)
            
            if abs(total_facility - (grant_amount + soft_loan_amount)) > 0.001:
                errors.append(f"Split amounts don't sum to total: {total_facility} ≠ {grant_amount + soft_loan_amount}")
            
            # Check grant percentage compliance
            if total_facility > 0:
                grant_percentage = grant_amount / total_facility
                expected_percentages = [
                    self.simest_config['grant_percentage_standard'],
                    self.simest_config['grant_percentage_southern_italy']
                ]
                
                if not any(abs(grant_percentage - expected) < 0.001 for expected in expected_percentages):
                    errors.append(f"Invalid grant percentage: {grant_percentage:.1%}")
            
            # Check facility limits
            min_facility = self.simest_config.get('minimum_facility_meur', 0.1)
            max_facility = self.simest_config.get('maximum_facility_meur', 50.0)
            
            if total_facility < min_facility:
                errors.append(f"Facility below minimum: {total_facility} < {min_facility}")
            
            if total_facility > max_facility:
                errors.append(f"Facility above maximum: {total_facility} > {max_facility}")
            
            # Check project location eligibility
            project_location = project_data.get('project_location', '').lower()
            eligible_countries = self.simest_config.get('eligible_countries', [])
            
            if total_facility > 0 and project_location:
                location_eligible = any(
                    country in project_location or project_location in country
                    for country in eligible_countries
                )
                
                if not location_eligible:
                    errors.append(f"Project location '{project_location}' may not be eligible for SIMEST")
            
            # Check financial consistency
            capex = project_data.get('capex_meur', 0)
            if grant_amount > capex:
                errors.append(f"Grant amount exceeds CAPEX: {grant_amount} > {capex}")
            
        except Exception as e:
            errors.append(f"Validation error: {e}")
        
        return errors
    
    def migrate_templates(self) -> bool:
        """Migrate project templates to use SIMEST split structure."""
        try:
            if not self.templates_dir.exists():
                self.logger.warning("Templates directory not found")
                return True
            
            templates_migrated = 0
            
            for template_file in self.templates_dir.glob("*.json"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        template_data = json.load(f)
                    
                    # Check if template needs migration
                    if self._check_project_needs_migration(template_data):
                        self.logger.info(f"Migrating template {template_file.name}")
                        
                        # Apply migration logic
                        project_info = {
                            'project_data': template_data,
                            'migration_type': self._check_project_needs_migration(template_data),
                            'file_path': str(template_file)
                        }
                        
                        if self.migrate_project(project_info):
                            templates_migrated += 1
                        else:
                            self.logger.error(f"Failed to migrate template {template_file.name}")
                
                except Exception as e:
                    self.logger.error(f"Error migrating template {template_file}: {e}")
            
            self.logger.info(f"Migrated {templates_migrated} templates")
            return True
            
        except Exception as e:
            self.logger.error(f"Template migration failed: {e}")
            return False
    
    def validate_migration(self, projects_to_validate: List[Dict[str, Any]]) -> bool:
        """Validate migration results for consistency and integrity."""
        try:
            self.logger.info("Validating migration results...")
            
            validation_passed = True
            total_errors = 0
            
            for project_info in projects_to_validate:
                file_path = project_info['file_path']
                
                try:
                    # Load migrated project
                    with open(file_path, 'r', encoding='utf-8') as f:
                        migrated_data = json.load(f)
                    
                    # Validate split structure
                    errors = self._validate_migrated_project(migrated_data)
                    
                    if errors:
                        self.logger.error(f"Validation errors in {file_path}: {errors}")
                        self.stats.validation_errors.extend(errors)
                        validation_passed = False
                        total_errors += len(errors)
                    
                    # Validate financial consistency
                    original_data = project_info['project_data']
                    if not self._validate_financial_consistency(original_data, migrated_data):
                        validation_passed = False
                        total_errors += 1
                
                except Exception as e:
                    self.logger.error(f"Validation failed for {file_path}: {e}")
                    validation_passed = False
                    total_errors += 1
            
            if validation_passed:
                self.logger.info("Migration validation passed successfully")
            else:
                self.logger.error(f"Migration validation failed with {total_errors} errors")
            
            return validation_passed
            
        except Exception as e:
            self.logger.error(f"Migration validation error: {e}")
            return False
    
    def _validate_financial_consistency(self, original_data: Dict[str, Any], migrated_data: Dict[str, Any]) -> bool:
        """Validate that financial totals remain consistent after migration."""
        try:
            # Calculate original total grants
            original_italy = original_data.get('grant_meur_italy', 0)
            original_simest = original_data.get('grant_meur_simest_africa', 0)
            original_unified = original_data.get('_grant_meur_piano_mattei_unified', 0)
            original_total = max(original_italy + original_simest, original_unified)
            
            # Calculate migrated total facility
            migrated_facility = migrated_data.get('simest_total_facility_meur', 0)
            
            # They should be equal (facility amount equals original grant amount)
            if abs(original_total - migrated_facility) > 0.001:
                self.logger.error(f"Financial inconsistency: original {original_total} ≠ migrated {migrated_facility}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Financial consistency validation error: {e}")
            return False
    
    def rollback_migration(self, rollback_timestamp: Optional[str] = None) -> bool:
        """Rollback migration to previous state using backup."""
        try:
            # Determine backup to restore
            if rollback_timestamp:
                backup_dir = Path(self.config.backup_dir) / f"simest_migration_{rollback_timestamp}"
            else:
                backup_dir = self.backup_path
            
            if not backup_dir.exists():
                self.logger.error(f"Backup directory not found: {backup_dir}")
                return False
            
            # Load backup manifest
            manifest_path = backup_dir / "backup_manifest.json"
            if not manifest_path.exists():
                self.logger.error(f"Backup manifest not found: {manifest_path}")
                return False
            
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            self.logger.info(f"Rolling back migration {manifest['migration_id']} from {manifest['timestamp']}")
            
            # Restore backed up data
            rollback_success = True
            
            for item in manifest['backup_items']:
                source_path = backup_dir / item
                target_path = Path(item)
                
                try:
                    if target_path.exists():
                        if target_path.is_dir():
                            shutil.rmtree(target_path)
                        else:
                            target_path.unlink()
                    
                    if source_path.is_dir():
                        shutil.copytree(source_path, target_path)
                    else:
                        shutil.copy2(source_path, target_path)
                    
                    self.logger.info(f"Restored {item}")
                
                except Exception as e:
                    self.logger.error(f"Failed to restore {item}: {e}")
                    rollback_success = False
            
            if rollback_success:
                self.logger.info("Migration rollback completed successfully")
            else:
                self.logger.error("Migration rollback completed with errors")
            
            return rollback_success
            
        except Exception as e:
            self.logger.error(f"Rollback failed: {e}")
            return False
    
    def run_migration(self) -> bool:
        """Execute the complete SIMEST split structure migration."""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"Starting SIMEST split structure migration (ID: {self.migration_id})")
            
            # Load SIMEST configuration
            self.simest_config = self.load_simest_config()
            
            # Handle rollback request
            if self.config.force_rollback:
                return self.rollback_migration(self.config.rollback_timestamp)
            
            # Create backup
            if not self.create_backup():
                raise SimestMigrationError("Backup creation failed")
            
            # Scan projects for migration needs
            projects_to_migrate = self.scan_projects()
            
            if not projects_to_migrate:
                self.logger.info("No projects require migration")
                return True
            
            # Validation-only mode
            if self.config.validate_only:
                self.logger.info("Running validation-only mode")
                return self.validate_migration(projects_to_migrate)
            
            # Migrate projects
            self.logger.info(f"Migrating {len(projects_to_migrate)} projects...")
            
            for project_info in projects_to_migrate:
                if self.migrate_project(project_info):
                    self.stats.projects_migrated += 1
                else:
                    self.stats.projects_failed += 1
            
            # Migrate templates
            if not self.migrate_templates():
                self.logger.warning("Template migration failed")
            
            # Validate migration results
            if not self.validate_migration(projects_to_migrate):
                if not self.config.dry_run:
                    self.logger.error("Migration validation failed, consider rollback")
                    return False
            
            # Calculate migration duration
            self.stats.migration_duration_seconds = (datetime.now() - start_time).total_seconds()
            
            # Log final statistics
            self._log_migration_summary()
            
            if self.stats.projects_failed > 0:
                self.logger.warning(f"Migration completed with {self.stats.projects_failed} failures")
                return False
            
            self.logger.info("SIMEST split structure migration completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            self.logger.debug(traceback.format_exc())
            
            # Attempt automatic rollback on critical failure
            if not self.config.dry_run and self.backup_path.exists():
                self.logger.info("Attempting automatic rollback due to migration failure...")
                self.rollback_migration()
            
            return False
    
    def _log_migration_summary(self):
        """Log comprehensive migration summary."""
        summary = f"""
SIMEST Split Structure Migration Summary
========================================
Migration ID: {self.migration_id}
Duration: {self.stats.migration_duration_seconds:.2f} seconds
Backup Size: {self.stats.backup_size_mb:.2f} MB

Project Statistics:
- Projects Scanned: {self.stats.projects_scanned}
- Projects with SIMEST: {self.stats.projects_with_simest}
- Projects Migrated: {self.stats.projects_migrated}
- Projects Failed: {self.stats.projects_failed}

Validation Errors: {len(self.stats.validation_errors)}
"""
        
        if self.stats.validation_errors:
            summary += "\nValidation Errors:\n"
            for error in self.stats.validation_errors[:10]:  # Show first 10 errors
                summary += f"- {error}\n"
            
            if len(self.stats.validation_errors) > 10:
                summary += f"... and {len(self.stats.validation_errors) - 10} more errors\n"
        
        self.logger.info(summary)


def main():
    """Main entry point for SIMEST migration script."""
    parser = argparse.ArgumentParser(
        description="SIMEST Split Structure Migration Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Dry run migration (no changes made)
  python simest_split_structure_migration.py --dry-run
  
  # Validate existing migration
  python simest_split_structure_migration.py --validate-only
  
  # Run full migration
  python simest_split_structure_migration.py
  
  # Rollback migration
  python simest_split_structure_migration.py --rollback
  
  # Rollback to specific timestamp
  python simest_split_structure_migration.py --rollback --timestamp 20241201_143022
        """
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Run migration without making changes (preview mode)'
    )
    
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Only validate existing data without migration'
    )
    
    parser.add_argument(
        '--rollback',
        action='store_true',
        help='Rollback previous migration'
    )
    
    parser.add_argument(
        '--timestamp',
        type=str,
        help='Specific migration timestamp to rollback to'
    )
    
    parser.add_argument(
        '--backup-dir',
        type=str,
        default='backups/simest_migration',
        help='Directory for migration backups'
    )
    
    parser.add_argument(
        '--log-file',
        type=str,
        default='logs/simest_migration.log',
        help='Log file path'
    )
    
    args = parser.parse_args()
    
    # Create migration configuration
    config = MigrationConfig(
        backup_dir=args.backup_dir,
        log_file=args.log_file,
        dry_run=args.dry_run,
        validate_only=args.validate_only,
        force_rollback=args.rollback,
        rollback_timestamp=args.timestamp
    )
    
    # Run migration
    migration = SimestSplitStructureMigration(config)
    success = migration.run_migration()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()