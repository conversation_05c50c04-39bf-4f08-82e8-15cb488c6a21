"""
Piano Mattei-SIMEST Grant Unification Migration Script

This script handles the migration of existing project data where both <PERSON> (grant_meur_italy)
and SIMEST (grant_meur_simest_africa) grants might be populated separately. Since Piano Mattei budget
is managed through SIMEST, these should be unified to prevent double-counting.

Author: Renewable Energy Modeling Team
Date: 2025-01-26
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple
from datetime import datetime
import shutil

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PianoMatteiSimestMigration:
    """Handles migration of Piano Mattei and SIMEST grants to unified representation."""
    
    def __init__(self, backup_dir: str = "migrations/backups"):
        """Initialize migration with backup directory."""
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.migration_log = []
        
    def migrate_piano_mattei_simest_grants(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate Piano Mattei and SIMEST grants to unified representation.
        
        Args:
            project_data: Project data dictionary
            
        Returns:
            Migrated project data with unified grants
        """
        # Extract current grant values
        grant_italy = project_data.get('grant_meur_italy', 0.0)
        grant_simest = project_data.get('grant_meur_simest_africa', 0.0)
        
        # Check if migration is needed
        if grant_italy == 0.0 and grant_simest == 0.0:
            logger.info("No grants to migrate - both fields are zero")
            return project_data
        
        # Create migrated data copy
        migrated_data = project_data.copy()
        
        # Consolidate grants
        if grant_italy > 0 and grant_simest > 0:
            # Both fields populated - sum them
            unified_value = grant_italy + grant_simest
            migration_type = "CONSOLIDATION"
            message = f"Consolidated Piano Mattei ({grant_italy}) + SIMEST ({grant_simest}) = {unified_value} MEUR"
        elif grant_italy > 0:
            # Only Italian grant populated
            unified_value = grant_italy
            migration_type = "ITALY_ONLY"
            message = f"Migrated Piano Mattei grant: {unified_value} MEUR"
        else:
            # Only SIMEST populated
            unified_value = grant_simest
            migration_type = "SIMEST_ONLY"
            message = f"Migrated SIMEST grant to Piano Mattei: {unified_value} MEUR"
        
        # Update the data
        migrated_data['_grant_meur_piano_mattei_unified'] = unified_value
        migrated_data['grant_meur_italy'] = unified_value  # For backward compatibility
        migrated_data['grant_meur_simest_africa'] = 0.0   # Clear to prevent double-counting
        
        # Log the migration
        migration_entry = {
            'timestamp': datetime.now().isoformat(),
            'type': migration_type,
            'original_italy': grant_italy,
            'original_simest': grant_simest,
            'unified_value': unified_value,
            'message': message
        }
        self.migration_log.append(migration_entry)
        logger.info(message)
        
        return migrated_data
    
    def migrate_project_file(self, file_path: Path) -> bool:
        """
        Migrate a single project file.
        
        Args:
            file_path: Path to the project file
            
        Returns:
            True if migration was performed, False otherwise
        """
        try:
            # Create backup
            backup_path = self.backup_dir / f"{file_path.name}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(file_path, backup_path)
            logger.info(f"Created backup: {backup_path}")
            
            # Load project data
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            # Migrate the data
            migrated_data = self.migrate_piano_mattei_simest_grants(project_data)
            
            # Check if changes were made
            if migrated_data != project_data:
                # Save migrated data
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(migrated_data, f, indent=2, ensure_ascii=False)
                logger.info(f"Migrated file: {file_path}")
                return True
            else:
                logger.info(f"No migration needed for: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error migrating {file_path}: {e}")
            return False
    
    def migrate_directory(self, directory_path: Path, pattern: str = "*.json") -> Tuple[int, int]:
        """
        Migrate all project files in a directory.
        
        Args:
            directory_path: Directory containing project files
            pattern: File pattern to match
            
        Returns:
            Tuple of (files_migrated, total_files_processed)
        """
        files_migrated = 0
        total_files = 0
        
        for file_path in directory_path.glob(pattern):
            if file_path.is_file():
                total_files += 1
                if self.migrate_project_file(file_path):
                    files_migrated += 1
        
        return files_migrated, total_files
    
    def validate_migration(self, project_data: Dict[str, Any]) -> List[str]:
        """
        Validate that migration was successful.
        
        Args:
            project_data: Migrated project data
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check that unified field exists
        if '_grant_meur_piano_mattei_unified' not in project_data:
            errors.append("Missing unified grant field")
        
        # Check that SIMEST is zero to prevent double-counting
        if project_data.get('grant_meur_simest_africa', 0.0) != 0.0:
            errors.append("SIMEST field should be zero after migration")
        
        # Check that total grants calculation is consistent
        unified_value = project_data.get('_grant_meur_piano_mattei_unified', 0.0)
        italy_value = project_data.get('grant_meur_italy', 0.0)
        
        if unified_value != italy_value:
            errors.append(f"Unified value ({unified_value}) doesn't match Italy value ({italy_value})")
        
        return errors
    
    def rollback_migration(self, file_path: Path) -> bool:
        """
        Rollback migration by restoring from backup.
        
        Args:
            file_path: Path to the file to rollback
            
        Returns:
            True if rollback was successful, False otherwise
        """
        try:
            # Find the most recent backup
            backup_pattern = f"{file_path.name}.backup.*"
            backup_files = list(self.backup_dir.glob(backup_pattern))
            
            if not backup_files:
                logger.error(f"No backup found for {file_path}")
                return False
            
            # Get the most recent backup
            latest_backup = max(backup_files, key=lambda p: p.stat().st_mtime)
            
            # Restore from backup
            shutil.copy2(latest_backup, file_path)
            logger.info(f"Restored {file_path} from {latest_backup}")
            return True
            
        except Exception as e:
            logger.error(f"Error rolling back {file_path}: {e}")
            return False
    
    def generate_migration_report(self) -> str:
        """Generate a migration report."""
        if not self.migration_log:
            return "No migrations performed."
        
        report = ["Piano Mattei-SIMEST Grant Unification Migration Report"]
        report.append("=" * 60)
        report.append(f"Total migrations: {len(self.migration_log)}")
        report.append("")
        
        for entry in self.migration_log:
            report.append(f"Timestamp: {entry['timestamp']}")
            report.append(f"Type: {entry['type']}")
            report.append(f"Message: {entry['message']}")
            report.append(f"Original Italy: {entry['original_italy']} MEUR")
            report.append(f"Original SIMEST: {entry['original_simest']} MEUR")
            report.append(f"Unified Value: {entry['unified_value']} MEUR")
            report.append("-" * 40)
        
        return "\n".join(report)


def main():
    """Main migration function."""
    migration = PianoMatteiSimestMigration()
    
    # Migrate templates
    templates_dir = Path("config/templates")
    if templates_dir.exists():
        logger.info("Migrating templates...")
        migrated, total = migration.migrate_directory(templates_dir)
        logger.info(f"Templates: {migrated}/{total} files migrated")
    
    # Migrate any project files in data directory
    data_dir = Path("data")
    if data_dir.exists():
        logger.info("Migrating project data files...")
        migrated, total = migration.migrate_directory(data_dir)
        logger.info(f"Data files: {migrated}/{total} files migrated")
    
    # Generate and save migration report
    report = migration.generate_migration_report()
    report_path = migration.backup_dir / f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    logger.info(f"Migration complete. Report saved to: {report_path}")
    print(report)


if __name__ == "__main__":
    main()
