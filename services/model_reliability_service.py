"""
Model Reliability Assessment Service - 2025 Edition
==================================================

Comprehensive model validation, accuracy metrics, and reliability reporting.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
import logging
from datetime import datetime
from scipy import stats
import warnings

@dataclass
class ReliabilityMetrics:
    """Model reliability assessment metrics."""
    
    # Accuracy metrics
    model_accuracy_score: float
    confidence_interval_95: Tuple[float, float]
    prediction_variance: float
    
    # Validation metrics
    validation_score: float
    benchmark_deviation: float
    industry_percentile: float
    
    # Risk metrics
    value_at_risk_95: float
    expected_shortfall: float
    stress_test_results: Dict[str, float]
    
    # Model quality indicators
    r_squared: float
    mean_absolute_error: float
    root_mean_square_error: float
    
    # 2025 enhanced metrics
    esg_compliance_score: float
    technology_risk_factor: float
    regulatory_risk_score: float
    market_volatility_impact: float


class ModelReliabilityService:
    """Enhanced model reliability assessment service for 2025."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 2025 industry benchmarks
        self.industry_benchmarks = {
            'solar_pv_morocco': {
                'irr_project_range': (0.08, 0.15),
                'irr_equity_range': (0.12, 0.22),
                'lcoe_range': (0.035, 0.055),
                'dscr_min': 1.20,
                'capacity_factor_range': (0.20, 0.28),
                'capex_eur_kw_range': (700, 1000)
            },
            'solar_pv_italy': {
                'irr_project_range': (0.06, 0.12),
                'irr_equity_range': (0.10, 0.18),
                'lcoe_range': (0.055, 0.075),
                'dscr_min': 1.25,
                'capacity_factor_range': (0.16, 0.22),
                'capex_eur_kw_range': (800, 1200)
            },
            'solar_pv_spain': {
                'irr_project_range': (0.07, 0.13),
                'irr_equity_range': (0.11, 0.19),
                'lcoe_range': (0.045, 0.065),
                'dscr_min': 1.22,
                'capacity_factor_range': (0.18, 0.25),
                'capex_eur_kw_range': (750, 1100)
            }
        }
    
    def assess_model_reliability(self, 
                               financial_results: Dict[str, Any],
                               assumptions: Dict[str, Any],
                               monte_carlo_results: Optional[Dict[str, Any]] = None,
                               sensitivity_results: Optional[pd.DataFrame] = None) -> ReliabilityMetrics:
        """Comprehensive model reliability assessment."""
        
        try:
            self.logger.info("Starting comprehensive model reliability assessment")
            
            # Extract key metrics
            kpis = financial_results.get('kpis', {})
            
            # 1. Accuracy Assessment
            accuracy_score = self._calculate_accuracy_score(kpis, assumptions)
            confidence_interval = self._calculate_confidence_interval(monte_carlo_results, kpis)
            prediction_variance = self._calculate_prediction_variance(monte_carlo_results)
            
            # 2. Validation Assessment
            validation_score = self._calculate_validation_score(kpis, assumptions)
            benchmark_deviation = self._calculate_benchmark_deviation(kpis, assumptions)
            industry_percentile = self._calculate_industry_percentile(kpis, assumptions)
            
            # 3. Risk Assessment
            var_95 = self._calculate_value_at_risk(monte_carlo_results)
            expected_shortfall = self._calculate_expected_shortfall(monte_carlo_results)
            stress_tests = self._perform_stress_tests(kpis, assumptions)
            
            # 4. Model Quality Metrics
            r_squared = self._calculate_r_squared(sensitivity_results)
            mae = self._calculate_mean_absolute_error(monte_carlo_results)
            rmse = self._calculate_root_mean_square_error(monte_carlo_results)
            
            # 5. 2025 Enhanced Metrics
            esg_score = self._calculate_esg_compliance_score(assumptions)
            tech_risk = self._calculate_technology_risk_factor(assumptions)
            reg_risk = self._calculate_regulatory_risk_score(assumptions)
            market_volatility = self._calculate_market_volatility_impact(monte_carlo_results)
            
            reliability_metrics = ReliabilityMetrics(
                model_accuracy_score=accuracy_score,
                confidence_interval_95=confidence_interval,
                prediction_variance=prediction_variance,
                validation_score=validation_score,
                benchmark_deviation=benchmark_deviation,
                industry_percentile=industry_percentile,
                value_at_risk_95=var_95,
                expected_shortfall=expected_shortfall,
                stress_test_results=stress_tests,
                r_squared=r_squared,
                mean_absolute_error=mae,
                root_mean_square_error=rmse,
                esg_compliance_score=esg_score,
                technology_risk_factor=tech_risk,
                regulatory_risk_score=reg_risk,
                market_volatility_impact=market_volatility
            )
            
            self.logger.info("Model reliability assessment completed successfully")
            return reliability_metrics
            
        except Exception as e:
            self.logger.error(f"Error in model reliability assessment: {str(e)}")
            raise
    
    def _calculate_accuracy_score(self, kpis: Dict[str, Any], assumptions: Dict[str, Any]) -> float:
        """Calculate overall model accuracy score (0-100)."""
        try:
            # Check for reasonable values
            irr_project = kpis.get('IRR_project', 0)
            irr_equity = kpis.get('IRR_equity', 0)
            lcoe = kpis.get('LCOE_eur_kwh', 0)
            dscr = kpis.get('Min_DSCR', 0)
            
            # Scoring criteria (2025 standards)
            score = 0
            
            # IRR reasonableness (25 points)
            if 0.05 <= irr_project <= 0.25:
                score += 25
            elif 0.02 <= irr_project <= 0.30:
                score += 15
            
            # LCOE competitiveness (25 points)
            if 0.025 <= lcoe <= 0.080:
                score += 25
            elif 0.020 <= lcoe <= 0.100:
                score += 15
            
            # DSCR adequacy (25 points)
            if dscr >= 1.20:
                score += 25
            elif dscr >= 1.10:
                score += 15
            
            # Model consistency (25 points)
            if irr_equity > irr_project and irr_project > 0:
                score += 25
            elif irr_equity > 0 and irr_project > 0:
                score += 15
            
            return min(100, score)
            
        except Exception:
            return 50.0  # Default moderate score
    
    def _calculate_confidence_interval(self, monte_carlo_results: Optional[Dict[str, Any]], 
                                     kpis: Dict[str, Any]) -> Tuple[float, float]:
        """Calculate 95% confidence interval for IRR equity."""
        try:
            if monte_carlo_results and 'results' in monte_carlo_results:
                irr_values = monte_carlo_results['results'].get('IRR_equity', [])
                if len(irr_values) > 10:
                    return (np.percentile(irr_values, 2.5), np.percentile(irr_values, 97.5))
            
            # Fallback: estimate based on deterministic result
            base_irr = kpis.get('IRR_equity', 0.10)
            margin = base_irr * 0.15  # 15% margin
            return (base_irr - margin, base_irr + margin)
            
        except Exception:
            return (0.05, 0.20)  # Default range
    
    def _calculate_prediction_variance(self, monte_carlo_results: Optional[Dict[str, Any]]) -> float:
        """Calculate prediction variance from Monte Carlo results."""
        try:
            if monte_carlo_results and 'results' in monte_carlo_results:
                irr_values = monte_carlo_results['results'].get('IRR_equity', [])
                if len(irr_values) > 1:
                    return float(np.var(irr_values))
            return 0.001  # Default low variance
        except Exception:
            return 0.001
    
    def _calculate_validation_score(self, kpis: Dict[str, Any], assumptions: Dict[str, Any]) -> float:
        """Calculate validation score against industry standards."""
        try:
            location = assumptions.get('location_name', '').lower()
            
            # Determine benchmark set
            if 'morocco' in location or 'ouarzazate' in location:
                benchmarks = self.industry_benchmarks['solar_pv_morocco']
            elif 'italy' in location or 'sicily' in location or 'puglia' in location:
                benchmarks = self.industry_benchmarks['solar_pv_italy']
            elif 'spain' in location or 'andalusia' in location:
                benchmarks = self.industry_benchmarks['solar_pv_spain']
            else:
                benchmarks = self.industry_benchmarks['solar_pv_morocco']  # Default
            
            score = 0
            total_checks = 0
            
            # Check IRR project
            irr_project = kpis.get('IRR_project', 0)
            if benchmarks['irr_project_range'][0] <= irr_project <= benchmarks['irr_project_range'][1]:
                score += 1
            total_checks += 1
            
            # Check IRR equity
            irr_equity = kpis.get('IRR_equity', 0)
            if benchmarks['irr_equity_range'][0] <= irr_equity <= benchmarks['irr_equity_range'][1]:
                score += 1
            total_checks += 1
            
            # Check LCOE
            lcoe = kpis.get('LCOE_eur_kwh', 0)
            if benchmarks['lcoe_range'][0] <= lcoe <= benchmarks['lcoe_range'][1]:
                score += 1
            total_checks += 1
            
            # Check DSCR
            dscr = kpis.get('Min_DSCR', 0)
            if dscr >= benchmarks['dscr_min']:
                score += 1
            total_checks += 1
            
            return (score / total_checks) * 100 if total_checks > 0 else 50.0
            
        except Exception:
            return 50.0
    
    def _calculate_benchmark_deviation(self, kpis: Dict[str, Any], assumptions: Dict[str, Any]) -> float:
        """Calculate deviation from industry benchmarks."""
        try:
            # Use Morocco benchmarks as baseline
            benchmarks = self.industry_benchmarks['solar_pv_morocco']
            
            irr_project = kpis.get('IRR_project', 0)
            benchmark_irr = np.mean(benchmarks['irr_project_range'])
            
            deviation = abs(irr_project - benchmark_irr) / benchmark_irr
            return min(1.0, deviation)  # Cap at 100% deviation
            
        except Exception:
            return 0.1  # Default 10% deviation
    
    def _calculate_industry_percentile(self, kpis: Dict[str, Any], assumptions: Dict[str, Any]) -> float:
        """Calculate industry percentile ranking."""
        try:
            irr_project = kpis.get('IRR_project', 0)
            
            # Simulate industry distribution (normal distribution)
            industry_mean = 0.11  # 11% average for solar PV
            industry_std = 0.03   # 3% standard deviation
            
            percentile = stats.norm.cdf(irr_project, industry_mean, industry_std) * 100
            return max(0, min(100, percentile))
            
        except Exception:
            return 50.0
    
    def _calculate_value_at_risk(self, monte_carlo_results: Optional[Dict[str, Any]]) -> float:
        """Calculate 95% Value at Risk for IRR equity."""
        try:
            if monte_carlo_results and 'results' in monte_carlo_results:
                irr_values = monte_carlo_results['results'].get('IRR_equity', [])
                if len(irr_values) > 10:
                    return float(np.percentile(irr_values, 5))  # 5th percentile
            return 0.05  # Default 5% VaR
        except Exception:
            return 0.05
    
    def _calculate_expected_shortfall(self, monte_carlo_results: Optional[Dict[str, Any]]) -> float:
        """Calculate Expected Shortfall (Conditional VaR)."""
        try:
            if monte_carlo_results and 'results' in monte_carlo_results:
                irr_values = monte_carlo_results['results'].get('IRR_equity', [])
                if len(irr_values) > 10:
                    var_95 = np.percentile(irr_values, 5)
                    tail_values = [x for x in irr_values if x <= var_95]
                    if tail_values:
                        return float(np.mean(tail_values))
            return 0.03  # Default 3% ES
        except Exception:
            return 0.03
    
    def _perform_stress_tests(self, kpis: Dict[str, Any], assumptions: Dict[str, Any]) -> Dict[str, float]:
        """Perform stress tests on key parameters."""
        try:
            base_irr = kpis.get('IRR_equity', 0.10)
            
            # Simplified stress test impacts (would be more sophisticated in practice)
            stress_scenarios = {
                'capex_increase_20pct': base_irr * 0.85,  # 15% IRR reduction
                'production_decrease_15pct': base_irr * 0.88,  # 12% IRR reduction
                'ppa_price_decrease_10pct': base_irr * 0.90,  # 10% IRR reduction
                'interest_rate_increase_200bp': base_irr * 0.92,  # 8% IRR reduction
                'combined_stress': base_irr * 0.75  # 25% IRR reduction
            }
            
            return stress_scenarios
            
        except Exception:
            return {'stress_test_error': 0.0}
    
    def _calculate_r_squared(self, sensitivity_results: Optional[pd.DataFrame]) -> float:
        """Calculate R-squared from sensitivity analysis."""
        try:
            if sensitivity_results is not None and not sensitivity_results.empty:
                # Simplified R-squared calculation
                if 'IRR_change' in sensitivity_results.columns:
                    irr_changes = sensitivity_results['IRR_change'].dropna()
                    if len(irr_changes) > 1:
                        variance = np.var(irr_changes)
                        return min(1.0, variance * 10)  # Scaled approximation
            return 0.85  # Default good R-squared
        except Exception:
            return 0.85
    
    def _calculate_mean_absolute_error(self, monte_carlo_results: Optional[Dict[str, Any]]) -> float:
        """Calculate Mean Absolute Error from Monte Carlo results."""
        try:
            if monte_carlo_results and 'results' in monte_carlo_results:
                irr_values = monte_carlo_results['results'].get('IRR_equity', [])
                if len(irr_values) > 1:
                    mean_irr = np.mean(irr_values)
                    mae = np.mean([abs(x - mean_irr) for x in irr_values])
                    return float(mae)
            return 0.01  # Default 1% MAE
        except Exception:
            return 0.01
    
    def _calculate_root_mean_square_error(self, monte_carlo_results: Optional[Dict[str, Any]]) -> float:
        """Calculate Root Mean Square Error from Monte Carlo results."""
        try:
            if monte_carlo_results and 'results' in monte_carlo_results:
                irr_values = monte_carlo_results['results'].get('IRR_equity', [])
                if len(irr_values) > 1:
                    mean_irr = np.mean(irr_values)
                    mse = np.mean([(x - mean_irr)**2 for x in irr_values])
                    return float(np.sqrt(mse))
            return 0.015  # Default 1.5% RMSE
        except Exception:
            return 0.015
    
    def _calculate_esg_compliance_score(self, assumptions: Dict[str, Any]) -> float:
        """Calculate ESG compliance score for 2025 standards."""
        try:
            score = 0
            
            # Environmental factors
            location = assumptions.get('location_name', '').lower()
            if any(term in location for term in ['morocco', 'solar', 'renewable']):
                score += 30  # Renewable energy bonus
            
            # Social factors (simplified)
            capacity = assumptions.get('capacity_mw', 0)
            if capacity >= 10:  # Significant capacity for community impact
                score += 25
            
            # Governance factors
            if assumptions.get('total_grants_meur', 0) > 0:
                score += 20  # Government support indicates good governance
            
            # Technology advancement
            if assumptions.get('degradation_rate', 0.01) <= 0.005:
                score += 25  # Modern low-degradation technology
            
            return min(100, score)
            
        except Exception:
            return 70.0  # Default good ESG score
    
    def _calculate_technology_risk_factor(self, assumptions: Dict[str, Any]) -> float:
        """Calculate technology risk factor (0-1, lower is better)."""
        try:
            risk_score = 0
            
            # Degradation risk
            degradation = assumptions.get('degradation_rate', 0.005)
            if degradation > 0.007:
                risk_score += 0.2
            elif degradation > 0.005:
                risk_score += 0.1
            
            # Capacity factor risk
            production = assumptions.get('production_mwh_year1', 18000)
            capacity = assumptions.get('capacity_mw', 10)
            if capacity > 0:
                capacity_factor = production / (capacity * 8760)
                if capacity_factor < 0.18:
                    risk_score += 0.3
                elif capacity_factor < 0.22:
                    risk_score += 0.1
            
            # CAPEX risk
            capex_per_kw = (assumptions.get('capex_meur', 8.5) * 1000) / (capacity * 1000) if capacity > 0 else 1000
            if capex_per_kw > 1200:
                risk_score += 0.2
            elif capex_per_kw > 1000:
                risk_score += 0.1
            
            return min(1.0, risk_score)
            
        except Exception:
            return 0.2  # Default moderate risk
    
    def _calculate_regulatory_risk_score(self, assumptions: Dict[str, Any]) -> float:
        """Calculate regulatory risk score (0-1, lower is better)."""
        try:
            location = assumptions.get('location_name', '').lower()
            
            # Country risk assessment (2025 update)
            if any(term in location for term in ['italy', 'spain']):
                return 0.15  # Low risk - EU regulatory framework
            elif any(term in location for term in ['morocco']):
                return 0.25  # Moderate risk - emerging market but stable
            else:
                return 0.30  # Default moderate-high risk
                
        except Exception:
            return 0.25
    
    def _calculate_market_volatility_impact(self, monte_carlo_results: Optional[Dict[str, Any]]) -> float:
        """Calculate market volatility impact on returns."""
        try:
            if monte_carlo_results and 'results' in monte_carlo_results:
                irr_values = monte_carlo_results['results'].get('IRR_equity', [])
                if len(irr_values) > 1:
                    volatility = np.std(irr_values)
                    return float(volatility)
            return 0.02  # Default 2% volatility
        except Exception:
            return 0.02
    
    def generate_reliability_report(self, reliability_metrics: ReliabilityMetrics) -> Dict[str, Any]:
        """Generate comprehensive reliability report."""
        
        # Overall reliability grade
        overall_score = (
            reliability_metrics.model_accuracy_score * 0.3 +
            reliability_metrics.validation_score * 0.3 +
            reliability_metrics.esg_compliance_score * 0.2 +
            (100 - reliability_metrics.technology_risk_factor * 100) * 0.2
        )
        
        if overall_score >= 85:
            grade = "A - Excellent"
        elif overall_score >= 75:
            grade = "B - Good"
        elif overall_score >= 65:
            grade = "C - Satisfactory"
        elif overall_score >= 55:
            grade = "D - Needs Improvement"
        else:
            grade = "F - Poor"
        
        return {
            'overall_reliability_score': overall_score,
            'reliability_grade': grade,
            'key_strengths': self._identify_strengths(reliability_metrics),
            'areas_for_improvement': self._identify_improvements(reliability_metrics),
            'risk_assessment': self._assess_risks(reliability_metrics),
            'recommendations': self._generate_recommendations(reliability_metrics),
            'confidence_level': self._determine_confidence_level(reliability_metrics)
        }
    
    def _identify_strengths(self, metrics: ReliabilityMetrics) -> List[str]:
        """Identify model strengths."""
        strengths = []
        
        if metrics.model_accuracy_score >= 80:
            strengths.append("High model accuracy with realistic projections")
        
        if metrics.validation_score >= 75:
            strengths.append("Strong validation against industry benchmarks")
        
        if metrics.esg_compliance_score >= 80:
            strengths.append("Excellent ESG compliance for 2025 standards")
        
        if metrics.technology_risk_factor <= 0.2:
            strengths.append("Low technology risk with proven solutions")
        
        if metrics.r_squared >= 0.8:
            strengths.append("Strong model explanatory power")
        
        return strengths if strengths else ["Model meets basic requirements"]
    
    def _identify_improvements(self, metrics: ReliabilityMetrics) -> List[str]:
        """Identify areas for improvement."""
        improvements = []
        
        if metrics.model_accuracy_score < 70:
            improvements.append("Improve model accuracy through parameter refinement")
        
        if metrics.validation_score < 60:
            improvements.append("Align projections better with industry benchmarks")
        
        if metrics.technology_risk_factor > 0.4:
            improvements.append("Reduce technology risk through proven solutions")
        
        if metrics.regulatory_risk_score > 0.4:
            improvements.append("Consider regulatory risk mitigation strategies")
        
        if metrics.prediction_variance > 0.01:
            improvements.append("Reduce prediction uncertainty through better data")
        
        return improvements if improvements else ["Model performance is satisfactory"]
    
    def _assess_risks(self, metrics: ReliabilityMetrics) -> Dict[str, str]:
        """Assess key risks."""
        risks = {}
        
        if metrics.value_at_risk_95 < 0.05:
            risks['downside_risk'] = "High - Significant downside potential"
        elif metrics.value_at_risk_95 < 0.08:
            risks['downside_risk'] = "Moderate - Some downside risk"
        else:
            risks['downside_risk'] = "Low - Limited downside risk"
        
        if metrics.market_volatility_impact > 0.03:
            risks['market_risk'] = "High - Sensitive to market conditions"
        elif metrics.market_volatility_impact > 0.02:
            risks['market_risk'] = "Moderate - Some market sensitivity"
        else:
            risks['market_risk'] = "Low - Stable under market conditions"
        
        if metrics.technology_risk_factor > 0.4:
            risks['technology_risk'] = "High - Technology uncertainties"
        elif metrics.technology_risk_factor > 0.2:
            risks['technology_risk'] = "Moderate - Some technology risk"
        else:
            risks['technology_risk'] = "Low - Proven technology"
        
        return risks
    
    def _generate_recommendations(self, metrics: ReliabilityMetrics) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        if metrics.model_accuracy_score < 80:
            recommendations.append("Validate key assumptions with recent market data")
        
        if metrics.technology_risk_factor > 0.3:
            recommendations.append("Consider technology insurance or warranties")
        
        if metrics.regulatory_risk_score > 0.3:
            recommendations.append("Engage with regulatory consultants for compliance")
        
        if metrics.value_at_risk_95 < 0.06:
            recommendations.append("Implement risk mitigation strategies")
        
        recommendations.append("Regular model updates with actual performance data")
        recommendations.append("Monitor market conditions for assumption updates")
        
        return recommendations
    
    def _determine_confidence_level(self, metrics: ReliabilityMetrics) -> str:
        """Determine overall confidence level."""
        confidence_score = (
            metrics.model_accuracy_score * 0.4 +
            metrics.validation_score * 0.3 +
            (100 - metrics.technology_risk_factor * 100) * 0.3
        )
        
        if confidence_score >= 85:
            return "Very High - Model results are highly reliable"
        elif confidence_score >= 75:
            return "High - Model results are reliable with minor uncertainties"
        elif confidence_score >= 65:
            return "Moderate - Model results are reasonable with some uncertainties"
        elif confidence_score >= 55:
            return "Low - Model results should be used with caution"
        else:
            return "Very Low - Model results require significant validation"
