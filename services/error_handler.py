"""
Comprehensive Error Handler - 2025 Edition
==========================================

Robust error handling, fallback mechanisms, and user-friendly error messages.
"""

import logging
import traceback
import functools
from typing import Any, Callable, Dict, Optional, Union
from datetime import datetime
import flet as ft

class ErrorSeverity:
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ApplicationError(Exception):
    """Base application error with enhanced context."""
    
    def __init__(self, message: str, severity: str = ErrorSeverity.MEDIUM, 
                 context: Optional[Dict[str, Any]] = None, 
                 user_message: Optional[str] = None):
        super().__init__(message)
        self.severity = severity
        self.context = context or {}
        self.user_message = user_message or self._generate_user_message()
        self.timestamp = datetime.now()
    
    def _generate_user_message(self) -> str:
        """Generate user-friendly error message."""
        if self.severity == ErrorSeverity.CRITICAL:
            return "A critical error occurred. Please restart the application and contact support if the issue persists."
        elif self.severity == ErrorSeverity.HIGH:
            return "An error occurred while processing your request. Please try again or contact support."
        elif self.severity == ErrorSeverity.MEDIUM:
            return "Something went wrong. Please check your inputs and try again."
        else:
            return "A minor issue occurred. The operation may have completed partially."

class FinancialModelError(ApplicationError):
    """Financial model specific errors."""
    pass

class LocationServiceError(ApplicationError):
    """Location service specific errors."""
    pass

class ValidationError(ApplicationError):
    """Validation specific errors."""
    pass

class ExportError(ApplicationError):
    """Export service specific errors."""
    pass

class ErrorHandler:
    """Comprehensive error handler with logging and user feedback."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_count = 0
        self.error_history = []
        
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None,
                    show_user_message: bool = True, page: Optional[ft.Page] = None) -> Dict[str, Any]:
        """Handle errors with comprehensive logging and user feedback."""
        
        self.error_count += 1
        
        # Determine error type and severity
        if isinstance(error, ApplicationError):
            severity = error.severity
            user_message = error.user_message
            error_context = error.context
        else:
            severity = self._determine_severity(error)
            user_message = self._generate_fallback_message(error)
            error_context = context or {}
        
        # Create error information dictionary
        error_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'severity': severity,
            'context': error_context,
            'timestamp': datetime.now().isoformat(),
            'traceback': traceback.format_exc()
        }
        
        self.error_history.append(error_info)
        
        # Create logging context without 'message' key to avoid conflicts
        logging_context = {
            'error_type': error_info['error_type'],
            'severity': error_info['severity'],
            'context': error_info['context'],
            'timestamp': error_info['timestamp']
        }
        
        # Log based on severity
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"CRITICAL ERROR: {error}", extra=logging_context)
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(f"HIGH SEVERITY ERROR: {error}", extra=logging_context)
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"MEDIUM SEVERITY ERROR: {error}", extra=logging_context)
        else:
            self.logger.info(f"LOW SEVERITY ERROR: {error}", extra=logging_context)
        
        # Show user message if requested
        if show_user_message and page:
            self._show_user_error_message(page, user_message, severity)
        
        return error_info
    
    def _determine_severity(self, error: Exception) -> str:
        """Determine error severity based on error type."""
        critical_errors = (MemoryError, SystemError, KeyboardInterrupt)
        high_errors = (ValueError, TypeError, AttributeError, ImportError)
        medium_errors = (FileNotFoundError, PermissionError, ConnectionError)
        
        if isinstance(error, critical_errors):
            return ErrorSeverity.CRITICAL
        elif isinstance(error, high_errors):
            return ErrorSeverity.HIGH
        elif isinstance(error, medium_errors):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _generate_fallback_message(self, error: Exception) -> str:
        """Generate fallback user message for unhandled errors."""
        error_type = type(error).__name__
        
        fallback_messages = {
            'ValueError': "Invalid input values detected. Please check your data and try again.",
            'TypeError': "Data type mismatch occurred. Please verify your inputs.",
            'AttributeError': "A component is missing or incorrectly configured. Please restart the application.",
            'FileNotFoundError': "Required file not found. Please check file paths and permissions.",
            'PermissionError': "Permission denied. Please check file permissions or run as administrator.",
            'ConnectionError': "Network connection failed. Please check your internet connection.",
            'ImportError': "Required component not available. Please reinstall the application.",
            'KeyError': "Required data field missing. Please check your configuration.",
            'IndexError': "Data index out of range. Please verify your data structure."
        }
        
        return fallback_messages.get(error_type, 
                                   "An unexpected error occurred. Please try again or contact support.")
    
    def _show_user_error_message(self, page: ft.Page, message: str, severity: str):
        """Show user-friendly error message in the UI."""
        try:
            # Determine color based on severity
            color_map = {
                ErrorSeverity.CRITICAL: ft.Colors.RED_800,
                ErrorSeverity.HIGH: ft.Colors.RED_600,
                ErrorSeverity.MEDIUM: ft.Colors.ORANGE_600,
                ErrorSeverity.LOW: ft.Colors.BLUE_600
            }
            
            icon_map = {
                ErrorSeverity.CRITICAL: ft.Icons.ERROR,
                ErrorSeverity.HIGH: ft.Icons.WARNING,
                ErrorSeverity.MEDIUM: ft.Icons.INFO,
                ErrorSeverity.LOW: ft.Icons.NOTIFICATIONS
            }
            
            # Create error snackbar
            snackbar = ft.SnackBar(
                content=ft.Row([
                    ft.Icon(icon_map.get(severity, ft.Icons.INFO),
                           color=color_map.get(severity, ft.Colors.BLUE_600)),
                    ft.Text(message, expand=True, color=ft.Colors.WHITE)
                ]),
                bgcolor=color_map.get(severity, ft.Colors.BLUE_600),
                duration=5000 if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL] else 3000
            )
            
            page.overlay.append(snackbar)
            snackbar.open = True
            page.update()
            
        except Exception as e:
            # Fallback logging if UI update fails
            self.logger.error(f"Failed to show user error message: {e}")

def error_handler(severity: str = ErrorSeverity.MEDIUM, 
                 user_message: Optional[str] = None,
                 fallback_return: Any = None,
                 show_user_message: bool = True):
    """Decorator for automatic error handling."""
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Get error handler instance
                handler = ErrorHandler()
                
                # Extract page from args if available
                page = None
                for arg in args:
                    if hasattr(arg, 'page') and isinstance(arg.page, ft.Page):
                        page = arg.page
                        break
                
                # Handle error
                error_info = handler.handle_error(
                    e, 
                    context={'function': func.__name__, 'args': str(args)[:200]},
                    show_user_message=show_user_message,
                    page=page
                )
                
                # Return fallback value or re-raise based on severity
                if severity == ErrorSeverity.CRITICAL:
                    raise ApplicationError(
                        f"Critical error in {func.__name__}: {str(e)}", 
                        severity=severity,
                        user_message=user_message
                    )
                else:
                    return fallback_return
                    
        return wrapper
    return decorator

def safe_execute(func: Callable, *args, fallback=None, **kwargs) -> Any:
    """Safely execute a function with error handling."""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handler = ErrorHandler()
        handler.handle_error(e, context={'function': func.__name__})
        return fallback

class FallbackManager:
    """Manages fallback mechanisms for critical operations."""
    
    @staticmethod
    def get_fallback_financial_results() -> Dict[str, Any]:
        """Get fallback financial results when model fails."""
        return {
            'kpis': {
                'IRR_project': 0.10,
                'IRR_equity': 0.15,
                'NPV_project': 1000000,
                'NPV_equity': 500000,
                'LCOE_eur_kwh': 0.045,
                'Min_DSCR': 1.25,
                'Payback_years': 8.5,
                'Terminal_value': 2000000
            },
            'cashflow': {},
            'model_version': 'fallback_v1.0',
            'execution_time': datetime.now().isoformat(),
            'is_fallback': True
        }
    
    @staticmethod
    def get_fallback_location_data() -> Dict[str, Any]:
        """Get fallback location data when location service fails."""
        return {
            'name': 'Default Location',
            'production_mwh_year1': 18000,
            'capex_meur': 8.5,
            'opex_keuros_year1': 180,
            'ppa_price_eur_kwh': 0.045,
            'land_lease_eur_mw_year': 2000,
            'description': 'Fallback location data',
            'advantages': ['Reliable fallback'],
            'challenges': ['Limited data'],
            'irradiation_kwh_m2': 2200
        }
    
    @staticmethod
    def get_fallback_sensitivity_results() -> Dict[str, Any]:
        """Get fallback sensitivity analysis results."""
        import pandas as pd
        
        return pd.DataFrame({
            'Variable': ['production_mwh_year1', 'ppa_price_eur_kwh', 'capex_meur'],
            'Change_%': [0, 0, 0],
            'IRR_equity': [0.15, 0.15, 0.15],
            'IRR_change_%': [0, 0, 0],
            'NPV_equity': [500000, 500000, 500000],
            'LCOE': [0.045, 0.045, 0.045]
        })

# Global error handler instance
global_error_handler = ErrorHandler()

def setup_global_error_handling():
    """Setup global error handling for the application."""
    import sys
    
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        global_error_handler.handle_error(
            exc_value,
            context={'type': 'global_exception'},
            show_user_message=False
        )
    
    sys.excepthook = handle_exception
