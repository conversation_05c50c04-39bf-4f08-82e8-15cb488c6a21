"""
ML Prediction Service
====================

Machine learning predictions for financial modeling with risk assessment and optimization.
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Callable
from datetime import datetime
import logging
from dataclasses import dataclass
from enum import Enum

try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.linear_model import LinearRegression, Ridge
    from sklearn.svm import SVR
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split, cross_val_score, KFold
    from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


class PredictionTarget(Enum):
    """Prediction targets."""
    IRR_EQUITY = "irr_equity"
    IRR_PROJECT = "irr_project"
    NPV_EQUITY = "npv_equity"
    LCOE = "lcoe"
    RISK_SCORE = "risk_score"


@dataclass
class PredictionResult:
    """ML prediction result."""
    target: PredictionTarget
    predicted_value: float
    confidence_interval: Tuple[float, float]
    feature_contributions: Dict[str, float]
    model_confidence: float
    recommendations: List[str]


class FinancialDataGenerator:
    """Generate synthetic financial data for training."""
    
    def generate_training_data(self, n_samples: int = 1000) -> pd.DataFrame:
        """Generate synthetic training data based on realistic ranges with enhanced features."""
        np.random.seed(42)
        
        data = {
            'capacity_mw': np.random.uniform(5, 100, n_samples),
            'capex_eur_kw': np.random.uniform(800, 1500, n_samples),
            'capacity_factor': np.random.uniform(0.15, 0.35, n_samples),
            'ppa_price_eur_kwh': np.random.uniform(0.03, 0.08, n_samples),
            'debt_ratio': np.random.uniform(0.6, 0.85, n_samples),
            'interest_rate': np.random.uniform(0.04, 0.08, n_samples),
            'discount_rate': np.random.uniform(0.06, 0.12, n_samples),
            # Enhanced weather features
            'solar_irradiance_kwh_m2': np.random.uniform(1500, 2500, n_samples),
            'avg_temperature_c': np.random.uniform(15, 35, n_samples),
            'weather_volatility': np.random.uniform(0.05, 0.20, n_samples),
            # Regulatory features
            'regulatory_stability_score': np.random.uniform(0.5, 1.0, n_samples),
            'incentive_policy_score': np.random.uniform(0.3, 1.0, n_samples),
            'grid_connection_score': np.random.uniform(0.4, 1.0, n_samples),
            # Geographic features
            'country_risk_score': np.random.uniform(0.3, 0.9, n_samples),
            'market_maturity_score': np.random.uniform(0.2, 1.0, n_samples)
        }
        
        df = pd.DataFrame(data)
        
        # Calculate synthetic targets with enhanced features
        df['irr_equity'] = self._calculate_enhanced_synthetic_irr(df)
        df['npv_equity'] = self._calculate_enhanced_synthetic_npv(df)
        df['lcoe'] = self._calculate_enhanced_synthetic_lcoe(df)
        
        return df
    
    def _calculate_enhanced_synthetic_irr(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate enhanced synthetic IRR with weather and regulatory features."""
        # Base IRR calculation
        base_irr = 0.08 + (df['ppa_price_eur_kwh'] - 0.05) * 2
        base_irr += df['capacity_factor'] * 0.2
        base_irr -= df['debt_ratio'] * 0.05
        
        # Weather impact
        irradiance_impact = (df['solar_irradiance_kwh_m2'] - 2000) / 2000 * 0.03
        temp_impact = np.where(df['avg_temperature_c'] > 25, 
                              -(df['avg_temperature_c'] - 25) * 0.001, 0)
        weather_risk = -df['weather_volatility'] * 0.1
        
        # Regulatory impact
        regulatory_boost = df['regulatory_stability_score'] * 0.02
        incentive_boost = df['incentive_policy_score'] * 0.025
        grid_boost = df['grid_connection_score'] * 0.015
        
        # Geographic impact
        country_risk = -df['country_risk_score'] * 0.02
        market_maturity = df['market_maturity_score'] * 0.01
        
        enhanced_irr = (base_irr + irradiance_impact + temp_impact + weather_risk + 
                       regulatory_boost + incentive_boost + grid_boost + 
                       country_risk + market_maturity)
        
        noise = np.random.normal(0, 0.01, len(df))
        return np.clip(enhanced_irr + noise, 0.02, 0.30)
    
    def _calculate_enhanced_synthetic_npv(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate enhanced synthetic NPV with all features."""
        # Base NPV calculation
        revenue_factor = df['capacity_mw'] * df['capacity_factor'] * df['ppa_price_eur_kwh']
        cost_factor = df['capacity_mw'] * df['capex_eur_kw'] / 1000
        base_npv = revenue_factor * 100 - cost_factor
        
        # Weather enhancements
        irradiance_boost = (df['solar_irradiance_kwh_m2'] - 2000) / 2000 * base_npv * 0.1
        weather_penalty = -df['weather_volatility'] * base_npv * 0.05
        
        # Regulatory enhancements
        regulatory_boost = df['regulatory_stability_score'] * base_npv * 0.05
        incentive_boost = df['incentive_policy_score'] * base_npv * 0.08
        
        # Geographic impact
        country_penalty = -df['country_risk_score'] * base_npv * 0.03
        
        enhanced_npv = (base_npv + irradiance_boost + weather_penalty + 
                       regulatory_boost + incentive_boost + country_penalty)
        
        noise = np.random.normal(0, enhanced_npv.std() * 0.1, len(df))
        return enhanced_npv + noise
    
    def _calculate_enhanced_synthetic_lcoe(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate enhanced synthetic LCOE with all features."""
        # Base LCOE calculation
        base_lcoe = df['capex_eur_kw'] / (df['capacity_factor'] * 8760 * 25)
        base_lcoe += 0.01  # Add base O&M
        
        # Weather impact
        irradiance_reduction = (df['solar_irradiance_kwh_m2'] - 2000) / 2000 * -0.005
        weather_penalty = df['weather_volatility'] * 0.01
        
        # Regulatory impact
        regulatory_reduction = df['regulatory_stability_score'] * -0.003
        incentive_reduction = df['incentive_policy_score'] * -0.005
        
        # Geographic impact
        country_penalty = df['country_risk_score'] * 0.002
        
        enhanced_lcoe = (base_lcoe + irradiance_reduction + weather_penalty + 
                        regulatory_reduction + incentive_reduction + country_penalty)
        
        noise = np.random.normal(0, 0.002, len(df))
        return np.clip(enhanced_lcoe + noise, 0.015, 0.200)
    
    def _calculate_synthetic_irr(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate synthetic IRR."""
        base_irr = 0.08 + (df['ppa_price_eur_kwh'] - 0.05) * 2
        base_irr += df['capacity_factor'] * 0.2
        base_irr -= df['debt_ratio'] * 0.05
        
        noise = np.random.normal(0, 0.01, len(df))
        return np.clip(base_irr + noise, 0.05, 0.25)
    
    def _calculate_synthetic_npv(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate synthetic NPV."""
        revenue_factor = df['capacity_mw'] * df['capacity_factor'] * df['ppa_price_eur_kwh']
        cost_factor = df['capacity_mw'] * df['capex_eur_kw'] / 1000
        
        npv = revenue_factor * 100 - cost_factor
        noise = np.random.normal(0, npv.std() * 0.1, len(df))
        
        return npv + noise
    
    def _calculate_synthetic_lcoe(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate synthetic LCOE."""
        lcoe = df['capex_eur_kw'] / (df['capacity_factor'] * 8760 * 25)
        lcoe += 0.01  # Add base O&M
        
        noise = np.random.normal(0, 0.002, len(df))
        return np.clip(lcoe + noise, 0.02, 0.15)


class MLPredictionService:
    """ML prediction service for financial modeling."""
    
    def __init__(self, models_dir: str = "ml_models"):
        if not SKLEARN_AVAILABLE:
            self.logger = logging.getLogger(__name__)
            self.logger.warning("scikit-learn not available - ML predictions disabled")
            return
        
        self.logger = logging.getLogger(__name__)
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        self.models: Dict[PredictionTarget, Any] = {}
        self.scalers: Dict[PredictionTarget, StandardScaler] = {}
        self.feature_columns = [
            'capacity_mw', 'capex_eur_kw', 'capacity_factor',
            'ppa_price_eur_kwh', 'debt_ratio', 'interest_rate', 'discount_rate',
            # Enhanced weather features
            'solar_irradiance_kwh_m2', 'avg_temperature_c', 'weather_volatility',
            # Regulatory features
            'regulatory_stability_score', 'incentive_policy_score', 'grid_connection_score',
            # Geographic features
            'country_risk_score', 'market_maturity_score'
        ]
        
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize models with synthetic data."""
        if not SKLEARN_AVAILABLE:
            return
        
        try:
            generator = FinancialDataGenerator()
            training_data = generator.generate_training_data(1000)
            
            targets = [PredictionTarget.IRR_EQUITY, PredictionTarget.NPV_EQUITY, PredictionTarget.LCOE]
            
            for target in targets:
                self._train_model(training_data, target)
            
            self.logger.info("ML models initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize ML models: {e}")
    
    def _train_model(self, data: pd.DataFrame, target: PredictionTarget, 
                    progress_callback: Optional[Callable[[str, str, float, str], None]] = None):
        """Train model ensemble for specific target with cross-validation."""
        X = data[self.feature_columns]
        y = data[target.value]
        
        if progress_callback:
            progress_callback("ml_training", "model_training", 0, f"Preparing {target.value} training data")
        
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        if progress_callback:
            progress_callback("ml_training", "model_training", 20, f"Data scaled for {target.value}")
        
        # Model ensemble with different algorithms
        models = {
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'linear_regression': LinearRegression(),
            'ridge': Ridge(alpha=1.0),
            'svr': SVR(kernel='rbf', C=1.0)
        }
        
        # Cross-validation for model selection
        kfold = KFold(n_splits=5, shuffle=True, random_state=42)
        best_model = None
        best_score = -float('inf')
        model_scores = {}
        
        for i, (name, model) in enumerate(models.items()):
            try:
                if progress_callback:
                    progress_callback("ml_training", "model_training", 
                                    40 + (i / len(models)) * 40, 
                                    f"Cross-validating {name} for {target.value}")
                
                # Perform cross-validation
                cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=kfold, scoring='r2')
                mean_score = cv_scores.mean()
                model_scores[name] = {
                    'cv_score': mean_score,
                    'cv_std': cv_scores.std(),
                    'scores': cv_scores.tolist()
                }
                
                if mean_score > best_score:
                    best_score = mean_score
                    best_model = model
                    
            except Exception as e:
                self.logger.warning(f"Failed to train {name} for {target.value}: {e}")
                continue
        
        if best_model is None:
            # Fallback to RandomForest
            best_model = RandomForestRegressor(n_estimators=100, random_state=42)
        
        if progress_callback:
            progress_callback("ml_training", "model_training", 80, 
                            f"Training final {type(best_model).__name__} for {target.value}")
        
        # Train the best model on full training set
        best_model.fit(X_train_scaled, y_train)
        
        if progress_callback:
            progress_callback("ml_training", "model_training", 90, 
                            f"Evaluating {target.value} model performance")
        
        # Test set evaluation
        y_pred = best_model.predict(X_test_scaled)
        r2 = r2_score(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        
        self.models[target] = best_model
        self.scalers[target] = scaler
        
        # Store model evaluation metrics
        if not hasattr(self, 'model_metrics'):
            self.model_metrics = {}
        
        self.model_metrics[target] = {
            'cv_scores': model_scores,
            'best_model': type(best_model).__name__,
            'test_r2': r2,
            'test_mae': mae,
            'test_rmse': rmse
        }
        
        if progress_callback:
            progress_callback("ml_training", "model_training", 100, 
                            f"Completed {target.value} model - R²: {r2:.3f}")
        
        self.logger.info(f"Trained {target.value} model - Best: {type(best_model).__name__}, CV R²: {best_score:.3f}, Test R²: {r2:.3f}")
    
    def predict(self, assumptions: Dict[str, Any], target: PredictionTarget) -> PredictionResult:
        """Make prediction for given assumptions."""
        if not SKLEARN_AVAILABLE or target not in self.models:
            # Return fallback prediction
            return PredictionResult(
                target=target,
                predicted_value=0.12 if target == PredictionTarget.IRR_EQUITY else 1000000,
                confidence_interval=(0.10, 0.14),
                feature_contributions={},
                model_confidence=0.5,
                recommendations=["ML models not available - using fallback values"]
            )
        
        input_data = self._prepare_input_data(assumptions)
        model = self.models[target]
        scaler = self.scalers[target]
        
        input_scaled = scaler.transform([input_data])
        prediction = model.predict(input_scaled)[0]
        
        # Simple confidence interval
        confidence_interval = (prediction * 0.9, prediction * 1.1)
        
        # Feature contributions
        if hasattr(model, 'feature_importances_'):
            contributions = dict(zip(self.feature_columns, model.feature_importances_))
        else:
            contributions = {}
        
        recommendations = self._generate_recommendations(target, prediction)
        
        return PredictionResult(
            target=target,
            predicted_value=prediction,
            confidence_interval=confidence_interval,
            feature_contributions=contributions,
            model_confidence=0.8,
            recommendations=recommendations
        )
    
    def _prepare_input_data(self, assumptions: Dict[str, Any]) -> List[float]:
        """Prepare input data for prediction with enhanced features."""
        # Basic features
        basic_features = {
            'capacity_mw': assumptions.get('capacity_mw', 10.0),
            'capex_eur_kw': (assumptions.get('capex_meur', 8.5) * 1000) / assumptions.get('capacity_mw', 10.0),
            'capacity_factor': assumptions.get('production_mwh_year1', 18000) / (assumptions.get('capacity_mw', 10.0) * 8760),
            'ppa_price_eur_kwh': assumptions.get('ppa_price_eur_kwh', 0.05),
            'debt_ratio': assumptions.get('debt_ratio', 0.75),
            'interest_rate': assumptions.get('interest_rate', 0.06),
            'discount_rate': assumptions.get('discount_rate', 0.08)
        }
        
        # Enhanced features
        enhanced_features = self._extract_enhanced_features(assumptions)
        
        # Combine all features
        all_features = {**basic_features, **enhanced_features}
        
        return [all_features.get(feature, 0.0) for feature in self.feature_columns]
    
    def _extract_enhanced_features(self, assumptions: Dict[str, Any]) -> Dict[str, float]:
        """Extract enhanced features from assumptions."""
        location_name = assumptions.get('location_name', '').lower()
        
        # Weather features based on location
        weather_features = self._get_weather_features(location_name)
        
        # Regulatory features based on location
        regulatory_features = self._get_regulatory_features(location_name)
        
        # Geographic features
        geographic_features = self._get_geographic_features(location_name)
        
        return {**weather_features, **regulatory_features, **geographic_features}
    
    def _get_weather_features(self, location: str) -> Dict[str, float]:
        """Get weather features based on location."""
        # Weather data mapping for different locations
        weather_data = {
            'morocco': {
                'solar_irradiance_kwh_m2': 2200,
                'avg_temperature_c': 22,
                'weather_volatility': 0.08
            },
            'ouarzazate': {
                'solar_irradiance_kwh_m2': 2400,
                'avg_temperature_c': 25,
                'weather_volatility': 0.06
            },
            'italy': {
                'solar_irradiance_kwh_m2': 1650,
                'avg_temperature_c': 18,
                'weather_volatility': 0.12
            },
            'sicily': {
                'solar_irradiance_kwh_m2': 1800,
                'avg_temperature_c': 20,
                'weather_volatility': 0.10
            },
            'spain': {
                'solar_irradiance_kwh_m2': 1750,
                'avg_temperature_c': 19,
                'weather_volatility': 0.11
            },
            'andalusia': {
                'solar_irradiance_kwh_m2': 1900,
                'avg_temperature_c': 21,
                'weather_volatility': 0.09
            }
        }
        
        # Find best match for location
        for key, data in weather_data.items():
            if key in location:
                return data
        
        # Default values
        return {
            'solar_irradiance_kwh_m2': 1800,
            'avg_temperature_c': 20,
            'weather_volatility': 0.10
        }
    
    def _get_regulatory_features(self, location: str) -> Dict[str, float]:
        """Get regulatory features based on location."""
        # Regulatory data mapping
        regulatory_data = {
            'morocco': {
                'regulatory_stability_score': 0.75,
                'incentive_policy_score': 0.80,
                'grid_connection_score': 0.70
            },
            'italy': {
                'regulatory_stability_score': 0.85,
                'incentive_policy_score': 0.70,
                'grid_connection_score': 0.90
            },
            'spain': {
                'regulatory_stability_score': 0.90,
                'incentive_policy_score': 0.75,
                'grid_connection_score': 0.95
            },
            'france': {
                'regulatory_stability_score': 0.95,
                'incentive_policy_score': 0.60,
                'grid_connection_score': 0.95
            }
        }
        
        # Find best match for location
        for key, data in regulatory_data.items():
            if key in location:
                return data
        
        # Default values
        return {
            'regulatory_stability_score': 0.70,
            'incentive_policy_score': 0.60,
            'grid_connection_score': 0.75
        }
    
    def _get_geographic_features(self, location: str) -> Dict[str, float]:
        """Get geographic features based on location."""
        # Geographic risk data
        geographic_data = {
            'morocco': {
                'country_risk_score': 0.65,
                'market_maturity_score': 0.70
            },
            'italy': {
                'country_risk_score': 0.15,
                'market_maturity_score': 0.90
            },
            'spain': {
                'country_risk_score': 0.10,
                'market_maturity_score': 0.95
            },
            'france': {
                'country_risk_score': 0.05,
                'market_maturity_score': 0.95
            },
            'germany': {
                'country_risk_score': 0.05,
                'market_maturity_score': 1.00
            }
        }
        
        # Find best match for location
        for key, data in geographic_data.items():
            if key in location:
                return data
        
        # Default values
        return {
            'country_risk_score': 0.30,
            'market_maturity_score': 0.60
        }
    
    def _generate_recommendations(self, target: PredictionTarget, prediction: float) -> List[str]:
        """Generate recommendations based on prediction."""
        recommendations = []
        
        if target == PredictionTarget.IRR_EQUITY:
            if prediction < 0.10:
                recommendations.append("Consider increasing PPA price or reducing CAPEX")
            elif prediction > 0.20:
                recommendations.append("Excellent returns - verify assumptions")
        
        return recommendations


    def predict_multiple(self, assumptions: Dict[str, Any], 
                        targets: List[PredictionTarget] = None) -> Dict[PredictionTarget, PredictionResult]:
        """Make predictions for multiple targets."""
        if targets is None:
            targets = [PredictionTarget.IRR_EQUITY, PredictionTarget.NPV_EQUITY, PredictionTarget.LCOE]
        
        results = {}
        for target in targets:
            try:
                results[target] = self.predict(assumptions, target)
            except Exception as e:
                self.logger.error(f"Failed to predict {target.value}: {e}")
                # Add fallback result
                results[target] = self._get_fallback_prediction(target)
        
        return results
    
    def optimize_parameters(self, base_assumptions: Dict[str, Any], 
                           target: PredictionTarget, target_value: float,
                           variable_params: List[str] = None) -> Dict[str, Any]:
        """Optimize parameters to achieve target value."""
        if not SKLEARN_AVAILABLE or target not in self.models:
            return base_assumptions.copy()
        
        if variable_params is None:
            variable_params = ['capacity_mw', 'ppa_price_eur_kwh', 'capex_eur_kw']
        
        best_assumptions = base_assumptions.copy()
        best_diff = float('inf')
        
        # Simple grid search optimization
        for param in variable_params:
            original_value = base_assumptions.get(param, 0)
            
            # Try different values around the original
            test_values = self._get_parameter_range(param, original_value)
            
            for test_value in test_values:
                test_assumptions = base_assumptions.copy()
                test_assumptions[param] = test_value
                
                try:
                    prediction = self.predict(test_assumptions, target)
                    diff = abs(prediction.predicted_value - target_value)
                    
                    if diff < best_diff:
                        best_diff = diff
                        best_assumptions = test_assumptions.copy()
                        
                except Exception:
                    continue
        
        return best_assumptions
    
    def sensitivity_analysis(self, base_assumptions: Dict[str, Any], 
                           target: PredictionTarget, variation_pct: float = 0.1) -> Dict[str, float]:
        """Perform sensitivity analysis on prediction."""
        if not SKLEARN_AVAILABLE or target not in self.models:
            return {}
        
        base_prediction = self.predict(base_assumptions, target)
        sensitivities = {}
        
        for param in self.feature_columns:
            if param in base_assumptions:
                original_value = base_assumptions[param]
                
                # Test positive variation
                test_assumptions = base_assumptions.copy()
                test_assumptions[param] = original_value * (1 + variation_pct)
                
                try:
                    high_prediction = self.predict(test_assumptions, target)
                    sensitivity = (high_prediction.predicted_value - base_prediction.predicted_value) / (original_value * variation_pct)
                    sensitivities[param] = sensitivity
                except Exception:
                    sensitivities[param] = 0.0
        
        return sensitivities
    
    def risk_assessment(self, assumptions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk factors for the project."""
        risk_factors = {}
        
        # Price risk
        ppa_price = assumptions.get('ppa_price_eur_kwh', 0.05)
        if ppa_price < 0.04:
            risk_factors['price_risk'] = 'HIGH - Very low PPA price'
        elif ppa_price < 0.05:
            risk_factors['price_risk'] = 'MEDIUM - Below average PPA price'
        else:
            risk_factors['price_risk'] = 'LOW - Good PPA price'
        
        # Technology risk
        capacity_factor = assumptions.get('capacity_factor', 0.25)
        if capacity_factor < 0.2:
            risk_factors['technology_risk'] = 'HIGH - Low capacity factor'
        elif capacity_factor < 0.25:
            risk_factors['technology_risk'] = 'MEDIUM - Average capacity factor'
        else:
            risk_factors['technology_risk'] = 'LOW - High capacity factor'
        
        # Financial risk
        debt_ratio = assumptions.get('debt_ratio', 0.75)
        if debt_ratio > 0.8:
            risk_factors['financial_risk'] = 'HIGH - Very high leverage'
        elif debt_ratio > 0.75:
            risk_factors['financial_risk'] = 'MEDIUM - High leverage'
        else:
            risk_factors['financial_risk'] = 'LOW - Conservative leverage'
        
        # Market risk
        capex = assumptions.get('capex_eur_kw', 1200)
        if capex > 1400:
            risk_factors['market_risk'] = 'HIGH - High CAPEX vs market'
        elif capex > 1200:
            risk_factors['market_risk'] = 'MEDIUM - Above average CAPEX'
        else:
            risk_factors['market_risk'] = 'LOW - Competitive CAPEX'
        
        # Overall risk score
        risk_scores = {'LOW': 1, 'MEDIUM': 2, 'HIGH': 3}
        avg_risk = sum(risk_scores.get(risk.split(' - ')[0], 2) for risk in risk_factors.values()) / len(risk_factors)
        
        if avg_risk <= 1.5:
            overall_risk = 'LOW'
        elif avg_risk <= 2.5:
            overall_risk = 'MEDIUM'
        else:
            overall_risk = 'HIGH'
        
        return {
            'risk_factors': risk_factors,
            'overall_risk': overall_risk,
            'risk_score': avg_risk,
            'recommendations': self._get_risk_recommendations(risk_factors)
        }
    
    def benchmark_comparison(self, assumptions: Dict[str, Any]) -> Dict[str, Any]:
        """Compare project metrics against industry benchmarks."""
        benchmarks = {
            'capacity_factor': {'excellent': 0.3, 'good': 0.25, 'average': 0.2},
            'capex_eur_kw': {'excellent': 1000, 'good': 1200, 'average': 1400},
            'ppa_price_eur_kwh': {'excellent': 0.06, 'good': 0.05, 'average': 0.04},
            'debt_ratio': {'excellent': 0.7, 'good': 0.75, 'average': 0.8}
        }
        
        comparisons = {}
        for param, thresholds in benchmarks.items():
            value = assumptions.get(param, 0)
            
            if param == 'capex_eur_kw':  # Lower is better for CAPEX
                if value <= thresholds['excellent']:
                    rating = 'EXCELLENT'
                elif value <= thresholds['good']:
                    rating = 'GOOD'
                elif value <= thresholds['average']:
                    rating = 'AVERAGE'
                else:
                    rating = 'BELOW_AVERAGE'
            else:  # Higher is better for other metrics
                if value >= thresholds['excellent']:
                    rating = 'EXCELLENT'
                elif value >= thresholds['good']:
                    rating = 'GOOD'
                elif value >= thresholds['average']:
                    rating = 'AVERAGE'
                else:
                    rating = 'BELOW_AVERAGE'
            
            comparisons[param] = {
                'value': value,
                'rating': rating,
                'benchmarks': thresholds
            }
        
        return comparisons
    
    def get_model_statistics(self) -> Dict[str, Any]:
        """Get ML model performance statistics."""
        if not SKLEARN_AVAILABLE:
            return {'status': 'ML models not available'}
        
        stats = {
            'available_models': [target.value for target in self.models.keys()],
            'feature_columns': self.feature_columns,
            'model_types': {target.value: type(model).__name__ for target, model in self.models.items()},
            'sklearn_available': SKLEARN_AVAILABLE,
            'model_metrics': getattr(self, 'model_metrics', {}),
            'last_training_time': getattr(self, 'last_training_time', None),
            'performance_feedback_count': getattr(self, 'performance_feedback_count', 0)
        }
        
        return stats
    
    def add_performance_feedback(self, project_id: str, predicted_values: Dict[str, float], 
                               actual_values: Dict[str, float], project_assumptions: Dict[str, Any]):
        """Add performance feedback for real-time learning."""
        try:
            if not hasattr(self, 'performance_feedback'):
                self.performance_feedback = []
            
            # Prepare feedback record
            feedback_record = {
                'project_id': project_id,
                'timestamp': datetime.now().isoformat(),
                'predicted_values': predicted_values,
                'actual_values': actual_values,
                'project_assumptions': project_assumptions,
                'prediction_errors': {}
            }
            
            # Calculate prediction errors
            for target in ['irr_equity', 'npv_equity', 'lcoe']:
                if target in predicted_values and target in actual_values:
                    error = abs(predicted_values[target] - actual_values[target])
                    relative_error = error / abs(actual_values[target]) if actual_values[target] != 0 else 0
                    feedback_record['prediction_errors'][target] = {
                        'absolute_error': error,
                        'relative_error': relative_error
                    }
            
            self.performance_feedback.append(feedback_record)
            
            # Update feedback count
            self.performance_feedback_count = getattr(self, 'performance_feedback_count', 0) + 1
            
            # Trigger automatic retraining if enough feedback collected
            if self.performance_feedback_count % 10 == 0:  # Retrain every 10 feedback entries
                self.logger.info(f"Triggering automatic retraining after {self.performance_feedback_count} feedback entries")
                self._automatic_retraining()
            
            self.logger.info(f"Added performance feedback for project {project_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to add performance feedback: {e}")
    
    def _automatic_retraining(self):
        """Automatically retrain models with performance feedback."""
        try:
            if not hasattr(self, 'performance_feedback') or len(self.performance_feedback) < 5:
                self.logger.info("Not enough feedback data for retraining")
                return
            
            # Convert feedback to training data
            feedback_data = self._convert_feedback_to_training_data()
            
            if len(feedback_data) > 0:
                self.logger.info(f"Retraining models with {len(feedback_data)} feedback samples")
                
                # Retrain with combined data (feedback + historical + synthetic)
                self.retrain_models(feedback_data, include_historical=True)
                
                # Update last training time
                self.last_training_time = datetime.now().isoformat()
                
                # Clear old feedback to prevent memory growth
                if len(self.performance_feedback) > 100:
                    self.performance_feedback = self.performance_feedback[-50:]  # Keep last 50 entries
                
                self.logger.info("Automatic retraining completed successfully")
            
        except Exception as e:
            self.logger.error(f"Automatic retraining failed: {e}")
    
    def _convert_feedback_to_training_data(self) -> pd.DataFrame:
        """Convert performance feedback to training data format."""
        try:
            feedback_records = []
            
            for feedback in self.performance_feedback:
                try:
                    assumptions = feedback['project_assumptions']
                    actual_values = feedback['actual_values']
                    
                    # Basic features
                    basic_features = {
                        'capacity_mw': assumptions.get('capacity_mw', 10.0),
                        'capex_eur_kw': (assumptions.get('capex_meur', 8.5) * 1000) / assumptions.get('capacity_mw', 10.0),
                        'capacity_factor': assumptions.get('production_mwh_year1', 18000) / (assumptions.get('capacity_mw', 10.0) * 8760),
                        'ppa_price_eur_kwh': assumptions.get('ppa_price_eur_kwh', 0.05),
                        'debt_ratio': assumptions.get('debt_ratio', 0.75),
                        'interest_rate': assumptions.get('interest_rate', 0.06),
                        'discount_rate': assumptions.get('discount_rate', 0.08)
                    }
                    
                    # Enhanced features
                    enhanced_features = self._extract_enhanced_features(assumptions)
                    
                    # Combine all features
                    record = {**basic_features, **enhanced_features}
                    
                    # Add actual target values
                    record['irr_equity'] = actual_values.get('irr_equity', 0)
                    record['npv_equity'] = actual_values.get('npv_equity', 0)
                    record['lcoe'] = actual_values.get('lcoe', 0)
                    
                    # Validate record
                    if self._validate_historical_record(record):
                        feedback_records.append(record)
                        
                except Exception as e:
                    self.logger.warning(f"Failed to convert feedback record: {e}")
                    continue
            
            if feedback_records:
                return pd.DataFrame(feedback_records)
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Failed to convert feedback to training data: {e}")
            return pd.DataFrame()
    
    def get_prediction_accuracy_metrics(self) -> Dict[str, Any]:
        """Get prediction accuracy metrics from performance feedback."""
        try:
            if not hasattr(self, 'performance_feedback') or len(self.performance_feedback) == 0:
                return {'status': 'No performance feedback available'}
            
            metrics = {}
            
            # Calculate accuracy metrics for each target
            for target in ['irr_equity', 'npv_equity', 'lcoe']:
                errors = []
                relative_errors = []
                
                for feedback in self.performance_feedback:
                    if target in feedback.get('prediction_errors', {}):
                        error_data = feedback['prediction_errors'][target]
                        errors.append(error_data['absolute_error'])
                        relative_errors.append(error_data['relative_error'])
                
                if errors:
                    metrics[target] = {
                        'count': len(errors),
                        'mean_absolute_error': np.mean(errors),
                        'median_absolute_error': np.median(errors),
                        'mean_relative_error': np.mean(relative_errors),
                        'median_relative_error': np.median(relative_errors),
                        'max_error': np.max(errors),
                        'min_error': np.min(errors)
                    }
            
            metrics['total_feedback_count'] = len(self.performance_feedback)
            metrics['last_feedback_time'] = self.performance_feedback[-1]['timestamp'] if self.performance_feedback else None
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to calculate accuracy metrics: {e}")
            return {'error': str(e)}
    
    def schedule_periodic_retraining(self, interval_hours: int = 24):
        """Schedule periodic retraining (placeholder for production implementation)."""
        # In a production environment, this would integrate with a scheduler like Celery
        # For now, we'll just log the intention
        self.logger.info(f"Periodic retraining scheduled every {interval_hours} hours")
        self.retraining_interval = interval_hours
    
    def retrain_models(self, new_data: pd.DataFrame = None, include_historical: bool = True,
                      progress_callback: Optional[Callable[[str, str, float, str], None]] = None):
        """Retrain models with new data including historical project outcomes."""
        if not SKLEARN_AVAILABLE:
            self.logger.warning("Cannot retrain models - scikit-learn not available")
            return
        
        try:
            # Progress tracking
            if progress_callback:
                progress_callback("ml_training", "prepare_data", 0, "Starting data preparation")
            
            # Combine synthetic and historical data
            combined_data = self._prepare_training_data(new_data, include_historical)
            
            if progress_callback:
                progress_callback("ml_training", "prepare_data", 100, f"Prepared {len(combined_data)} training samples")
            
            targets = [PredictionTarget.IRR_EQUITY, PredictionTarget.NPV_EQUITY, PredictionTarget.LCOE]
            
            for i, target in enumerate(targets):
                try:
                    if progress_callback:
                        progress_callback("ml_training", "model_training", 
                                        (i / len(targets)) * 100, 
                                        f"Training {target.value} model")
                    
                    self._train_model(combined_data, target, progress_callback)
                    
                    if progress_callback:
                        progress_callback("ml_training", "model_training", 
                                        ((i + 1) / len(targets)) * 100, 
                                        f"Completed {target.value} model training")
                    
                    self.logger.info(f"Retrained model for {target.value}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to retrain model for {target.value}: {e}")
                    if progress_callback:
                        progress_callback("ml_training", "model_training", 
                                        ((i + 1) / len(targets)) * 100, 
                                        f"Failed to train {target.value} model")
            
            if progress_callback:
                progress_callback("ml_training", "save_models", 100, "Models saved successfully")
                
        except Exception as e:
            self.logger.error(f"Error in model retraining: {e}")
            if progress_callback:
                progress_callback("ml_training", "model_training", 0, f"Training failed: {str(e)}")
    
    def _prepare_training_data(self, new_data: pd.DataFrame = None, include_historical: bool = True) -> pd.DataFrame:
        """Prepare training data by combining synthetic and historical data."""
        datasets = []
        
        # Generate synthetic data
        if new_data is None:
            generator = FinancialDataGenerator()
            synthetic_data = generator.generate_training_data(2000)
            datasets.append(synthetic_data)
        else:
            datasets.append(new_data)
        
        # Add historical project data if available
        if include_historical:
            historical_data = self._extract_historical_data()
            if historical_data is not None and len(historical_data) > 0:
                datasets.append(historical_data)
                self.logger.info(f"Added {len(historical_data)} historical projects to training data")
        
        # Combine all datasets
        if len(datasets) > 1:
            combined_data = pd.concat(datasets, ignore_index=True)
        else:
            combined_data = datasets[0]
        
        self.logger.info(f"Total training samples: {len(combined_data)}")
        return combined_data
    
    def _extract_historical_data(self) -> Optional[pd.DataFrame]:
        """Extract historical project data from persistence service."""
        try:
            # Import persistence service
            from services.persistence_service import DataPersistenceService
            
            persistence = DataPersistenceService()
            projects = persistence.list_projects(limit=1000)
            
            historical_records = []
            
            for project_info in projects:
                try:
                    # Load full project data
                    project_data = persistence.load_project(project_info['id'])
                    
                    if project_data and project_data.financial_results:
                        # Extract project assumptions
                        assumptions = project_data.project_assumptions
                        results = project_data.financial_results
                        
                        # Get KPIs from results
                        kpis = results.get('kpis', {})
                        
                        if kpis:
                            # Map assumptions to feature columns
                            record = {
                                'capacity_mw': assumptions.get('capacity_mw', 10.0),
                                'capex_eur_kw': (assumptions.get('capex_meur', 8.5) * 1000) / assumptions.get('capacity_mw', 10.0),
                                'capacity_factor': assumptions.get('production_mwh_year1', 18000) / (assumptions.get('capacity_mw', 10.0) * 8760),
                                'ppa_price_eur_kwh': assumptions.get('ppa_price_eur_kwh', 0.045),
                                'debt_ratio': assumptions.get('debt_ratio', 0.75),
                                'interest_rate': assumptions.get('interest_rate', 0.06),
                                'discount_rate': assumptions.get('discount_rate', 0.08),
                                # Target variables from actual results
                                'irr_equity': kpis.get('IRR_equity', 0),
                                'npv_equity': kpis.get('NPV_equity', 0),
                                'lcoe': kpis.get('LCOE_eur_kwh', 0)
                            }
                            
                            # Validate record
                            if self._validate_historical_record(record):
                                historical_records.append(record)
                                
                except Exception as e:
                    self.logger.warning(f"Failed to process project {project_info['id']}: {e}")
                    continue
            
            if historical_records:
                return pd.DataFrame(historical_records)
            else:
                self.logger.info("No valid historical data found")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to extract historical data: {e}")
            return None
    
    def _validate_historical_record(self, record: Dict[str, Any]) -> bool:
        """Validate historical record for training."""
        required_fields = ['capacity_mw', 'capex_eur_kw', 'capacity_factor', 'ppa_price_eur_kwh', 
                          'debt_ratio', 'interest_rate', 'discount_rate', 'irr_equity', 'npv_equity', 'lcoe']
        
        # Check all required fields exist and are numeric
        for field in required_fields:
            if field not in record or not isinstance(record[field], (int, float)):
                return False
            if np.isnan(record[field]) or np.isinf(record[field]):
                return False
        
        # Check reasonable ranges
        if record['capacity_mw'] <= 0 or record['capacity_mw'] > 1000:
            return False
        if record['capacity_factor'] <= 0 or record['capacity_factor'] > 1:
            return False
        if record['ppa_price_eur_kwh'] <= 0 or record['ppa_price_eur_kwh'] > 1:
            return False
        if record['debt_ratio'] < 0 or record['debt_ratio'] > 1:
            return False
        if record['irr_equity'] < -1 or record['irr_equity'] > 5:  # -100% to 500%
            return False
        
        return True
    
    def _get_fallback_prediction(self, target: PredictionTarget) -> PredictionResult:
        """Get fallback prediction when models are not available."""
        fallback_values = {
            PredictionTarget.IRR_EQUITY: 0.12,
            PredictionTarget.NPV_EQUITY: 1000000,
            PredictionTarget.LCOE: 0.06
        }
        
        return PredictionResult(
            target=target,
            predicted_value=fallback_values.get(target, 0.1),
            confidence_interval=(0.08, 0.15),
            feature_contributions={},
            model_confidence=0.5,
            recommendations=["ML models not available - using industry averages"]
        )
    
    def _get_parameter_range(self, param: str, original_value: float) -> List[float]:
        """Get parameter range for optimization."""
        if original_value <= 0:
            original_value = {
                'capacity_mw': 10.0,
                'capex_eur_kw': 1200.0,
                'capacity_factor': 0.25,
                'ppa_price_eur_kwh': 0.05,
                'debt_ratio': 0.75,
                'interest_rate': 0.06,
                'discount_rate': 0.08
            }.get(param, 1.0)
        
        ranges = {
            'capacity_mw': np.linspace(original_value * 0.5, original_value * 2, 10),
            'ppa_price_eur_kwh': np.linspace(original_value * 0.8, original_value * 1.3, 10),
            'capex_eur_kw': np.linspace(original_value * 0.7, original_value * 1.3, 10),
            'capacity_factor': np.linspace(max(0.15, original_value * 0.8), min(0.4, original_value * 1.2), 10),
            'debt_ratio': np.linspace(0.5, 0.9, 10),
            'interest_rate': np.linspace(0.03, 0.1, 10),
            'discount_rate': np.linspace(0.06, 0.15, 10)
        }
        
        return ranges.get(param, [original_value])
    
    def _get_risk_recommendations(self, risk_factors: Dict[str, str]) -> List[str]:
        """Get recommendations based on risk assessment."""
        recommendations = []
        
        for factor, risk_level in risk_factors.items():
            if 'HIGH' in risk_level:
                if 'price' in factor:
                    recommendations.append("Consider long-term PPA with escalation clauses")
                elif 'technology' in factor:
                    recommendations.append("Evaluate higher efficiency PV modules")
                elif 'financial' in factor:
                    recommendations.append("Consider reducing debt ratio or finding cheaper financing")
                elif 'market' in factor:
                    recommendations.append("Review CAPEX assumptions and supplier quotes")
        
        if not recommendations:
            recommendations.append("Risk profile is acceptable for the project")
        
        return recommendations


# Global ML service instance
ml_prediction_service = MLPredictionService() if SKLEARN_AVAILABLE else None


# Convenience functions
def get_ml_service() -> Optional[MLPredictionService]:
    """Get global ML prediction service instance."""
    return ml_prediction_service


def predict_financial_metrics(assumptions: Dict[str, Any]) -> Dict[str, Any]:
    """Predict financial metrics using ML if available."""
    if ml_prediction_service:
        try:
            predictions = ml_prediction_service.predict_multiple(assumptions)
            return {
                target.value: {
                    'predicted_value': result.predicted_value,
                    'confidence_interval': result.confidence_interval,
                    'recommendations': result.recommendations
                }
                for target, result in predictions.items()
            }
        except Exception as e:
            logging.getLogger(__name__).error(f"ML prediction failed: {e}")
    
    # Fallback predictions
    return {
        'irr_equity': {
            'predicted_value': 0.12,
            'confidence_interval': (0.10, 0.14),
            'recommendations': ['ML predictions not available - using fallback values']
        },
        'npv_equity': {
            'predicted_value': 1000000,
            'confidence_interval': (800000, 1200000),
            'recommendations': ['Verify assumptions with detailed DCF model']
        },
        'lcoe': {
            'predicted_value': 0.06,
            'confidence_interval': (0.05, 0.07),
            'recommendations': ['Compare with regional LCOE benchmarks']
        }
    }


def add_performance_feedback(project_id: str, predicted_values: Dict[str, float], 
                           actual_values: Dict[str, float], project_assumptions: Dict[str, Any]):
    """Add performance feedback for real-time learning."""
    if ml_prediction_service:
        ml_prediction_service.add_performance_feedback(
            project_id, predicted_values, actual_values, project_assumptions
        )


def get_ml_accuracy_metrics() -> Dict[str, Any]:
    """Get ML model accuracy metrics."""
    if ml_prediction_service:
        return ml_prediction_service.get_prediction_accuracy_metrics()
    return {'status': 'ML service not available'}


def trigger_model_retraining():
    """Manually trigger model retraining."""
    if ml_prediction_service:
        ml_prediction_service.retrain_models(include_historical=True)
        return True
    return False
