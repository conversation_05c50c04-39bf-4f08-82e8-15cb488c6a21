# Hiel RnE Modeler v3.0

A comprehensive financial modeling application for renewable energy projects with advanced analysis capabilities.

## Author & Company Information

**Author:** <PERSON><PERSON><PERSON><PERSON>  
**Company:** Agevolami SRL  
**Website:** www.agevolami.it & www.agevolami.ma  
**Tagline:** Your way to explore crossborder opportunities and grow big  

## Features

### Core Functionality
- **Project Setup & Configuration**: Complete client profile and project parameter management
- **Financial Modeling**: Advanced DCF analysis with comprehensive KPI calculations
- **Location Comparison**: Multi-location analysis for optimal project placement
- **Validation & Benchmarking**: Industry benchmark comparison and model validation
- **Sensitivity Analysis**: Impact analysis of key variables on project returns
- **Monte Carlo Simulation**: Risk analysis through probabilistic modeling
- **Scenario Analysis**: Multiple scenario comparison (Base, Optimistic, Pessimistic)
- **Professional Reporting**: Export to Excel, DOCX, HTML, and JSON formats

### Enhanced Features
- **Security Screen**: Password-protected access with developer information
- **Comprehensive Analysis**: One-click generation of all analyses and reports
- **LCOE Impact Breakdown**: Detailed analysis of SIMEST program benefits (grant + financing), Moroccan grants, VAT reduction, and tax exemptions
- **SIMEST Split Structure Modeling**: Accurate representation of grant component (10-20%) and soft loan component (80-90%) with preferential 0.511% interest rate
- **Dual Debt Facility Support**: Advanced financial modeling supporting both commercial debt and SIMEST preferential financing simultaneously
- **African Market Focus**: Specialized support for Italian companies investing across all 54 African countries through SIMEST program
- **Professional Charts**: Interactive visualizations with separate representation of grant and soft loan components
- **Modular Architecture**: Clean separation of concerns for maintainability

## Installation

1. **Clone or download the application**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Running the Application
```bash
python src/new_app/main.py
```

### Security Access
- Default password: `agevolami2024`
- The security screen displays developer and company information

### Application Workflow
1. **Project Setup**: Enter client information and project parameters
2. **SIMEST Program Configuration**: 
   - Enter total SIMEST facility amount in unified field
   - Toggle Southern Italy company status (affects grant percentage: 10% vs 20%)
   - View real-time split calculation showing grant and soft loan components
   - Validate program compliance and eligibility requirements
3. **Run Analysis**: Use "Generate Complete Analysis & Reports" for comprehensive analysis
4. **Review Results**: Navigate through different tabs to review analysis results with SIMEST split structure
5. **Export Reports**: Generate professional reports showing separate grant and soft loan representation

### SIMEST Program Usage Examples

#### Standard Italian Company Example
- **Total SIMEST Facility**: €10M
- **Grant Component**: €1M (10% - non-repayable)
- **Soft Loan Component**: €9M (90% at 0.511% for 6 years, 2-year grace)
- **Financial Benefit**: ~€3.2M total program value vs. commercial financing

#### Southern Italy Company Example  
- **Total SIMEST Facility**: €10M
- **Grant Component**: €2M (20% - non-repayable)
- **Soft Loan Component**: €8M (80% at 0.511% for 6 years, 2-year grace)
- **Financial Benefit**: ~€4.2M total program value vs. commercial financing

#### African Market Focus
- **Eligible Locations**: All 54 African countries supported
- **Project Types**: Productive investments, digitalization, sustainability, training, innovation
- **Company Requirements**: Italian legal entity, €500K+ turnover, export capability
- **Strategic Impact**: Enhanced competitiveness in African markets through preferential financing

## Application Structure

```
src/new_app/
├── app/                    # Application core
│   ├── app_controller.py   # Main application controller
│   └── app_state.py        # Centralized state management
├── models/                 # Data models
│   ├── client_profile.py   # Client information model
│   ├── project_assumptions.py # Project parameters model
│   ├── location_config.py  # Location configuration
│   └── ui_state.py         # UI state management
├── services/               # Business logic services
│   ├── financial_service.py # Financial modeling service
│   ├── validation_service.py # Model validation service
│   ├── export_service.py   # Export functionality
│   ├── location_service.py # Location comparison service
│   └── report_service.py   # Report generation service
├── views/                  # UI view components
│   ├── base_view.py        # Base view class
│   ├── project_setup_view.py # Project setup interface
│   ├── dashboard_view.py   # Main dashboard
│   ├── location_comparison_view.py # Location comparison
│   ├── financial_model_view.py # Financial results
│   ├── validation_view.py  # Validation results
│   ├── sensitivity_view.py # Sensitivity analysis
│   ├── monte_carlo_view.py # Monte Carlo simulation
│   ├── scenarios_view.py   # Scenario analysis
│   └── export_view.py      # Export interface
├── components/             # Reusable UI components
│   ├── charts/             # Chart components
│   ├── forms/              # Form components
│   └── widgets/            # UI widgets
├── config/                 # Configuration
│   ├── app_config.py       # Application configuration
│   ├── ui_config.py        # UI configuration
│   └── export_config.py    # Export configuration
├── utils/                  # Utility functions
│   ├── file_utils.py       # File operations
│   ├── validation_utils.py # Data validation
│   └── formatting_utils.py # Data formatting
└── main.py                 # Application entry point
```

## Key Features Detail

### Financial Analysis
- **IRR Calculation**: Project and equity IRR with industry benchmarks
- **NPV Analysis**: Net present value calculations
- **LCOE Calculation**: Levelized cost of energy
- **DSCR Analysis**: Debt service coverage ratio timeline
- **Payback Period**: Investment recovery analysis

### Grant Analysis & Development Finance
- **SIMEST "Strengthening African Markets" Program**: €200M Italian government-backed initiative combining grant (10-20%) and preferential soft loan (80-90% at 0.511%) components for Italian companies investing across all African countries
- **MASEN Strategic Grants**: Moroccan renewable energy grants
- **Grid Connection Grants**: Infrastructure support grants
- **Dual Debt Facility Support**: Advanced modeling of regular commercial debt alongside SIMEST preferential financing
- **Impact Analysis**: Comprehensive breakdown of grant and financing benefits impact on LCOE, IRR, and NPV
- **Program Compliance Validation**: Automated validation against SIMEST eligibility requirements and program parameters
- **Migration Support**: Seamless migration from legacy dual-field approach to unified SIMEST program structure

### Location Comparison
- **Multi-location Analysis**: Compare up to 9 Moroccan locations
- **Ranking System**: Automatic ranking by different criteria
- **Risk Assessment**: Location-specific risk analysis
- **Recommendations**: AI-powered location recommendations

### Export Capabilities
- **Excel Reports**: Comprehensive financial models with charts
- **DOCX Reports**: Professional documents with embedded charts
- **HTML Reports**: Web-ready reports with responsive design
- **JSON Data**: Raw data export for integration

## Technical Requirements

- **Python**: 3.8 or higher
- **Flet**: 0.21.0 or higher
- **Pandas**: For data processing
- **Matplotlib**: For chart generation
- **OpenPyXL**: For Excel export
- **Python-DOCX**: For Word document export

## Configuration

The application uses configuration files for customization:
- `app_config.py`: General application settings
- `ui_config.py`: User interface configuration
- `export_config.py`: Export format settings
- `simest_config.json`: SIMEST program parameters including grant percentages, soft loan terms, eligible countries, and facility limits

### SIMEST Program Configuration
The SIMEST configuration file (`config/simest_config.json`) contains official program parameters:
- **Grant Percentages**: 10% standard, 20% for Southern Italy companies
- **Soft Loan Terms**: 0.511% interest rate, 6-year tenor, 2-year grace period
- **Eligible Countries**: Comprehensive list of all 54 African countries
- **Facility Limits**: €0.1M minimum, €50M maximum per project
- **Eligibility Criteria**: Italian company requirements and compliance parameters

### Configuration Management
- **Centralized Parameters**: All SIMEST program parameters managed through configuration
- **Validation**: Automatic validation of configuration integrity on application startup
- **Updates**: Easy parameter updates through configuration file modification
- **Compliance**: Ensures all calculations align with official SIMEST program terms

## Migration from Legacy Implementation

### Automatic Data Migration
The application includes comprehensive migration support for projects created with the previous dual-field approach:

#### Legacy Field Consolidation
- **Previous Approach**: Separate "Italian Grant (Piano Mattei)" and "SIMEST African Fund" fields
- **Current Approach**: Single "SIMEST - Piano Mattei Program" field with automatic split calculation
- **Migration Process**: Automatic consolidation of legacy data into unified structure
- **Data Preservation**: All historical project data preserved with proper split calculations

#### Migration Features
- **Backward Compatibility**: Existing projects continue to work seamlessly
- **Data Validation**: Migrated data validated against current SIMEST program parameters
- **Report Updates**: All exported reports automatically reflect new split structure
- **Audit Trail**: Complete migration history maintained for compliance

#### Troubleshooting Migration Issues
- **Data Inconsistencies**: Automatic resolution of conflicting legacy field values
- **Validation Errors**: Clear guidance for resolving post-migration validation issues
- **Report Discrepancies**: Support for updating historical reports to new format
- **Documentation**: Comprehensive migration guide available in `/docs/guides/SIMEST_PROGRAM_GUIDE.md`

## Support & Contact

For support, customization, or business inquiries:

**Abdelhalim Serhani**
Business Consultant and Analyst
Agevolami SRL
Email: Available through www.agevolami.it
Websites: www.agevolami.it & www.agevolami.ma

### SIMEST Program Support
- **Technical Issues**: Application support for SIMEST program features
- **Program Eligibility**: Guidance on SIMEST program requirements and compliance
- **Migration Support**: Assistance with legacy data migration and validation
- **Documentation**: Comprehensive SIMEST program guide and technical documentation

*"Your gateway to cross-border opportunities and exponential growth"*

## License

This application is proprietary software developed by Agevolami SRL.
All rights reserved.

## Version History

- **v4.0.0**: SIMEST Program Integration - Unified SIMEST "Strengthening African Markets" program implementation with split structure modeling, dual debt facility support, and comprehensive migration from legacy dual-field approach
- **v3.0.0**: Enhanced financial modeling and analysis capabilities
- **v2.0.0**: Complete modular refactor with enhanced features
- **v1.0.0**: Initial monolithic version
